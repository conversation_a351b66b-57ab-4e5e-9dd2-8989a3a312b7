use rust_fsm::*;
use serde::{Deserialize, Serialize};
use std::fmt;
use tracing::{info, warn, error};
use chrono::{DateTime, Utc};

// Deployment State Machine
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum DeploymentState {
    Created,
    Queued,
    Building,
    Testing,
    Deploying,
    Running,
    Failed,
    Cancelled,
    RolledBack,
}

#[derive(Clone, Debug, PartialEq)]
pub enum DeploymentEvent {
    Queue,
    StartBuild,
    BuildComplete,
    BuildFailed,
    StartTest,
    TestComplete,
    TestFailed,
    StartDeploy,
    DeployComplete,
    DeployFailed,
    Cancel,
    Rollback,
    Retry,
}

state_machine! {
    derive(Clone, Debug, PartialEq, Serialize, Deserialize)
    DeploymentStateMachine(DeploymentState)

    Created => {
        Queue => Queued,
        Cancel => Cancelled,
    },

    Queued => {
        StartBuild => Building,
        Cancel => Cancelled,
    },

    Building => {
        BuildComplete => Testing,
        BuildFailed => Failed,
        Cancel => Cancelled,
    },

    Testing => {
        TestComplete => Deploying,
        TestFailed => Failed,
        Cancel => Cancelled,
    },

    Deploying => {
        DeployComplete => Running,
        DeployFailed => Failed,
        Cancel => Cancelled,
    },

    Running => {
        Rollback => RolledBack,
        StartBuild => Building, // For redeployment
    },

    Failed => {
        Retry => Queued,
        Cancel => Cancelled,
    },

    Cancelled => {},
    RolledBack => {
        StartBuild => Building, // For new deployment
    },
}

// Application State Machine
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum ApplicationState {
    Creating,
    Active,
    Suspended,
    Deleting,
    Deleted,
    Error,
}

#[derive(Clone, Debug, PartialEq)]
pub enum ApplicationEvent {
    Created,
    Suspend,
    Resume,
    Delete,
    Error,
    Recover,
}

state_machine! {
    derive(Clone, Debug, PartialEq, Serialize, Deserialize)
    ApplicationStateMachine(ApplicationState)

    Creating => {
        Created => Active,
        Error => Error,
    },

    Active => {
        Suspend => Suspended,
        Delete => Deleting,
        Error => Error,
    },

    Suspended => {
        Resume => Active,
        Delete => Deleting,
        Error => Error,
    },

    Deleting => {
        Delete => Deleted,
        Error => Error,
    },

    Error => {
        Recover => Active,
        Delete => Deleting,
    },

    Deleted => {},
}

// Build State Machine
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum BuildState {
    Queued,
    Running,
    Success,
    Failed,
    Cancelled,
    Timeout,
}

#[derive(Clone, Debug, PartialEq)]
pub enum BuildEvent {
    Start,
    Complete,
    Fail,
    Cancel,
    Timeout,
    Retry,
}

state_machine! {
    derive(Clone, Debug, PartialEq, Serialize, Deserialize)
    BuildStateMachine(BuildState)

    Queued => {
        Start => Running,
        Cancel => Cancelled,
        Timeout => Timeout,
    },

    Running => {
        Complete => Success,
        Fail => Failed,
        Cancel => Cancelled,
        Timeout => Timeout,
    },

    Success => {},

    Failed => {
        Retry => Queued,
    },

    Cancelled => {},
    Timeout => {
        Retry => Queued,
    },
}

// State transition tracking
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct StateTransition<S, E> {
    pub from_state: S,
    pub to_state: S,
    pub event: E,
    pub timestamp: DateTime<Utc>,
    pub metadata: Option<serde_json::Value>,
}

pub trait StateMachineManager<S, E> 
where 
    S: Clone + fmt::Debug + PartialEq + Serialize,
    E: Clone + fmt::Debug + PartialEq,
{
    fn current_state(&self) -> &S;
    fn transition(&mut self, event: E) -> Result<StateTransition<S, E>, StateMachineError>;
    fn can_transition(&self, event: &E) -> bool;
    fn get_history(&self) -> &[StateTransition<S, E>];
}

#[derive(Debug, Clone)]
pub struct StateMachineError {
    pub message: String,
    pub current_state: String,
    pub attempted_event: String,
}

impl fmt::Display for StateMachineError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "State machine error: {} (current state: {}, attempted event: {})",
            self.message, self.current_state, self.attempted_event
        )
    }
}

impl std::error::Error for StateMachineError {}

// Deployment State Manager
pub struct DeploymentStateManager {
    state_machine: DeploymentStateMachine,
    history: Vec<StateTransition<DeploymentState, DeploymentEvent>>,
}

impl DeploymentStateManager {
    pub fn new() -> Self {
        Self {
            state_machine: DeploymentStateMachine::new(DeploymentState::Created),
            history: Vec::new(),
        }
    }

    pub fn from_state(state: DeploymentState) -> Self {
        Self {
            state_machine: DeploymentStateMachine::new(state),
            history: Vec::new(),
        }
    }
}

impl StateMachineManager<DeploymentState, DeploymentEvent> for DeploymentStateManager {
    fn current_state(&self) -> &DeploymentState {
        self.state_machine.state()
    }

    fn transition(&mut self, event: DeploymentEvent) -> Result<StateTransition<DeploymentState, DeploymentEvent>, StateMachineError> {
        let from_state = self.state_machine.state().clone();
        
        match self.state_machine.consume(&event) {
            Ok(new_machine) => {
                let to_state = new_machine.state().clone();
                self.state_machine = new_machine;
                
                let transition = StateTransition {
                    from_state: from_state.clone(),
                    to_state: to_state.clone(),
                    event: event.clone(),
                    timestamp: Utc::now(),
                    metadata: None,
                };
                
                self.history.push(transition.clone());
                
                info!(
                    "Deployment state transition: {:?} -> {:?} (event: {:?})",
                    from_state, to_state, event
                );
                
                Ok(transition)
            }
            Err(_) => {
                let error = StateMachineError {
                    message: "Invalid state transition".to_string(),
                    current_state: format!("{:?}", from_state),
                    attempted_event: format!("{:?}", event),
                };
                
                warn!(
                    "Invalid deployment state transition: {:?} -> {:?}",
                    from_state, event
                );
                
                Err(error)
            }
        }
    }

    fn can_transition(&self, event: &DeploymentEvent) -> bool {
        self.state_machine.consume(event).is_ok()
    }

    fn get_history(&self) -> &[StateTransition<DeploymentState, DeploymentEvent>] {
        &self.history
    }
}

// Application State Manager
pub struct ApplicationStateManager {
    state_machine: ApplicationStateMachine,
    history: Vec<StateTransition<ApplicationState, ApplicationEvent>>,
}

impl ApplicationStateManager {
    pub fn new() -> Self {
        Self {
            state_machine: ApplicationStateMachine::new(ApplicationState::Creating),
            history: Vec::new(),
        }
    }

    pub fn from_state(state: ApplicationState) -> Self {
        Self {
            state_machine: ApplicationStateMachine::new(state),
            history: Vec::new(),
        }
    }
}

impl StateMachineManager<ApplicationState, ApplicationEvent> for ApplicationStateManager {
    fn current_state(&self) -> &ApplicationState {
        self.state_machine.state()
    }

    fn transition(&mut self, event: ApplicationEvent) -> Result<StateTransition<ApplicationState, ApplicationEvent>, StateMachineError> {
        let from_state = self.state_machine.state().clone();
        
        match self.state_machine.consume(&event) {
            Ok(new_machine) => {
                let to_state = new_machine.state().clone();
                self.state_machine = new_machine;
                
                let transition = StateTransition {
                    from_state: from_state.clone(),
                    to_state: to_state.clone(),
                    event: event.clone(),
                    timestamp: Utc::now(),
                    metadata: None,
                };
                
                self.history.push(transition.clone());
                
                info!(
                    "Application state transition: {:?} -> {:?} (event: {:?})",
                    from_state, to_state, event
                );
                
                Ok(transition)
            }
            Err(_) => {
                let error = StateMachineError {
                    message: "Invalid state transition".to_string(),
                    current_state: format!("{:?}", from_state),
                    attempted_event: format!("{:?}", event),
                };
                
                warn!(
                    "Invalid application state transition: {:?} -> {:?}",
                    from_state, event
                );
                
                Err(error)
            }
        }
    }

    fn can_transition(&self, event: &ApplicationEvent) -> bool {
        self.state_machine.consume(event).is_ok()
    }

    fn get_history(&self) -> &[StateTransition<ApplicationState, ApplicationEvent>] {
        &self.history
    }
}
