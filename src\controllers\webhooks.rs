use crate::{
    controllers::{<PERSON><PERSON><PERSON><PERSON>, ControllerResult},
    models::{ApiResponse, GitWebhookPayload, DeploymentResponse},
    services::{GitService, DeploymentService},
    state::AppState,
};
use axum::{
    extract::{Path, State},
    http::{HeaderMap, StatusCode},
    response::Json,
    body::Bytes,
};
use std::sync::Arc;
use tracing::{error, info, instrument, warn};

#[instrument(skip(state, headers, body))]
pub async fn handle_github_webhook(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
    body: Bytes,
) -> ControllerResult<Json<ApiResponse<Vec<String>>>> {
    // Verify GitHub webhook signature
    let signature = headers
        .get("x-hub-signature-256")
        .and_then(|h| h.to_str().ok())
        .ok_or_else(|| ControllerError::Validation("Missing GitHub signature".to_string()))?;

    let event_type = headers
        .get("x-github-event")
        .and_then(|h| h.to_str().ok())
        .ok_or_else(|| ControllerError::Validation("Missing GitHub event type".to_string()))?;

    // Only process push events for now
    if event_type != "push" {
        info!("Ignoring GitHub event type: {}", event_type);
        return Ok(Json(ApiResponse::success(vec!["ignored".to_string()])));
    }

    let git_service = GitService::new(&state.database, &state.config);
    
    // Parse webhook payload
    let payload: GitWebhookPayload = serde_json::from_slice(&body)
        .map_err(|e| ControllerError::Validation(format!("Invalid webhook payload: {}", e)))?;

    // Find applications that match this repository and have webhooks enabled
    let triggered_deployments = git_service
        .process_webhook(payload)
        .await
        .map_err(|e| ControllerError::Service(e.to_string()))?;

    let deployment_ids: Vec<String> = triggered_deployments
        .iter()
        .map(|id| id.to_hex())
        .collect();

    info!("GitHub webhook processed, triggered {} deployments", deployment_ids.len());

    Ok(Json(ApiResponse::success(deployment_ids)))
}

#[instrument(skip(state, headers, body))]
pub async fn handle_gitlab_webhook(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
    body: Bytes,
) -> ControllerResult<Json<ApiResponse<Vec<String>>>> {
    // Verify GitLab webhook token
    let token = headers
        .get("x-gitlab-token")
        .and_then(|h| h.to_str().ok())
        .ok_or_else(|| ControllerError::Validation("Missing GitLab token".to_string()))?;

    let event_type = headers
        .get("x-gitlab-event")
        .and_then(|h| h.to_str().ok())
        .ok_or_else(|| ControllerError::Validation("Missing GitLab event type".to_string()))?;

    // Only process push events for now
    if event_type != "Push Hook" {
        info!("Ignoring GitLab event type: {}", event_type);
        return Ok(Json(ApiResponse::success(vec!["ignored".to_string()])));
    }

    // Parse GitLab webhook payload (similar structure to GitHub)
    let payload: GitWebhookPayload = serde_json::from_slice(&body)
        .map_err(|e| ControllerError::Validation(format!("Invalid webhook payload: {}", e)))?;

    let git_service = GitService::new(&state.database, &state.config);
    
    let triggered_deployments = git_service
        .process_webhook(payload)
        .await
        .map_err(|e| ControllerError::Service(e.to_string()))?;

    let deployment_ids: Vec<String> = triggered_deployments
        .iter()
        .map(|id| id.to_hex())
        .collect();

    info!("GitLab webhook processed, triggered {} deployments", deployment_ids.len());

    Ok(Json(ApiResponse::success(deployment_ids)))
}

#[instrument(skip(state, app_id, headers, body))]
pub async fn handle_application_webhook(
    State(state): State<Arc<AppState>>,
    Path(app_id): Path<String>,
    headers: HeaderMap,
    body: Bytes,
) -> ControllerResult<Json<ApiResponse<DeploymentResponse>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    // Determine webhook source based on headers
    let webhook_source = if headers.contains_key("x-github-event") {
        "github"
    } else if headers.contains_key("x-gitlab-event") {
        "gitlab"
    } else if headers.contains_key("x-bitbucket-event") {
        "bitbucket"
    } else {
        return Err(ControllerError::Validation("Unknown webhook source".to_string()));
    };

    let git_service = GitService::new(&state.database, &state.config);
    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);

    // Get application to verify webhook secret
    let application = deployment_service
        .get_application_by_id(&app_id)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Application not found".to_string()),
            _ => ControllerError::Service(e.to_string()),
        })?;

    // Verify webhook signature if secret is configured
    if let Some(webhook_secret) = &application.repository.webhook_secret {
        let signature = match webhook_source {
            "github" => headers.get("x-hub-signature-256"),
            "gitlab" => headers.get("x-gitlab-token"),
            "bitbucket" => headers.get("x-hub-signature"),
            _ => None,
        }
        .and_then(|h| h.to_str().ok())
        .ok_or_else(|| ControllerError::Validation("Missing webhook signature".to_string()))?;

        let is_valid = git_service
            .verify_webhook_signature(&body, signature, webhook_secret)
            .map_err(|e| ControllerError::Service(e.to_string()))?;

        if !is_valid {
            warn!("Invalid webhook signature for application {}", app_id);
            return Err(ControllerError::Unauthorized("Invalid webhook signature".to_string()));
        }
    }

    // Parse webhook payload
    let payload: GitWebhookPayload = serde_json::from_slice(&body)
        .map_err(|e| ControllerError::Validation(format!("Invalid webhook payload: {}", e)))?;

    // Check if this is for the correct branch
    let target_branch = format!("refs/heads/{}", application.repository.branch);
    if payload.ref_name != target_branch {
        info!(
            "Ignoring webhook for branch {} (expected {})",
            payload.ref_name, target_branch
        );
        return Err(ControllerError::Validation("Branch mismatch".to_string()));
    }

    // Trigger deployment
    let deployment_request = crate::models::TriggerDeploymentRequest {
        branch: Some(application.repository.branch.clone()),
        commit_sha: Some(payload.after.clone()),
        force_rebuild: false,
    };

    let deployment = deployment_service
        .trigger_deployment_by_webhook(&app_id, deployment_request, payload)
        .await
        .map_err(|e| ControllerError::Service(e.to_string()))?;

    info!("Webhook triggered deployment {} for application {}", deployment.id, app_id);

    Ok(Json(ApiResponse::success(deployment)))
}

#[instrument(skip(state))]
pub async fn webhook_health_check(
    State(_state): State<Arc<AppState>>,
) -> ControllerResult<Json<ApiResponse<String>>> {
    Ok(Json(ApiResponse::success("Webhook service is healthy".to_string())))
}

#[instrument(skip(state, headers, body))]
pub async fn handle_generic_webhook(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
    body: Bytes,
) -> ControllerResult<Json<ApiResponse<String>>> {
    // Log webhook details for debugging
    let content_type = headers
        .get("content-type")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("unknown");

    let user_agent = headers
        .get("user-agent")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("unknown");

    info!(
        "Received generic webhook: content_type={}, user_agent={}, body_size={}",
        content_type,
        user_agent,
        body.len()
    );

    // Try to determine the webhook source
    let source = if headers.contains_key("x-github-event") {
        "GitHub"
    } else if headers.contains_key("x-gitlab-event") {
        "GitLab"
    } else if headers.contains_key("x-bitbucket-event") {
        "Bitbucket"
    } else if user_agent.contains("GitHub") {
        "GitHub (detected)"
    } else if user_agent.contains("GitLab") {
        "GitLab (detected)"
    } else {
        "Unknown"
    };

    let response_message = format!(
        "Webhook received from {} but no specific handler configured. Use application-specific webhook URLs.",
        source
    );

    Ok(Json(ApiResponse::success(response_message)))
}

// Additional methods for DeploymentService
impl<'a> DeploymentService<'a> {
    pub async fn get_application_by_id(&self, app_id: &str) -> crate::services::ServiceResult<crate::models::Application> {
        use bson::{doc, oid::ObjectId};
        
        let app_object_id = ObjectId::parse_str(app_id)
            .map_err(|_| crate::services::ServiceError::Validation("Invalid application ID".to_string()))?;

        let application = self.applications
            .find_one(doc! { "_id": app_object_id }, None)
            .await
            .map_err(|e| crate::services::ServiceError::Database(e))?
            .ok_or_else(|| crate::services::ServiceError::NotFound("Application not found".to_string()))?;

        Ok(application)
    }

    pub async fn trigger_deployment_by_webhook(
        &self,
        app_id: &str,
        request: crate::models::TriggerDeploymentRequest,
        payload: GitWebhookPayload,
    ) -> crate::services::ServiceResult<DeploymentResponse> {
        use bson::oid::ObjectId;
        use chrono::Utc;

        let app_object_id = ObjectId::parse_str(app_id)
            .map_err(|_| crate::services::ServiceError::Validation("Invalid application ID".to_string()))?;

        // Create deployment record with webhook trigger
        let deployment = crate::models::Deployment {
            id: None,
            application_id: app_object_id,
            version: format!("v{}", Utc::now().timestamp()),
            commit_sha: payload.after.clone(),
            commit_message: payload.commits.first().map(|c| c.message.clone()),
            branch: request.branch.unwrap_or_else(|| "main".to_string()),
            status: crate::models::DeploymentStatus::Queued,
            trigger: crate::models::DeploymentTrigger::Webhook { 
                source: "git".to_string() 
            },
            build_logs: Vec::new(),
            runtime_logs: Vec::new(),
            metrics: crate::models::DeploymentMetrics {
                build_duration: None,
                deploy_duration: None,
                instances_count: 1,
                cpu_usage: 0.0,
                memory_usage: 0.0,
                request_count: 0,
                error_rate: 0.0,
                response_time_p95: 0.0,
            },
            created_at: Utc::now(),
            started_at: None,
            completed_at: None,
            failed_at: None,
        };

        let result = self.deployments
            .insert_one(&deployment, None)
            .await
            .map_err(|e| crate::services::ServiceError::Database(e))?;

        let deployment_id = result.inserted_id.as_object_id()
            .ok_or_else(|| crate::services::ServiceError::Internal("Failed to get inserted deployment ID".to_string()))?;

        // Start deployment process (similar to manual trigger)
        let application = self.get_application_by_id(app_id).await?;
        self.execute_deployment(deployment_id, &application).await?;

        Ok(DeploymentResponse {
            id: deployment_id.to_hex(),
            application_id: app_id.to_string(),
            version: deployment.version,
            commit_sha: deployment.commit_sha,
            commit_message: deployment.commit_message,
            branch: deployment.branch,
            status: deployment.status,
            trigger: deployment.trigger,
            metrics: deployment.metrics,
            created_at: deployment.created_at,
            started_at: deployment.started_at,
            completed_at: deployment.completed_at,
            failed_at: deployment.failed_at,
        })
    }
}
