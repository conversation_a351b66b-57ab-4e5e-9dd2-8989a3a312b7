use governor::{<PERSON>uo<PERSON>, RateLimiter, DefaultDirectRateLimiter, clock::Default<PERSON><PERSON>, state::InMemoryState};
use std::num::NonZeroU32;
use std::time::{Duration, Instant};
use dashmap::DashMap;
use std::sync::Arc;
use std::collections::VecDeque;
use tracing::{warn, info, error};
use crate::services::ServiceError;
use tokio::sync::Semaphore;
use rand::Rng;

/// Production-ready rate limiter with sliding window, jitter, and back pressure
#[derive(Clone)]
pub struct RateLimiterService {
    limiters: Arc<DashMap<String, DefaultDirectRateLimiter>>,
    sliding_windows: Arc<DashMap<String, SlidingWindow>>,
    api_throttle: Arc<ApiThrottle>,
    back_pressure: Arc<BackPressureManager>,
}

/// Sliding window rate limiter for more accurate rate limiting
#[derive(Debug)]
struct SlidingWindow {
    requests: VecDeque<Instant>,
    window_size: Duration,
    max_requests: usize,
}

/// API throttling and batching for external services
#[derive(Debug)]
pub struct ApiThrottle {
    vultr_semaphore: Semaphore,
    github_semaphore: Semaphore,
    batch_queues: DashMap<String, VecDeque<BatchRequest>>,
    last_batch_time: DashMap<String, Instant>,
}

/// Back pressure management to prevent system overload
#[derive(Debug)]
pub struct BackPressureManager {
    queue_depths: DashMap<String, usize>,
    max_queue_depth: usize,
    pressure_threshold: f64,
}

#[derive(Debug, Clone)]
pub struct BatchRequest {
    pub id: String,
    pub payload: serde_json::Value,
    pub timestamp: Instant,
}

impl SlidingWindow {
    fn new(window_size: Duration, max_requests: usize) -> Self {
        Self {
            requests: VecDeque::new(),
            window_size,
            max_requests,
        }
    }

    fn is_allowed(&mut self) -> bool {
        let now = Instant::now();

        // Remove old requests outside the window
        while let Some(&front_time) = self.requests.front() {
            if now.duration_since(front_time) > self.window_size {
                self.requests.pop_front();
            } else {
                break;
            }
        }

        // Check if we can add a new request
        if self.requests.len() < self.max_requests {
            self.requests.push_back(now);
            true
        } else {
            false
        }
    }

    fn remaining_capacity(&self) -> usize {
        self.max_requests.saturating_sub(self.requests.len())
    }
}

impl ApiThrottle {
    fn new() -> Self {
        Self {
            vultr_semaphore: Semaphore::new(5), // 5 concurrent Vultr API calls
            github_semaphore: Semaphore::new(10), // 10 concurrent GitHub API calls
            batch_queues: DashMap::new(),
            last_batch_time: DashMap::new(),
        }
    }

    /// Throttle API calls with semaphore-based concurrency control
    pub async fn throttle_vultr_call<F, T>(&self, operation: F) -> Result<T, ServiceError>
    where
        F: std::future::Future<Output = Result<T, ServiceError>>,
    {
        let _permit = self.vultr_semaphore.acquire().await
            .map_err(|_| ServiceError::Internal("Failed to acquire Vultr API permit".to_string()))?;

        operation.await
    }

    /// Batch API requests to reduce rate limiting
    pub async fn batch_request(&self, service: &str, request: BatchRequest) -> Result<(), ServiceError> {
        let batch_interval = Duration::from_millis(100); // Batch every 100ms

        // Add to batch queue
        self.batch_queues.entry(service.to_string())
            .or_insert_with(VecDeque::new)
            .push_back(request);

        // Check if we should process the batch
        let should_process = {
            let last_batch = self.last_batch_time.get(service)
                .map(|time| time.elapsed() > batch_interval)
                .unwrap_or(true);

            let queue_size = self.batch_queues.get(service)
                .map(|queue| queue.len())
                .unwrap_or(0);

            last_batch || queue_size >= 10 // Process if 100ms passed or 10 requests queued
        };

        if should_process {
            self.process_batch(service).await?;
        }

        Ok(())
    }

    async fn process_batch(&self, service: &str) -> Result<(), ServiceError> {
        let requests = {
            let mut queue = self.batch_queues.entry(service.to_string())
                .or_insert_with(VecDeque::new);
            let batch: Vec<_> = queue.drain(..).collect();
            batch
        };

        if requests.is_empty() {
            return Ok(());
        }

        self.last_batch_time.insert(service.to_string(), Instant::now());

        // Process batch based on service
        match service {
            "vultr" => self.process_vultr_batch(requests).await,
            "github" => self.process_github_batch(requests).await,
            _ => {
                warn!("Unknown service for batching: {}", service);
                Ok(())
            }
        }
    }

    async fn process_vultr_batch(&self, requests: Vec<BatchRequest>) -> Result<(), ServiceError> {
        info!("Processing Vultr batch with {} requests", requests.len());

        // Group requests by type and process efficiently
        // This would contain actual Vultr API batching logic
        for chunk in requests.chunks(5) {
            // Process 5 requests at a time to respect rate limits
            let _permit = self.vultr_semaphore.acquire().await
                .map_err(|_| ServiceError::Internal("Failed to acquire Vultr permit".to_string()))?;

            // Process chunk
            tokio::time::sleep(Duration::from_millis(200)).await; // Rate limiting
        }

        Ok(())
    }

    async fn process_github_batch(&self, requests: Vec<BatchRequest>) -> Result<(), ServiceError> {
        info!("Processing GitHub batch with {} requests", requests.len());

        // GitHub API batching logic
        for chunk in requests.chunks(10) {
            let _permit = self.github_semaphore.acquire().await
                .map_err(|_| ServiceError::Internal("Failed to acquire GitHub permit".to_string()))?;

            // Process chunk
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        Ok(())
    }
}

impl BackPressureManager {
    fn new() -> Self {
        Self {
            queue_depths: DashMap::new(),
            max_queue_depth: 1000,
            pressure_threshold: 0.8, // 80% capacity
        }
    }

    /// Check if system can accept more load
    pub fn can_accept_load(&self, queue_name: &str) -> bool {
        let current_depth = self.queue_depths.get(queue_name)
            .map(|depth| *depth)
            .unwrap_or(0);

        let pressure = current_depth as f64 / self.max_queue_depth as f64;
        pressure < self.pressure_threshold
    }

    /// Increment queue depth
    pub fn increment_queue(&self, queue_name: &str) -> Result<(), ServiceError> {
        let mut depth = self.queue_depths.entry(queue_name.to_string()).or_insert(0);

        if *depth >= self.max_queue_depth {
            return Err(ServiceError::ExternalApi(format!(
                "Queue {} is at maximum capacity", queue_name
            )));
        }

        *depth += 1;

        // Log warning if approaching capacity
        let pressure = *depth as f64 / self.max_queue_depth as f64;
        if pressure > 0.9 {
            warn!("Queue {} is at {}% capacity", queue_name, (pressure * 100.0) as u32);
        }

        Ok(())
    }

    /// Decrement queue depth
    pub fn decrement_queue(&self, queue_name: &str) {
        if let Some(mut depth) = self.queue_depths.get_mut(queue_name) {
            *depth = depth.saturating_sub(1);
        }
    }

    /// Get current queue depth
    pub fn get_queue_depth(&self, queue_name: &str) -> usize {
        self.queue_depths.get(queue_name).map(|d| *d).unwrap_or(0)
    }
}

impl RateLimiterService {
    pub fn new() -> Self {
        Self {
            limiters: Arc::new(DashMap::new()),
            sliding_windows: Arc::new(DashMap::new()),
            api_throttle: Arc::new(ApiThrottle::new()),
            back_pressure: Arc::new(BackPressureManager::new()),
        }
    }

    pub fn get_or_create_limiter(&self, key: &str, quota: Quota) -> DefaultDirectRateLimiter {
        self.limiters
            .entry(key.to_string())
            .or_insert_with(|| RateLimiter::direct(quota))
            .clone()
    }

    /// Check if request is allowed using sliding window rate limiting
    pub async fn check_api_rate_limit(&self, user_id: &str) -> Result<(), ServiceError> {
        // Use sliding window for more accurate rate limiting
        let window_key = format!("api_window:{}", user_id);
        let mut window = self.sliding_windows
            .entry(window_key.clone())
            .or_insert_with(|| SlidingWindow::new(Duration::from_secs(60), 100));

        if window.is_allowed() {
            Ok(())
        } else {
            warn!("Sliding window rate limit exceeded for user: {}", user_id);
            Err(ServiceError::ExternalApi("Rate limit exceeded".to_string()))
        }
    }

    /// Check with back pressure awareness
    pub async fn check_api_rate_limit_with_backpressure(&self, user_id: &str) -> Result<(), ServiceError> {
        // Check back pressure first
        if !self.back_pressure.can_accept_load("api_requests") {
            return Err(ServiceError::ExternalApi("System under high load, please retry later".to_string()));
        }

        self.back_pressure.increment_queue("api_requests")?;

        let result = self.check_api_rate_limit(user_id).await;

        self.back_pressure.decrement_queue("api_requests");
        result
    }

    /// Check if deployment is allowed
    pub async fn check_deployment_rate_limit(&self, user_id: &str) -> Result<(), ServiceError> {
        let quota = Quota::per_hour(NonZeroU32::new(10).unwrap()); // 10 deployments per hour
        let limiter = self.get_or_create_limiter(&format!("deploy:{}", user_id), quota);

        match limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => {
                warn!("Deployment rate limit exceeded for user: {}", user_id);
                Err(ServiceError::ExternalApi("Deployment rate limit exceeded".to_string()))
            }
        }
    }

    /// Check if build is allowed
    pub async fn check_build_rate_limit(&self, user_id: &str) -> Result<(), ServiceError> {
        let quota = Quota::per_hour(NonZeroU32::new(20).unwrap()); // 20 builds per hour
        let limiter = self.get_or_create_limiter(&format!("build:{}", user_id), quota);

        match limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => {
                warn!("Build rate limit exceeded for user: {}", user_id);
                Err(ServiceError::ExternalApi("Build rate limit exceeded".to_string()))
            }
        }
    }

    /// Check if external API call is allowed
    pub async fn check_external_api_rate_limit(&self, service: &str) -> Result<(), ServiceError> {
        let quota = match service {
            "vultr" => Quota::per_second(NonZeroU32::new(5).unwrap()), // 5 calls per second
            "github" => Quota::per_hour(NonZeroU32::new(5000).unwrap()), // GitHub API limit
            "gitlab" => Quota::per_minute(NonZeroU32::new(300).unwrap()), // GitLab API limit
            _ => Quota::per_second(NonZeroU32::new(10).unwrap()), // Default
        };

        let limiter = self.get_or_create_limiter(&format!("external:{}", service), quota);

        match limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => {
                warn!("External API rate limit exceeded for service: {}", service);
                Err(ServiceError::ExternalApi(format!(
                    "Rate limit exceeded for service: {}",
                    service
                )))
            }
        }
    }

    /// Check if database operation is allowed (to prevent overwhelming DB)
    pub async fn check_database_rate_limit(&self, operation: &str) -> Result<(), ServiceError> {
        let quota = match operation {
            "bulk_insert" => Quota::per_minute(NonZeroU32::new(10).unwrap()),
            "bulk_update" => Quota::per_minute(NonZeroU32::new(20).unwrap()),
            "bulk_delete" => Quota::per_minute(NonZeroU32::new(5).unwrap()),
            "migration" => Quota::per_hour(NonZeroU32::new(1).unwrap()),
            _ => Quota::per_second(NonZeroU32::new(100).unwrap()), // Regular operations
        };

        let limiter = self.get_or_create_limiter(&format!("db:{}", operation), quota);

        match limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => {
                warn!("Database rate limit exceeded for operation: {}", operation);
                Err(ServiceError::Database(mongodb::error::Error::custom(format!(
                    "Database rate limit exceeded for operation: {}",
                    operation
                ))))
            }
        }
    }

    /// Get rate limit status for monitoring
    pub fn get_rate_limit_status(&self, key: &str) -> Option<RateLimitStatus> {
        self.limiters.get(key).map(|limiter| {
            let snapshot = limiter.snapshot();
            RateLimitStatus {
                key: key.to_string(),
                remaining_capacity: snapshot.remaining_burst_capacity(),
                reset_after: snapshot.time_to_wait_for_refill(),
            }
        })
    }

    /// Get all rate limit statuses for monitoring
    pub fn get_all_rate_limit_status(&self) -> Vec<RateLimitStatus> {
        self.limiters
            .iter()
            .map(|entry| {
                let snapshot = entry.value().snapshot();
                RateLimitStatus {
                    key: entry.key().clone(),
                    remaining_capacity: snapshot.remaining_burst_capacity(),
                    reset_after: snapshot.time_to_wait_for_refill(),
                }
            })
            .collect()
    }

    /// Clear rate limits for a specific key (admin function)
    pub fn clear_rate_limit(&self, key: &str) -> bool {
        self.limiters.remove(key).is_some()
    }

    /// Clear all rate limits (admin function)
    pub fn clear_all_rate_limits(&self) {
        self.limiters.clear();
        self.sliding_windows.clear();
        info!("All rate limits cleared");
    }

    /// Exponential backoff with jitter to prevent thundering herd
    pub async fn retry_with_backoff<F, T>(&self, operation: F, max_retries: u32) -> Result<T, ServiceError>
    where
        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T, ServiceError>> + Send>>,
    {
        let mut attempt = 0;
        let mut rng = rand::thread_rng();

        loop {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(e) => {
                    attempt += 1;
                    if attempt >= max_retries {
                        error!("Operation failed after {} attempts: {}", max_retries, e);
                        return Err(e);
                    }

                    // Exponential backoff: 2^attempt * 100ms
                    let base_delay = Duration::from_millis(100 * 2_u64.pow(attempt));

                    // Add jitter: ±25% randomness to prevent thundering herd
                    let jitter_factor = rng.gen_range(0.75..=1.25);
                    let delay = Duration::from_millis(
                        (base_delay.as_millis() as f64 * jitter_factor) as u64
                    );

                    warn!("Operation failed (attempt {}), retrying in {:?}: {}", attempt, delay, e);
                    tokio::time::sleep(delay).await;
                }
            }
        }
    }

    /// Throttled external API call with batching
    pub async fn throttled_api_call<F, T>(&self, service: &str, operation: F) -> Result<T, ServiceError>
    where
        F: std::future::Future<Output = Result<T, ServiceError>>,
    {
        match service {
            "vultr" => self.api_throttle.throttle_vultr_call(operation).await,
            _ => operation.await,
        }
    }

    /// Get system health metrics
    pub fn get_system_health(&self) -> SystemHealth {
        let api_queue_depth = self.back_pressure.get_queue_depth("api_requests");
        let deployment_queue_depth = self.back_pressure.get_queue_depth("deployments");
        let build_queue_depth = self.back_pressure.get_queue_depth("builds");

        let total_capacity = self.back_pressure.max_queue_depth * 3; // 3 main queues
        let total_usage = api_queue_depth + deployment_queue_depth + build_queue_depth;
        let pressure = total_usage as f64 / total_capacity as f64;

        SystemHealth {
            api_queue_depth,
            deployment_queue_depth,
            build_queue_depth,
            system_pressure: pressure,
            healthy: pressure < 0.8,
        }
    }
}


#[derive(Debug, Clone, serde::Serialize)]
pub struct RateLimitStatus {
    pub key: String,
    pub remaining_capacity: u32,
    pub reset_after: Option<Duration>,
}

#[derive(Debug, Clone, serde::Serialize)]
pub struct SystemHealth {
    pub api_queue_depth: usize,
    pub deployment_queue_depth: usize,
    pub build_queue_depth: usize,
    pub system_pressure: f64,
    pub healthy: bool,
}

impl Default for RateLimiterService {
    fn default() -> Self {
        Self::new()
    }
}

/// Middleware-compatible rate limiter
pub async fn rate_limit_middleware(
    user_id: Option<String>,
    rate_limiter: &RateLimiterService,
) -> Result<(), ServiceError> {
    if let Some(user_id) = user_id {
        rate_limiter.check_api_rate_limit(&user_id).await?;
    } else {
        // Anonymous rate limiting by IP would go here
        // For now, we'll use a global anonymous limit
        let quota = Quota::per_minute(NonZeroU32::new(20).unwrap());
        let limiter = rate_limiter.get_or_create_limiter("anonymous", quota);
        
        match limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => {
                warn!("Anonymous rate limit exceeded");
                Err(ServiceError::ExternalApi("Rate limit exceeded".to_string()))
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;

    #[tokio::test]
    async fn test_api_rate_limit() {
        let service = RateLimiterService::new();
        
        // Should allow first request
        let result = service.check_api_rate_limit("user1").await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_deployment_rate_limit() {
        let service = RateLimiterService::new();
        
        // Should allow first deployment
        let result = service.check_deployment_rate_limit("user1").await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_external_api_rate_limit() {
        let service = RateLimiterService::new();
        
        // Should allow first external API call
        let result = service.check_external_api_rate_limit("vultr").await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_rate_limit_status() {
        let service = RateLimiterService::new();
        
        // Make a request to create a limiter
        let _ = service.check_api_rate_limit("user1").await;
        
        // Check status
        let status = service.get_rate_limit_status("api:user1");
        assert!(status.is_some());
    }

    #[tokio::test]
    async fn test_clear_rate_limit() {
        let service = RateLimiterService::new();
        
        // Make a request to create a limiter
        let _ = service.check_api_rate_limit("user1").await;
        
        // Clear the rate limit
        let cleared = service.clear_rate_limit("api:user1");
        assert!(cleared);
        
        // Try to clear again (should return false)
        let cleared_again = service.clear_rate_limit("api:user1");
        assert!(!cleared_again);
    }
}
