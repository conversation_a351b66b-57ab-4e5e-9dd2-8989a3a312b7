use governor::{Quo<PERSON>, RateLimiter, DefaultDirectRateLimiter, clock::Default<PERSON><PERSON>, state::InMemoryState};
use std::num::NonZeroU32;
use std::time::Duration;
use dashmap::DashMap;
use std::sync::Arc;
use tracing::{warn, info};
use crate::services::ServiceError;

#[derive(Clone)]
pub struct RateLimiterService {
    limiters: Arc<DashMap<String, DefaultDirectRateLimiter>>,
}

impl RateLimiterService {
    pub fn new() -> Self {
        Self {
            limiters: Arc::new(DashMap::new()),
        }
    }

    pub fn get_or_create_limiter(&self, key: &str, quota: Quota) -> DefaultDirectRateLimiter {
        self.limiters
            .entry(key.to_string())
            .or_insert_with(|| RateLimiter::direct(quota))
            .clone()
    }

    /// Check if request is allowed for API endpoints
    pub async fn check_api_rate_limit(&self, user_id: &str) -> Result<(), ServiceError> {
        let quota = Quota::per_minute(NonZeroU32::new(100).unwrap()); // 100 requests per minute
        let limiter = self.get_or_create_limiter(&format!("api:{}", user_id), quota);

        match limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => {
                warn!("Rate limit exceeded for user: {}", user_id);
                Err(ServiceError::ExternalApi("Rate limit exceeded".to_string()))
            }
        }
    }

    /// Check if deployment is allowed
    pub async fn check_deployment_rate_limit(&self, user_id: &str) -> Result<(), ServiceError> {
        let quota = Quota::per_hour(NonZeroU32::new(10).unwrap()); // 10 deployments per hour
        let limiter = self.get_or_create_limiter(&format!("deploy:{}", user_id), quota);

        match limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => {
                warn!("Deployment rate limit exceeded for user: {}", user_id);
                Err(ServiceError::ExternalApi("Deployment rate limit exceeded".to_string()))
            }
        }
    }

    /// Check if build is allowed
    pub async fn check_build_rate_limit(&self, user_id: &str) -> Result<(), ServiceError> {
        let quota = Quota::per_hour(NonZeroU32::new(20).unwrap()); // 20 builds per hour
        let limiter = self.get_or_create_limiter(&format!("build:{}", user_id), quota);

        match limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => {
                warn!("Build rate limit exceeded for user: {}", user_id);
                Err(ServiceError::ExternalApi("Build rate limit exceeded".to_string()))
            }
        }
    }

    /// Check if external API call is allowed
    pub async fn check_external_api_rate_limit(&self, service: &str) -> Result<(), ServiceError> {
        let quota = match service {
            "vultr" => Quota::per_second(NonZeroU32::new(5).unwrap()), // 5 calls per second
            "github" => Quota::per_hour(NonZeroU32::new(5000).unwrap()), // GitHub API limit
            "gitlab" => Quota::per_minute(NonZeroU32::new(300).unwrap()), // GitLab API limit
            _ => Quota::per_second(NonZeroU32::new(10).unwrap()), // Default
        };

        let limiter = self.get_or_create_limiter(&format!("external:{}", service), quota);

        match limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => {
                warn!("External API rate limit exceeded for service: {}", service);
                Err(ServiceError::ExternalApi(format!(
                    "Rate limit exceeded for service: {}",
                    service
                )))
            }
        }
    }

    /// Check if database operation is allowed (to prevent overwhelming DB)
    pub async fn check_database_rate_limit(&self, operation: &str) -> Result<(), ServiceError> {
        let quota = match operation {
            "bulk_insert" => Quota::per_minute(NonZeroU32::new(10).unwrap()),
            "bulk_update" => Quota::per_minute(NonZeroU32::new(20).unwrap()),
            "bulk_delete" => Quota::per_minute(NonZeroU32::new(5).unwrap()),
            "migration" => Quota::per_hour(NonZeroU32::new(1).unwrap()),
            _ => Quota::per_second(NonZeroU32::new(100).unwrap()), // Regular operations
        };

        let limiter = self.get_or_create_limiter(&format!("db:{}", operation), quota);

        match limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => {
                warn!("Database rate limit exceeded for operation: {}", operation);
                Err(ServiceError::Database(mongodb::error::Error::custom(format!(
                    "Database rate limit exceeded for operation: {}",
                    operation
                ))))
            }
        }
    }

    /// Get rate limit status for monitoring
    pub fn get_rate_limit_status(&self, key: &str) -> Option<RateLimitStatus> {
        self.limiters.get(key).map(|limiter| {
            let snapshot = limiter.snapshot();
            RateLimitStatus {
                key: key.to_string(),
                remaining_capacity: snapshot.remaining_burst_capacity(),
                reset_after: snapshot.time_to_wait_for_refill(),
            }
        })
    }

    /// Get all rate limit statuses for monitoring
    pub fn get_all_rate_limit_status(&self) -> Vec<RateLimitStatus> {
        self.limiters
            .iter()
            .map(|entry| {
                let snapshot = entry.value().snapshot();
                RateLimitStatus {
                    key: entry.key().clone(),
                    remaining_capacity: snapshot.remaining_burst_capacity(),
                    reset_after: snapshot.time_to_wait_for_refill(),
                }
            })
            .collect()
    }

    /// Clear rate limits for a specific key (admin function)
    pub fn clear_rate_limit(&self, key: &str) -> bool {
        self.limiters.remove(key).is_some()
    }

    /// Clear all rate limits (admin function)
    pub fn clear_all_rate_limits(&self) {
        self.limiters.clear();
        info!("All rate limits cleared");
    }
}

#[derive(Debug, Clone, serde::Serialize)]
pub struct RateLimitStatus {
    pub key: String,
    pub remaining_capacity: u32,
    pub reset_after: Option<Duration>,
}

impl Default for RateLimiterService {
    fn default() -> Self {
        Self::new()
    }
}

/// Middleware-compatible rate limiter
pub async fn rate_limit_middleware(
    user_id: Option<String>,
    rate_limiter: &RateLimiterService,
) -> Result<(), ServiceError> {
    if let Some(user_id) = user_id {
        rate_limiter.check_api_rate_limit(&user_id).await?;
    } else {
        // Anonymous rate limiting by IP would go here
        // For now, we'll use a global anonymous limit
        let quota = Quota::per_minute(NonZeroU32::new(20).unwrap());
        let limiter = rate_limiter.get_or_create_limiter("anonymous", quota);
        
        match limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => {
                warn!("Anonymous rate limit exceeded");
                Err(ServiceError::ExternalApi("Rate limit exceeded".to_string()))
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;

    #[tokio::test]
    async fn test_api_rate_limit() {
        let service = RateLimiterService::new();
        
        // Should allow first request
        let result = service.check_api_rate_limit("user1").await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_deployment_rate_limit() {
        let service = RateLimiterService::new();
        
        // Should allow first deployment
        let result = service.check_deployment_rate_limit("user1").await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_external_api_rate_limit() {
        let service = RateLimiterService::new();
        
        // Should allow first external API call
        let result = service.check_external_api_rate_limit("vultr").await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_rate_limit_status() {
        let service = RateLimiterService::new();
        
        // Make a request to create a limiter
        let _ = service.check_api_rate_limit("user1").await;
        
        // Check status
        let status = service.get_rate_limit_status("api:user1");
        assert!(status.is_some());
    }

    #[tokio::test]
    async fn test_clear_rate_limit() {
        let service = RateLimiterService::new();
        
        // Make a request to create a limiter
        let _ = service.check_api_rate_limit("user1").await;
        
        // Clear the rate limit
        let cleared = service.clear_rate_limit("api:user1");
        assert!(cleared);
        
        // Try to clear again (should return false)
        let cleared_again = service.clear_rate_limit("api:user1");
        assert!(!cleared_again);
    }
}
