use crate::{
    config::Config,
    models::{ApiResponse, ServiceError, ServiceResult},
    vultr::{VultrClient, CreateInstanceRequest},
};
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{info, instrument, warn, error};
use tokio::time::{sleep, Duration};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HostingTier {
    pub tier_type: HostingTierType,
    pub plans: Vec<HostingPlan>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HostingTierType {
    Shared,
    Dedicated,
    Enterprise,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HostingPlan {
    pub name: String,
    pub monthly_price_usd: f64,
    pub local_pricing: HashMap<String, f64>,
    pub resource_allocation: ResourceAllocation,
    pub burst_capability: Option<BurstCapability>,
    pub priority_weight: u32,
    pub target_users: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ResourceAllocation {
    pub cpu_shares: Option<u32>,
    pub memory_reservation: Option<String>,
    pub memory_limit: Option<String>,
    pub storage_limit: String,
    pub bandwidth_limit: String,
    pub domains: Option<u32>,
    pub databases: Option<u32>,
    pub vcpu: Option<u32>,
    pub memory_gb: Option<u32>,
    pub guaranteed_resources: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BurstCapability {
    pub cpu_burst: String,
    pub memory_burst: String,
    pub performance_multiplier: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerPool {
    pub pool_type: PoolType,
    pub server_type: String,
    pub cost_per_server: f64,
    pub max_users_per_server: u32,
    pub current_servers: Vec<Server>,
    pub auto_scaling_config: AutoScalingConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PoolType {
    SharedHot,
    SharedCold,
    Dedicated,
    Enterprise,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Server {
    pub instance_id: String,
    pub server_type: String,
    pub pool_type: PoolType,
    pub current_users: u32,
    pub max_users: u32,
    pub cpu_utilization: f64,
    pub memory_utilization: f64,
    pub status: ServerStatus,
    pub region: String,
    pub monthly_cost: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServerStatus {
    Provisioning,
    Active,
    Maintenance,
    Scaling,
    Terminating,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoScalingConfig {
    pub scale_up_trigger: String,
    pub scale_down_trigger: String,
    pub cooldown_period: String,
    pub enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserDeployment {
    pub user_id: String,
    pub plan: String,
    pub tier: HostingTierType,
    pub server_id: String,
    pub container_id: Option<String>,
    pub vm_id: Option<String>,
    pub status: DeploymentStatus,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub resource_usage: ResourceUsage,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DeploymentStatus {
    Pending,
    Deploying,
    Active,
    Sleeping,
    Migrating,
    Failed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUsage {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub storage_usage: f64,
    pub bandwidth_usage: f64,
    pub last_activity: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerPoolStatus {
    pub pool_type: PoolType,
    pub total_servers: u32,
    pub active_servers: u32,
    pub total_users: u32,
    pub max_capacity: u32,
    pub average_cpu_utilization: f64,
    pub average_memory_utilization: f64,
    pub monthly_cost: f64,
    pub auto_scaling_enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProfitAnalysis {
    pub total_revenue: f64,
    pub total_cost: f64,
    pub profit: f64,
    pub profit_margin: f64,
    pub plan_breakdown: Vec<PlanRevenue>,
    pub break_even_users: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlanRevenue {
    pub plan_name: String,
    pub user_count: u32,
    pub monthly_price: f64,
    pub total_revenue: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingAction {
    pub action_type: ScalingActionType,
    pub pool_type: PoolType,
    pub server_id: Option<String>,
    pub reason: String,
    pub estimated_cost: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScalingActionType {
    ScaleUp,
    ScaleDown,
    AddServer,
    RemoveServer,
    MigrateUsers,
}

#[derive(Clone)]
pub struct IntelligentHostingService {
    vultr_client: VultrClient,
    config: Config,
    server_pools: HashMap<PoolType, ServerPool>,
}

impl IntelligentHostingService {
    pub fn new(vultr_client: VultrClient, config: Config) -> Self {
        let mut server_pools = HashMap::new();
        
        // Initialize server pools
        server_pools.insert(PoolType::SharedHot, ServerPool {
            pool_type: PoolType::SharedHot,
            server_type: "vhf-1c-1gb".to_string(),
            cost_per_server: 6.00,
            max_users_per_server: 100,
            current_servers: Vec::new(),
            auto_scaling_config: AutoScalingConfig {
                scale_up_trigger: "CPU >80% for 5min OR Memory >85% for 3min OR Users >90".to_string(),
                scale_down_trigger: "CPU <30% for 15min AND Memory <40% for 15min AND Users <50".to_string(),
                cooldown_period: "10 minutes".to_string(),
                enabled: true,
            },
        });

        server_pools.insert(PoolType::SharedCold, ServerPool {
            pool_type: PoolType::SharedCold,
            server_type: "vc2-1c-1gb".to_string(),
            cost_per_server: 5.00,
            max_users_per_server: 120,
            current_servers: Vec::new(),
            auto_scaling_config: AutoScalingConfig {
                scale_up_trigger: "CPU >75% for 10min OR Memory >80% for 5min".to_string(),
                scale_down_trigger: "CPU <20% for 20min AND Memory <30% for 20min".to_string(),
                cooldown_period: "15 minutes".to_string(),
                enabled: true,
            },
        });

        Self {
            vultr_client,
            config,
            server_pools,
        }
    }

    #[instrument(skip(self))]
    pub async fn deploy_first_server(&self, region: &str) -> ServiceResult<Server> {
        info!("Deploying first vhf-1c-1gb server for hot pool");

        let user_data = self.generate_shared_hosting_user_data().await?;
        
        let request = CreateInstanceRequest {
            region: region.to_string(),
            plan: "vhf-1c-1gb".to_string(),
            os_id: Some(387), // Ubuntu 22.04 LTS
            label: Some("achidas-shared-hot-01".to_string()),
            tag: Some("achidas,shared-hosting,hot-pool,production".to_string()),
            hostname: Some(format!("achidas-hot-{}", chrono::Utc::now().timestamp())),
            enable_ipv6: Some(true),
            enable_private_network: Some(true),
            user_data: Some(user_data),
            ..Default::default()
        };

        let instance = self
            .vultr_client
            .create_instance_detailed(request)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to create instance: {}", e)))?;

        let server = Server {
            instance_id: instance.id.clone(),
            server_type: "vhf-1c-1gb".to_string(),
            pool_type: PoolType::SharedHot,
            current_users: 0,
            max_users: 100,
            cpu_utilization: 0.0,
            memory_utilization: 0.0,
            status: ServerStatus::Provisioning,
            region: region.to_string(),
            monthly_cost: 6.00,
        };

        info!("First server deployed successfully: {}", instance.id);
        Ok(server)
    }

    #[instrument(skip(self))]
    pub async fn deploy_user(&self, user_id: &str, plan: &str, region: &str) -> ServiceResult<UserDeployment> {
        info!("Deploying user {} with plan {} in region {}", user_id, plan, region);

        let hosting_plan = self.get_hosting_plan(plan)?;
        let tier = self.determine_hosting_tier(plan);
        
        match tier {
            HostingTierType::Shared => {
                self.deploy_shared_user(user_id, &hosting_plan, region).await
            },
            HostingTierType::Dedicated => {
                self.deploy_dedicated_user(user_id, &hosting_plan, region).await
            },
            HostingTierType::Enterprise => {
                self.deploy_enterprise_user(user_id, &hosting_plan, region).await
            },
        }
    }

    #[instrument(skip(self))]
    async fn deploy_shared_user(&self, user_id: &str, plan: &HostingPlan, region: &str) -> ServiceResult<UserDeployment> {
        // Find best server in hot pool
        let server = self.find_best_server_for_user(&PoolType::SharedHot, region).await?;
        
        // Deploy container with plan-specific resources
        let container_config = self.generate_container_config(plan);
        let container_id = self.deploy_container(user_id, &container_config, &server.instance_id).await?;

        Ok(UserDeployment {
            user_id: user_id.to_string(),
            plan: plan.name.clone(),
            tier: HostingTierType::Shared,
            server_id: server.instance_id,
            container_id: Some(container_id),
            vm_id: None,
            status: DeploymentStatus::Active,
            created_at: chrono::Utc::now(),
            resource_usage: ResourceUsage {
                cpu_usage: 0.0,
                memory_usage: 0.0,
                storage_usage: 0.0,
                bandwidth_usage: 0.0,
                last_activity: chrono::Utc::now(),
            },
        })
    }

    #[instrument(skip(self))]
    async fn deploy_dedicated_user(&self, user_id: &str, plan: &HostingPlan, region: &str) -> ServiceResult<UserDeployment> {
        // Create dedicated VM
        let vm_specs = plan.resource_allocation.clone();
        let vm_id = self.create_dedicated_vm(user_id, &vm_specs, region).await?;

        Ok(UserDeployment {
            user_id: user_id.to_string(),
            plan: plan.name.clone(),
            tier: HostingTierType::Dedicated,
            server_id: vm_id.clone(),
            container_id: None,
            vm_id: Some(vm_id),
            status: DeploymentStatus::Active,
            created_at: chrono::Utc::now(),
            resource_usage: ResourceUsage {
                cpu_usage: 0.0,
                memory_usage: 0.0,
                storage_usage: 0.0,
                bandwidth_usage: 0.0,
                last_activity: chrono::Utc::now(),
            },
        })
    }

    #[instrument(skip(self))]
    async fn deploy_enterprise_user(&self, user_id: &str, plan: &HostingPlan, region: &str) -> ServiceResult<UserDeployment> {
        // Create enterprise-grade server
        let server_specs = plan.resource_allocation.clone();
        let server_id = self.create_enterprise_server(user_id, &server_specs, region).await?;

        Ok(UserDeployment {
            user_id: user_id.to_string(),
            plan: plan.name.clone(),
            tier: HostingTierType::Enterprise,
            server_id: server_id.clone(),
            container_id: None,
            vm_id: Some(server_id),
            status: DeploymentStatus::Active,
            created_at: chrono::Utc::now(),
            resource_usage: ResourceUsage {
                cpu_usage: 0.0,
                memory_usage: 0.0,
                storage_usage: 0.0,
                bandwidth_usage: 0.0,
                last_activity: chrono::Utc::now(),
            },
        })
    }

    async fn generate_shared_hosting_user_data(&self) -> ServiceResult<String> {
        let user_data = r#"#!/bin/bash
set -e

# Update system
apt-get update
apt-get upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl enable docker
systemctl start docker

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Configure system for shared hosting
echo 'vm.overcommit_memory = 1' >> /etc/sysctl.conf
echo 'vm.overcommit_ratio = 150' >> /etc/sysctl.conf
echo 'vm.swappiness = 10' >> /etc/sysctl.conf
sysctl -p

# Create Achidas network
docker network create achidas-shared || true

# Install Traefik
mkdir -p /opt/traefik
cat > /opt/traefik/docker-compose.yml << 'EOF'
version: '3.8'
services:
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/traefik.yml:ro
      - ./letsencrypt:/letsencrypt
    networks:
      - achidas-shared
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(\`traefik.achidas.local\`)"
      - "traefik.http.routers.dashboard.service=api@internal"

networks:
  achidas-shared:
    external: true
EOF

cat > /opt/traefik/traefik.yml << 'EOF'
global:
  checkNewVersion: false
  sendAnonymousUsage: false

entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: "achidas-shared"

certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /letsencrypt/acme.json
      httpChallenge:
        entryPoint: web

api:
  dashboard: true
  insecure: true
EOF

cd /opt/traefik && docker-compose up -d

# Setup monitoring
mkdir -p /opt/monitoring
cat > /opt/monitoring/docker-compose.yml << 'EOF'
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - achidas-shared

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    networks:
      - achidas-shared

networks:
  achidas-shared:
    external: true
EOF

cat > /opt/monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
  
  - job_name: 'docker'
    static_configs:
      - targets: ['localhost:9323']
EOF

cd /opt/monitoring && docker-compose up -d

# Create management scripts
mkdir -p /opt/achidas
cat > /opt/achidas/deploy-user.sh << 'EOF'
#!/bin/bash
USER_ID=$1
PLAN=$2
IMAGE=$3

case $PLAN in
  starter) CPU_SHARES=256; MEM_RES=8m ;;
  nano) CPU_SHARES=512; MEM_RES=10m ;;
  micro) CPU_SHARES=1024; MEM_RES=20m ;;
  small) CPU_SHARES=2048; MEM_RES=40m ;;
  business) CPU_SHARES=4096; MEM_RES=80m ;;
esac

docker run -d \
  --cpu-shares=$CPU_SHARES \
  --memory-reservation=$MEM_RES \
  --name=user-$USER_ID \
  --network=achidas-shared \
  --label=traefik.enable=true \
  --label=traefik.http.routers.user-$USER_ID.rule=Host\(\`$USER_ID.achidas.com\`\) \
  --label=traefik.http.services.user-$USER_ID.loadbalancer.server.port=8080 \
  $IMAGE
EOF

chmod +x /opt/achidas/deploy-user.sh

# Log completion
echo "Achidas shared hosting server setup completed" > /var/log/achidas-setup.log
date >> /var/log/achidas-setup.log
"#;

        Ok(user_data.to_string())
    }

    #[instrument(skip(self))]
    pub async fn get_all_hosting_plans(&self) -> ServiceResult<Vec<HostingPlan>> {
        Ok(vec![
            // Shared Hosting Plans
            self.get_hosting_plan("starter")?,
            self.get_hosting_plan("nano")?,
            self.get_hosting_plan("micro")?,
            self.get_hosting_plan("small")?,
            self.get_hosting_plan("business")?,
            // Dedicated Hosting Plans
            self.get_hosting_plan("dedicated_small")?,
            self.get_hosting_plan("dedicated_medium")?,
            self.get_hosting_plan("dedicated_large")?,
            // Enterprise Hosting Plans
            self.get_hosting_plan("enterprise_standard")?,
            self.get_hosting_plan("enterprise_premium")?,
        ])
    }

    #[instrument(skip(self))]
    pub async fn get_server_pools_status(&self) -> ServiceResult<HashMap<PoolType, ServerPoolStatus>> {
        let mut status = HashMap::new();

        for (pool_type, pool) in &self.server_pools {
            let pool_status = ServerPoolStatus {
                pool_type: pool_type.clone(),
                total_servers: pool.current_servers.len() as u32,
                active_servers: pool.current_servers.iter().filter(|s| matches!(s.status, ServerStatus::Active)).count() as u32,
                total_users: pool.current_servers.iter().map(|s| s.current_users).sum(),
                max_capacity: pool.current_servers.len() as u32 * pool.max_users_per_server,
                average_cpu_utilization: pool.current_servers.iter().map(|s| s.cpu_utilization).sum::<f64>() / pool.current_servers.len().max(1) as f64,
                average_memory_utilization: pool.current_servers.iter().map(|s| s.memory_utilization).sum::<f64>() / pool.current_servers.len().max(1) as f64,
                monthly_cost: pool.current_servers.len() as f64 * pool.cost_per_server,
                auto_scaling_enabled: pool.auto_scaling_config.enabled,
            };
            status.insert(pool_type.clone(), pool_status);
        }

        Ok(status)
    }

    #[instrument(skip(self))]
    pub async fn calculate_profit_analysis(&self, user_distribution: &HashMap<String, u32>) -> ServiceResult<ProfitAnalysis> {
        let mut total_revenue = 0.0;
        let mut total_cost = 0.0;
        let mut plan_breakdown = Vec::new();

        for (plan_name, user_count) in user_distribution {
            let plan = self.get_hosting_plan(plan_name)?;
            let plan_revenue = plan.monthly_price_usd * (*user_count as f64);
            total_revenue += plan_revenue;

            plan_breakdown.push(PlanRevenue {
                plan_name: plan_name.clone(),
                user_count: *user_count,
                monthly_price: plan.monthly_price_usd,
                total_revenue: plan_revenue,
            });
        }

        // Calculate infrastructure costs
        let pools_status = self.get_server_pools_status().await?;
        for (_, pool_status) in pools_status {
            total_cost += pool_status.monthly_cost;
        }

        let profit = total_revenue - total_cost;
        let profit_margin = if total_revenue > 0.0 { (profit / total_revenue) * 100.0 } else { 0.0 };

        Ok(ProfitAnalysis {
            total_revenue,
            total_cost,
            profit,
            profit_margin,
            plan_breakdown,
            break_even_users: (total_cost / 1.44).ceil() as u32, // Based on nano plan price
        })
    }

    #[instrument(skip(self))]
    pub async fn auto_scale_check(&self) -> ServiceResult<Vec<ScalingAction>> {
        let mut actions = Vec::new();

        for (pool_type, pool) in &self.server_pools {
            if !pool.auto_scaling_config.enabled {
                continue;
            }

            for server in &pool.current_servers {
                // Check scale up conditions
                if server.cpu_utilization > 80.0 || server.memory_utilization > 85.0 || server.current_users > 90 {
                    actions.push(ScalingAction {
                        action_type: ScalingActionType::ScaleUp,
                        pool_type: pool_type.clone(),
                        server_id: Some(server.instance_id.clone()),
                        reason: format!("High utilization: CPU {}%, Memory {}%, Users {}",
                                      server.cpu_utilization, server.memory_utilization, server.current_users),
                        estimated_cost: pool.cost_per_server,
                    });
                }

                // Check scale down conditions
                if server.cpu_utilization < 30.0 && server.memory_utilization < 40.0 && server.current_users < 50 {
                    actions.push(ScalingAction {
                        action_type: ScalingActionType::ScaleDown,
                        pool_type: pool_type.clone(),
                        server_id: Some(server.instance_id.clone()),
                        reason: format!("Low utilization: CPU {}%, Memory {}%, Users {}",
                                      server.cpu_utilization, server.memory_utilization, server.current_users),
                        estimated_cost: -pool.cost_per_server,
                    });
                }
            }

            // Check if we need new servers
            if pool.current_servers.is_empty() || pool.current_servers.iter().all(|s| s.current_users >= pool.max_users_per_server * 90 / 100) {
                actions.push(ScalingAction {
                    action_type: ScalingActionType::AddServer,
                    pool_type: pool_type.clone(),
                    server_id: None,
                    reason: "Pool at capacity, need new server".to_string(),
                    estimated_cost: pool.cost_per_server,
                });
            }
        }

        Ok(actions)
    }

    // Helper methods
    fn get_hosting_plan(&self, plan_name: &str) -> ServiceResult<HostingPlan> {
        let plan = match plan_name {
            "starter" => HostingPlan {
                name: "starter".to_string(),
                monthly_price_usd: 0.99,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 1485.0),
                    ("kenya_kes".to_string(), 129.0),
                    ("south_africa_zar".to_string(), 18.0),
                    ("ghana_ghs".to_string(), 12.0),
                    ("egypt_egp".to_string(), 31.0),
                    ("morocco_mad".to_string(), 10.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: Some(256),
                    memory_reservation: Some("8Mi".to_string()),
                    memory_limit: None,
                    storage_limit: "500MB".to_string(),
                    bandwidth_limit: "5GB/month".to_string(),
                    domains: Some(1),
                    databases: Some(1),
                    vcpu: None,
                    memory_gb: None,
                    guaranteed_resources: Some(false),
                },
                burst_capability: Some(BurstCapability {
                    cpu_burst: "Up to 1000m when server idle".to_string(),
                    memory_burst: "Up to 800Mi when available".to_string(),
                    performance_multiplier: "20-100x during low traffic".to_string(),
                }),
                priority_weight: 1,
                target_users: "Students, personal projects, learning".to_string(),
            },
            "nano" => HostingPlan {
                name: "nano".to_string(),
                monthly_price_usd: 1.44,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 2160.0),
                    ("kenya_kes".to_string(), 187.0),
                    ("south_africa_zar".to_string(), 26.0),
                    ("ghana_ghs".to_string(), 17.0),
                    ("egypt_egp".to_string(), 45.0),
                    ("morocco_mad".to_string(), 15.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: Some(512),
                    memory_reservation: Some("10Mi".to_string()),
                    memory_limit: None,
                    storage_limit: "1GB".to_string(),
                    bandwidth_limit: "10GB/month".to_string(),
                    domains: Some(3),
                    databases: Some(2),
                    vcpu: None,
                    memory_gb: None,
                    guaranteed_resources: Some(false),
                },
                burst_capability: Some(BurstCapability {
                    cpu_burst: "Up to 1000m when server idle".to_string(),
                    memory_burst: "Up to 800Mi when available".to_string(),
                    performance_multiplier: "10-50x during low traffic".to_string(),
                }),
                priority_weight: 2,
                target_users: "Small websites, APIs, microservices".to_string(),
            },
            "micro" => HostingPlan {
                name: "micro".to_string(),
                monthly_price_usd: 2.88,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 4320.0),
                    ("kenya_kes".to_string(), 374.0),
                    ("south_africa_zar".to_string(), 52.0),
                    ("ghana_ghs".to_string(), 35.0),
                    ("egypt_egp".to_string(), 90.0),
                    ("morocco_mad".to_string(), 30.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: Some(1024),
                    memory_reservation: Some("20Mi".to_string()),
                    memory_limit: None,
                    storage_limit: "2GB".to_string(),
                    bandwidth_limit: "25GB/month".to_string(),
                    domains: Some(5),
                    databases: Some(3),
                    vcpu: None,
                    memory_gb: None,
                    guaranteed_resources: Some(false),
                },
                burst_capability: Some(BurstCapability {
                    cpu_burst: "Up to 1000m when server idle".to_string(),
                    memory_burst: "Up to 800Mi when available".to_string(),
                    performance_multiplier: "5-25x during low traffic".to_string(),
                }),
                priority_weight: 4,
                target_users: "Small businesses, growing startups".to_string(),
            },
            "small" => HostingPlan {
                name: "small".to_string(),
                monthly_price_usd: 5.76,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 8640.0),
                    ("kenya_kes".to_string(), 748.0),
                    ("south_africa_zar".to_string(), 104.0),
                    ("ghana_ghs".to_string(), 69.0),
                    ("egypt_egp".to_string(), 180.0),
                    ("morocco_mad".to_string(), 60.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: Some(2048),
                    memory_reservation: Some("40Mi".to_string()),
                    memory_limit: None,
                    storage_limit: "5GB".to_string(),
                    bandwidth_limit: "50GB/month".to_string(),
                    domains: Some(10),
                    databases: Some(5),
                    vcpu: None,
                    memory_gb: None,
                    guaranteed_resources: Some(false),
                },
                burst_capability: Some(BurstCapability {
                    cpu_burst: "Up to 1000m when server idle".to_string(),
                    memory_burst: "Up to 800Mi when available".to_string(),
                    performance_multiplier: "3-15x during low traffic".to_string(),
                }),
                priority_weight: 8,
                target_users: "Medium businesses, production apps".to_string(),
            },
            "business" => HostingPlan {
                name: "business".to_string(),
                monthly_price_usd: 11.52,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 17280.0),
                    ("kenya_kes".to_string(), 1497.0),
                    ("south_africa_zar".to_string(), 208.0),
                    ("ghana_ghs".to_string(), 138.0),
                    ("egypt_egp".to_string(), 360.0),
                    ("morocco_mad".to_string(), 120.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: Some(4096),
                    memory_reservation: Some("80Mi".to_string()),
                    memory_limit: None,
                    storage_limit: "10GB".to_string(),
                    bandwidth_limit: "100GB/month".to_string(),
                    domains: Some(25),
                    databases: Some(10),
                    vcpu: None,
                    memory_gb: None,
                    guaranteed_resources: Some(false),
                },
                burst_capability: Some(BurstCapability {
                    cpu_burst: "Up to 1000m when server idle".to_string(),
                    memory_burst: "Up to 800Mi when available".to_string(),
                    performance_multiplier: "2-10x during low traffic".to_string(),
                }),
                priority_weight: 16,
                target_users: "Established businesses, high-traffic sites".to_string(),
            },
            "dedicated_small" => HostingPlan {
                name: "dedicated_small".to_string(),
                monthly_price_usd: 25.00,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 37500.0),
                    ("kenya_kes".to_string(), 3250.0),
                    ("south_africa_zar".to_string(), 450.0),
                    ("ghana_ghs".to_string(), 300.0),
                    ("egypt_egp".to_string(), 780.0),
                    ("morocco_mad".to_string(), 260.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: None,
                    memory_reservation: None,
                    memory_limit: None,
                    storage_limit: "80GB".to_string(),
                    bandwidth_limit: "2TB/month".to_string(),
                    domains: None,
                    databases: None,
                    vcpu: Some(2),
                    memory_gb: Some(4),
                    guaranteed_resources: Some(true),
                },
                burst_capability: None,
                priority_weight: 32,
                target_users: "Agencies, SaaS startups, e-commerce".to_string(),
            },
            "dedicated_medium" => HostingPlan {
                name: "dedicated_medium".to_string(),
                monthly_price_usd: 50.00,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 75000.0),
                    ("kenya_kes".to_string(), 6500.0),
                    ("south_africa_zar".to_string(), 900.0),
                    ("ghana_ghs".to_string(), 600.0),
                    ("egypt_egp".to_string(), 1560.0),
                    ("morocco_mad".to_string(), 520.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: None,
                    memory_reservation: None,
                    memory_limit: None,
                    storage_limit: "160GB".to_string(),
                    bandwidth_limit: "4TB/month".to_string(),
                    domains: None,
                    databases: None,
                    vcpu: Some(4),
                    memory_gb: Some(8),
                    guaranteed_resources: Some(true),
                },
                burst_capability: None,
                priority_weight: 64,
                target_users: "Growing SaaS, fintech, enterprise apps".to_string(),
            },
            "dedicated_large" => HostingPlan {
                name: "dedicated_large".to_string(),
                monthly_price_usd: 100.00,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 150000.0),
                    ("kenya_kes".to_string(), 13000.0),
                    ("south_africa_zar".to_string(), 1800.0),
                    ("ghana_ghs".to_string(), 1200.0),
                    ("egypt_egp".to_string(), 3120.0),
                    ("morocco_mad".to_string(), 1040.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: None,
                    memory_reservation: None,
                    memory_limit: None,
                    storage_limit: "320GB".to_string(),
                    bandwidth_limit: "8TB/month".to_string(),
                    domains: None,
                    databases: None,
                    vcpu: Some(8),
                    memory_gb: Some(16),
                    guaranteed_resources: Some(true),
                },
                burst_capability: None,
                priority_weight: 128,
                target_users: "Large enterprises, high-performance apps".to_string(),
            },
            "enterprise_standard" => HostingPlan {
                name: "enterprise_standard".to_string(),
                monthly_price_usd: 200.00,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 300000.0),
                    ("kenya_kes".to_string(), 26000.0),
                    ("south_africa_zar".to_string(), 3600.0),
                    ("ghana_ghs".to_string(), 2400.0),
                    ("egypt_egp".to_string(), 6240.0),
                    ("morocco_mad".to_string(), 2080.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: None,
                    memory_reservation: None,
                    memory_limit: None,
                    storage_limit: "640GB".to_string(),
                    bandwidth_limit: "16TB/month".to_string(),
                    domains: None,
                    databases: None,
                    vcpu: Some(16),
                    memory_gb: Some(32),
                    guaranteed_resources: Some(true),
                },
                burst_capability: None,
                priority_weight: 256,
                target_users: "Large enterprises, mission-critical apps".to_string(),
            },
            "enterprise_premium" => HostingPlan {
                name: "enterprise_premium".to_string(),
                monthly_price_usd: 400.00,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 600000.0),
                    ("kenya_kes".to_string(), 52000.0),
                    ("south_africa_zar".to_string(), 7200.0),
                    ("ghana_ghs".to_string(), 4800.0),
                    ("egypt_egp".to_string(), 12480.0),
                    ("morocco_mad".to_string(), 4160.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: None,
                    memory_reservation: None,
                    memory_limit: None,
                    storage_limit: "1280GB".to_string(),
                    bandwidth_limit: "32TB/month".to_string(),
                    domains: None,
                    databases: None,
                    vcpu: Some(32),
                    memory_gb: Some(64),
                    guaranteed_resources: Some(true),
                },
                burst_capability: None,
                priority_weight: 512,
                target_users: "Fortune 500, banks, government".to_string(),
            },
            _ => return Err(ServiceError::NotFound(format!("Hosting plan '{}' not found", plan_name))),
        };

        Ok(plan)
    }

    fn determine_hosting_tier(&self, plan: &str) -> HostingTierType {
        match plan {
            "starter" | "nano" | "micro" | "small" | "business" => HostingTierType::Shared,
            "dedicated_small" | "dedicated_medium" | "dedicated_large" => HostingTierType::Dedicated,
            "enterprise_standard" | "enterprise_premium" => HostingTierType::Enterprise,
            _ => HostingTierType::Shared,
        }
    }

    async fn find_best_server_for_user(&self, pool_type: &PoolType, region: &str) -> ServiceResult<Server> {
        // Mock implementation - would find server with lowest utilization
        Ok(Server {
            instance_id: "mock-server-id".to_string(),
            server_type: "vhf-1c-1gb".to_string(),
            pool_type: pool_type.clone(),
            current_users: 50,
            max_users: 100,
            cpu_utilization: 60.0,
            memory_utilization: 55.0,
            status: ServerStatus::Active,
            region: region.to_string(),
            monthly_cost: 6.00,
        })
    }

    fn generate_container_config(&self, plan: &HostingPlan) -> String {
        format!(
            "--cpu-shares={} --memory-reservation={}",
            plan.resource_allocation.cpu_shares.unwrap_or(512),
            plan.resource_allocation.memory_reservation.as_ref().unwrap_or(&"10m".to_string())
        )
    }

    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {
        // Mock implementation - would deploy container via SSH or API
        Ok(format!("container-{}", user_id))
    }

    async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {
        // Mock implementation - would create dedicated VM
        Ok(format!("vm-{}", user_id))
    }

    async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {
        // Mock implementation - would create enterprise server
        Ok(format!("enterprise-{}", user_id))
    }
}
