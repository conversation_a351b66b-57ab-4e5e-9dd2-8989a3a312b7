#!/bin/bash

# Achidas Intelligent Hosting Platform - Server Import Script
# This script helps you import manually provisioned Vultr servers into the platform

set -e

# Configuration
REGION=${REGION:-"ewr"}  # Default to New York/New Jersey
API_BASE_URL=${API_BASE_URL:-"http://localhost:3000/api/v1"}
AUTH_TOKEN=${AUTH_TOKEN:-""}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if curl is installed
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed. Please install curl."
        exit 1
    fi
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed. Please install jq."
        exit 1
    fi
    
    # Check if AUTH_TOKEN is set
    if [ -z "$AUTH_TOKEN" ]; then
        log_error "AUTH_TOKEN environment variable is required."
        log_info "Please set AUTH_TOKEN with your authentication token."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Check API health
check_api_health() {
    log_info "Checking API health..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$API_BASE_URL/health" || echo "000")
    
    if [ "$response" = "200" ]; then
        log_success "API is healthy"
    else
        log_error "API health check failed (HTTP $response)"
        if [ -f /tmp/health_response.json ]; then
            cat /tmp/health_response.json
        fi
        exit 1
    fi
}

# Get hosting plans
get_hosting_plans() {
    log_info "Fetching hosting plans..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/plans_response.json \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        "$API_BASE_URL/hosting/plans")
    
    http_code=$(echo "$response" | tail -c 4)
    
    if [ "$http_code" = "200" ]; then
        log_success "Hosting plans fetched successfully"
        
        # Display available plans
        log_info "Available hosting plans:"
        echo "Shared Hosting Plans:"
        jq -r '.data.shared_plans[] | "  - \(.name): $\(.monthly_price_usd)/month - \(.target_users)"' /tmp/plans_response.json
        echo ""
        echo "Dedicated Hosting Plans:"
        jq -r '.data.dedicated_plans[] | "  - \(.name): $\(.monthly_price_usd)/month - \(.target_users)"' /tmp/plans_response.json
        echo ""
        echo "Enterprise Hosting Plans:"
        jq -r '.data.enterprise_plans[] | "  - \(.name): $\(.monthly_price_usd)/month - \(.target_users)"' /tmp/plans_response.json
        echo ""
    else
        log_error "Failed to fetch hosting plans (HTTP $http_code)"
        if [ -f /tmp/plans_response.json ]; then
            cat /tmp/plans_response.json
        fi
        exit 1
    fi
}

# List available Vultr servers
list_vultr_servers() {
    log_info "Fetching available Vultr servers..."

    response=$(curl -s -w "%{http_code}" -o /tmp/servers_response.json \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        "$API_BASE_URL/hosting/servers/vultr")

    http_code=$(echo "$response" | tail -c 4)

    if [ "$http_code" = "200" ]; then
        log_success "Vultr servers fetched successfully"

        # Display available servers
        log_info "Available Vultr servers for import:"
        echo ""
        printf "%-20s %-15s %-12s %-10s %-15s %-10s %s\n" "Instance ID" "Plan" "Region" "Status" "IP Address" "Cost/Month" "Suitable"
        echo "--------------------------------------------------------------------------------------------------------"

        jq -r '.data[] | "\(.instance_id) \(.plan) \(.region) \(.status) \(.main_ip) $\(.monthly_cost) \(.suitable_for_achidas)"' /tmp/servers_response.json | \
        while read -r line; do
            printf "%-20s %-15s %-12s %-10s %-15s %-10s %s\n" $line
        done
        echo ""

        # Show recommended servers
        log_info "Recommended servers for Achidas hosting:"
        jq -r '.data[] | select(.suitable_for_achidas == true) | "  - \(.instance_id) (\(.plan)) - \(.recommended_pool) pool - $\(.monthly_cost)/month"' /tmp/servers_response.json
        echo ""

    else
        log_error "Failed to fetch Vultr servers (HTTP $http_code)"
        if [ -f /tmp/servers_response.json ]; then
            cat /tmp/servers_response.json
        fi
        exit 1
    fi
}

# Import a server
import_server() {
    local instance_id=$1
    local pool_type=$2

    if [ -z "$instance_id" ] || [ -z "$pool_type" ]; then
        log_error "Usage: import_server <instance_id> <pool_type>"
        log_info "Pool types: shared-hot, shared-cold, dedicated, enterprise"
        return 1
    fi

    log_info "Importing server $instance_id into $pool_type pool..."

    # Convert pool type to API format
    api_pool_type=""
    case $pool_type in
        "shared-hot") api_pool_type="SharedHot" ;;
        "shared-cold") api_pool_type="SharedCold" ;;
        "dedicated") api_pool_type="Dedicated" ;;
        "enterprise") api_pool_type="Enterprise" ;;
        *) log_error "Invalid pool type: $pool_type"; return 1 ;;
    esac

    request_body="{\"pool_type\": \"$api_pool_type\"}"

    response=$(curl -s -w "%{http_code}" -o /tmp/import_response.json \
        -X POST \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d "$request_body" \
        "$API_BASE_URL/hosting/servers/import/$instance_id")

    http_code=$(echo "$response" | tail -c 4)

    if [ "$http_code" = "200" ]; then
        log_success "Server imported successfully"

        # Extract server details
        instance_id=$(jq -r '.data.instance_id' /tmp/import_response.json)
        server_type=$(jq -r '.data.server_type' /tmp/import_response.json)
        monthly_cost=$(jq -r '.data.monthly_cost' /tmp/import_response.json)
        max_users=$(jq -r '.data.max_users' /tmp/import_response.json)
        pool_type=$(jq -r '.data.pool_type' /tmp/import_response.json)

        log_info "Imported Server Details:"
        echo "  Instance ID: $instance_id"
        echo "  Server Type: $server_type"
        echo "  Pool Type: $pool_type"
        echo "  Monthly Cost: \$$monthly_cost"
        echo "  Max Users: $max_users"
        echo ""

        # Save import info
        cat > imported_server.json << EOF
{
  "import_date": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "instance_id": "$instance_id",
  "server_type": "$server_type",
  "pool_type": "$pool_type",
  "monthly_cost": $monthly_cost,
  "max_users": $max_users
}
EOF

        log_success "Import info saved to imported_server.json"

        # Get setup instructions
        get_setup_instructions "$instance_id" "$pool_type"

    else
        log_error "Failed to import server (HTTP $http_code)"
        if [ -f /tmp/import_response.json ]; then
            cat /tmp/import_response.json
        fi
        exit 1
    fi
}

# Get setup instructions
get_setup_instructions() {
    local instance_id=$1
    local pool_type=$2

    log_info "Getting setup instructions for server $instance_id..."

    response=$(curl -s -w "%{http_code}" -o /tmp/setup_response.json \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        "$API_BASE_URL/hosting/servers/setup/$instance_id?pool_type=$pool_type")

    http_code=$(echo "$response" | tail -c 4)

    if [ "$http_code" = "200" ]; then
        log_success "Setup instructions generated"

        # Save setup script
        jq -r '.data.setup_script' /tmp/setup_response.json > setup_server.sh
        chmod +x setup_server.sh

        log_info "Setup Instructions:"
        echo "1. SSH into your server:"
        echo "   ssh root@<your-server-ip>"
        echo ""
        echo "2. Copy the setup script to your server:"
        echo "   scp setup_server.sh root@<your-server-ip>:/root/"
        echo ""
        echo "3. Run the setup script on your server:"
        echo "   ssh root@<your-server-ip> 'chmod +x /root/setup_server.sh && /root/setup_server.sh'"
        echo ""
        echo "4. Wait for setup to complete (5-10 minutes)"
        echo ""

        log_success "Setup script saved as setup_server.sh"

    else
        log_warning "Failed to get setup instructions (HTTP $http_code)"
    fi
}

# Monitor deployment progress
monitor_deployment() {
    log_info "Monitoring deployment progress..."
    
    if [ ! -f deployment_info.json ]; then
        log_error "deployment_info.json not found. Cannot monitor deployment."
        return 1
    fi
    
    instance_id=$(jq -r '.instance_id' deployment_info.json)
    
    log_info "Checking server status every 30 seconds..."
    
    for i in {1..20}; do  # Check for up to 10 minutes
        log_info "Check $i/20: Checking server status..."
        
        response=$(curl -s -w "%{http_code}" -o /tmp/status_response.json \
            -H "Authorization: Bearer $AUTH_TOKEN" \
            -H "Content-Type: application/json" \
            "$API_BASE_URL/hosting/infrastructure/status")
        
        http_code=$(echo "$response" | tail -c 4)
        
        if [ "$http_code" = "200" ]; then
            # Check if our server is active
            total_servers=$(jq -r '.data.total_servers' /tmp/status_response.json)
            
            if [ "$total_servers" -gt 0 ]; then
                log_success "Server is now active and ready!"
                log_info "Infrastructure Status:"
                echo "  Total Servers: $total_servers"
                echo "  Total Users: $(jq -r '.data.total_users' /tmp/status_response.json)"
                echo "  Monthly Cost: \$$(jq -r '.data.total_monthly_cost' /tmp/status_response.json)"
                echo "  Average Utilization: $(jq -r '.data.average_utilization' /tmp/status_response.json)%"
                return 0
            fi
        fi
        
        log_info "Server still provisioning... waiting 30 seconds"
        sleep 30
    done
    
    log_warning "Deployment monitoring timed out. Server may still be provisioning."
    log_info "You can check the status manually using the infrastructure status endpoint."
}

# Get profit analysis
show_profit_analysis() {
    log_info "Calculating profit analysis for sample user distribution..."
    
    # Sample user distribution for demonstration
    user_distribution='{
        "user_distribution": {
            "starter": 25,
            "nano": 30,
            "micro": 20,
            "small": 20,
            "business": 5
        }
    }'
    
    response=$(curl -s -w "%{http_code}" -o /tmp/profit_response.json \
        -X POST \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d "$user_distribution" \
        "$API_BASE_URL/hosting/analytics/profit")
    
    http_code=$(echo "$response" | tail -c 4)
    
    if [ "$http_code" = "200" ]; then
        log_success "Profit analysis calculated successfully"
        
        total_revenue=$(jq -r '.data.total_revenue' /tmp/profit_response.json)
        total_cost=$(jq -r '.data.total_cost' /tmp/profit_response.json)
        profit=$(jq -r '.data.profit' /tmp/profit_response.json)
        profit_margin=$(jq -r '.data.profit_margin' /tmp/profit_response.json)
        break_even_users=$(jq -r '.data.break_even_users' /tmp/profit_response.json)
        
        log_info "Profit Analysis (100 users sample):"
        echo "  Total Revenue: \$$total_revenue/month"
        echo "  Total Cost: \$$total_cost/month"
        echo "  Profit: \$$profit/month"
        echo "  Profit Margin: $profit_margin%"
        echo "  Break-even Users: $break_even_users"
        echo ""
        
        log_info "Plan Breakdown:"
        jq -r '.data.plan_breakdown[] | "  \(.plan_name): \(.user_count) users × $\(.monthly_price) = $\(.total_revenue)"' /tmp/profit_response.json
        
    else
        log_warning "Failed to calculate profit analysis (HTTP $http_code)"
    fi
}

# Interactive server import
interactive_import() {
    log_info "Starting interactive server import..."

    # List servers first
    list_vultr_servers

    # Ask user to select server
    echo ""
    read -p "Enter the Instance ID of the server you want to import: " instance_id

    if [ -z "$instance_id" ]; then
        log_error "No instance ID provided"
        return 1
    fi

    echo ""
    log_info "Available pool types:"
    echo "  1. shared-hot    - High-performance shared hosting (vhf-1c-1gb recommended)"
    echo "  2. shared-cold   - Cost-effective shared hosting (vc2-1c-1gb recommended)"
    echo "  3. dedicated     - Dedicated VM hosting (vc2-2c-4gb+ recommended)"
    echo "  4. enterprise    - Enterprise hosting with SLA (vc2-8c-16gb+ recommended)"
    echo ""
    read -p "Select pool type (1-4): " pool_choice

    case $pool_choice in
        1) pool_type="shared-hot" ;;
        2) pool_type="shared-cold" ;;
        3) pool_type="dedicated" ;;
        4) pool_type="enterprise" ;;
        *) log_error "Invalid choice"; return 1 ;;
    esac

    # Import the server
    import_server "$instance_id" "$pool_type"
}

# Main execution
main() {
    echo "=================================================="
    echo "  Achidas Intelligent Hosting Platform"
    echo "  Server Import Script"
    echo "=================================================="
    echo ""

    check_prerequisites
    check_api_health
    get_hosting_plans

    # Check if arguments provided for non-interactive mode
    if [ $# -eq 2 ]; then
        log_info "Non-interactive mode: importing server $1 into $2 pool"
        import_server "$1" "$2"
    elif [ "$1" = "list" ]; then
        list_vultr_servers
    else
        interactive_import
    fi

    show_profit_analysis

    echo ""
    log_success "🚀 Server import process completed!"
    log_info "Your Achidas intelligent hosting platform is ready."
    log_info "Next steps:"
    echo "  1. Complete server setup using the generated script"
    echo "  2. Deploy your first user: POST /api/v1/hosting/deploy/user/{user_id}"
    echo "  3. Monitor infrastructure: GET /api/v1/hosting/infrastructure/status"
    echo "  4. Check scaling recommendations: GET /api/v1/hosting/scaling/recommendations"
    echo "  5. View African pricing: GET /api/v1/hosting/pricing/{country_code}"
    echo ""
    log_info "Usage examples:"
    echo "  ./deploy_first_server.sh                    # Interactive mode"
    echo "  ./deploy_first_server.sh list               # List available servers"
    echo "  ./deploy_first_server.sh <instance_id> <pool_type>  # Direct import"
    echo ""
    log_info "Happy hosting! 🌍"
}

# Run main function
main "$@"
