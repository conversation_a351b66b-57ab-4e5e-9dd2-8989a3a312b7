#!/bin/bash

# Achidas Intelligent Hosting Platform - First Server Deployment Script
# This script deploys the first vhf-1c-1gb server for the hot pool

set -e

# Configuration
REGION=${REGION:-"ewr"}  # Default to New York/New Jersey
API_BASE_URL=${API_BASE_URL:-"http://localhost:3000/api/v1"}
AUTH_TOKEN=${AUTH_TOKEN:-""}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if curl is installed
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed. Please install curl."
        exit 1
    fi
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed. Please install jq."
        exit 1
    fi
    
    # Check if AUTH_TOKEN is set
    if [ -z "$AUTH_TOKEN" ]; then
        log_error "AUTH_TOKEN environment variable is required."
        log_info "Please set AUTH_TOKEN with your authentication token."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Check API health
check_api_health() {
    log_info "Checking API health..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$API_BASE_URL/health" || echo "000")
    
    if [ "$response" = "200" ]; then
        log_success "API is healthy"
    else
        log_error "API health check failed (HTTP $response)"
        if [ -f /tmp/health_response.json ]; then
            cat /tmp/health_response.json
        fi
        exit 1
    fi
}

# Get hosting plans
get_hosting_plans() {
    log_info "Fetching hosting plans..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/plans_response.json \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        "$API_BASE_URL/hosting/plans")
    
    http_code=$(echo "$response" | tail -c 4)
    
    if [ "$http_code" = "200" ]; then
        log_success "Hosting plans fetched successfully"
        
        # Display available plans
        log_info "Available hosting plans:"
        echo "Shared Hosting Plans:"
        jq -r '.data.shared_plans[] | "  - \(.name): $\(.monthly_price_usd)/month - \(.target_users)"' /tmp/plans_response.json
        echo ""
        echo "Dedicated Hosting Plans:"
        jq -r '.data.dedicated_plans[] | "  - \(.name): $\(.monthly_price_usd)/month - \(.target_users)"' /tmp/plans_response.json
        echo ""
        echo "Enterprise Hosting Plans:"
        jq -r '.data.enterprise_plans[] | "  - \(.name): $\(.monthly_price_usd)/month - \(.target_users)"' /tmp/plans_response.json
        echo ""
    else
        log_error "Failed to fetch hosting plans (HTTP $http_code)"
        if [ -f /tmp/plans_response.json ]; then
            cat /tmp/plans_response.json
        fi
        exit 1
    fi
}

# Deploy first server
deploy_first_server() {
    log_info "Deploying first server (vhf-1c-1gb) in region: $REGION"
    
    response=$(curl -s -w "%{http_code}" -o /tmp/deploy_response.json \
        -X POST \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        "$API_BASE_URL/hosting/deploy/first-server?region=$REGION")
    
    http_code=$(echo "$response" | tail -c 4)
    
    if [ "$http_code" = "200" ]; then
        log_success "First server deployment initiated successfully"
        
        # Extract server details
        instance_id=$(jq -r '.data.instance_id' /tmp/deploy_response.json)
        server_type=$(jq -r '.data.server_type' /tmp/deploy_response.json)
        monthly_cost=$(jq -r '.data.monthly_cost' /tmp/deploy_response.json)
        max_users=$(jq -r '.data.max_users' /tmp/deploy_response.json)
        
        log_info "Server Details:"
        echo "  Instance ID: $instance_id"
        echo "  Server Type: $server_type"
        echo "  Region: $REGION"
        echo "  Monthly Cost: \$$monthly_cost"
        echo "  Max Users: $max_users"
        echo "  Status: Provisioning"
        
        # Save deployment info
        cat > deployment_info.json << EOF
{
  "deployment_date": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "instance_id": "$instance_id",
  "server_type": "$server_type",
  "region": "$REGION",
  "monthly_cost": $monthly_cost,
  "max_users": $max_users,
  "pool_type": "SharedHot"
}
EOF
        
        log_success "Deployment info saved to deployment_info.json"
        
    else
        log_error "Failed to deploy first server (HTTP $http_code)"
        if [ -f /tmp/deploy_response.json ]; then
            cat /tmp/deploy_response.json
        fi
        exit 1
    fi
}

# Monitor deployment progress
monitor_deployment() {
    log_info "Monitoring deployment progress..."
    
    if [ ! -f deployment_info.json ]; then
        log_error "deployment_info.json not found. Cannot monitor deployment."
        return 1
    fi
    
    instance_id=$(jq -r '.instance_id' deployment_info.json)
    
    log_info "Checking server status every 30 seconds..."
    
    for i in {1..20}; do  # Check for up to 10 minutes
        log_info "Check $i/20: Checking server status..."
        
        response=$(curl -s -w "%{http_code}" -o /tmp/status_response.json \
            -H "Authorization: Bearer $AUTH_TOKEN" \
            -H "Content-Type: application/json" \
            "$API_BASE_URL/hosting/infrastructure/status")
        
        http_code=$(echo "$response" | tail -c 4)
        
        if [ "$http_code" = "200" ]; then
            # Check if our server is active
            total_servers=$(jq -r '.data.total_servers' /tmp/status_response.json)
            
            if [ "$total_servers" -gt 0 ]; then
                log_success "Server is now active and ready!"
                log_info "Infrastructure Status:"
                echo "  Total Servers: $total_servers"
                echo "  Total Users: $(jq -r '.data.total_users' /tmp/status_response.json)"
                echo "  Monthly Cost: \$$(jq -r '.data.total_monthly_cost' /tmp/status_response.json)"
                echo "  Average Utilization: $(jq -r '.data.average_utilization' /tmp/status_response.json)%"
                return 0
            fi
        fi
        
        log_info "Server still provisioning... waiting 30 seconds"
        sleep 30
    done
    
    log_warning "Deployment monitoring timed out. Server may still be provisioning."
    log_info "You can check the status manually using the infrastructure status endpoint."
}

# Get profit analysis
show_profit_analysis() {
    log_info "Calculating profit analysis for sample user distribution..."
    
    # Sample user distribution for demonstration
    user_distribution='{
        "user_distribution": {
            "starter": 25,
            "nano": 30,
            "micro": 20,
            "small": 20,
            "business": 5
        }
    }'
    
    response=$(curl -s -w "%{http_code}" -o /tmp/profit_response.json \
        -X POST \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d "$user_distribution" \
        "$API_BASE_URL/hosting/analytics/profit")
    
    http_code=$(echo "$response" | tail -c 4)
    
    if [ "$http_code" = "200" ]; then
        log_success "Profit analysis calculated successfully"
        
        total_revenue=$(jq -r '.data.total_revenue' /tmp/profit_response.json)
        total_cost=$(jq -r '.data.total_cost' /tmp/profit_response.json)
        profit=$(jq -r '.data.profit' /tmp/profit_response.json)
        profit_margin=$(jq -r '.data.profit_margin' /tmp/profit_response.json)
        break_even_users=$(jq -r '.data.break_even_users' /tmp/profit_response.json)
        
        log_info "Profit Analysis (100 users sample):"
        echo "  Total Revenue: \$$total_revenue/month"
        echo "  Total Cost: \$$total_cost/month"
        echo "  Profit: \$$profit/month"
        echo "  Profit Margin: $profit_margin%"
        echo "  Break-even Users: $break_even_users"
        echo ""
        
        log_info "Plan Breakdown:"
        jq -r '.data.plan_breakdown[] | "  \(.plan_name): \(.user_count) users × $\(.monthly_price) = $\(.total_revenue)"' /tmp/profit_response.json
        
    else
        log_warning "Failed to calculate profit analysis (HTTP $http_code)"
    fi
}

# Main execution
main() {
    echo "=================================================="
    echo "  Achidas Intelligent Hosting Platform"
    echo "  First Server Deployment Script"
    echo "=================================================="
    echo ""
    
    check_prerequisites
    check_api_health
    get_hosting_plans
    deploy_first_server
    monitor_deployment
    show_profit_analysis
    
    echo ""
    log_success "🚀 First server deployment completed successfully!"
    log_info "Your Achidas intelligent hosting platform is now ready to accept users."
    log_info "Next steps:"
    echo "  1. Deploy your first user: POST /api/v1/hosting/deploy/user/{user_id}"
    echo "  2. Monitor infrastructure: GET /api/v1/hosting/infrastructure/status"
    echo "  3. Check scaling recommendations: GET /api/v1/hosting/scaling/recommendations"
    echo "  4. View African pricing: GET /api/v1/hosting/pricing/{country_code}"
    echo ""
    log_info "Happy hosting! 🌍"
}

# Run main function
main "$@"
