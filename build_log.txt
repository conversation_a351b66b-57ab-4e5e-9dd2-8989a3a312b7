cargo :    Compiling achidas v0.1.0 (D:\workspace\.rust\achidas)
At line:1 char:1
+ cargo build 2>&1 | tee build_log.txt
+ ~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (   Compiling ac...\.rust\achidas):String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
error: struct is not supported in `trait`s or `impl`s
  --> src\utils\retry.rs:72:1
   |
72 | pub struct RetryClient {
   | ^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider moving the struct out to a nearby module scope

error: implementation is not supported in `trait`s or `impl`s
  --> src\utils\retry.rs:78:1
   |
78 | impl RetryClient {
   | ^^^^^^^^^^^^^^^^
   |
   = help: consider moving the implementation out to a nearby module scope

error: struct is not supported in `trait`s or `impl`s
   --> src\utils\retry.rs:182:1
    |
182 | pub struct RequestBuilder {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: consider moving the struct out to a nearby module scope

error: implementation is not supported in `trait`s or `impl`s
   --> src\utils\retry.rs:187:1
    |
187 | impl RequestBuilder {
    | ^^^^^^^^^^^^^^^^^^^
    |
    = help: consider moving the implementation out to a nearby module scope

error: attempting to skip non-existent parameter
   --> src\controllers\webhooks.rs:192:19
    |
192 | #[instrument(skip(state))]
    |                   ^^^^^

error: No transitions provided for a compact representation
  --> src\infrastructure\state_machine.rs:80:5
   |
80 |     Cancelled => {},
   |     ^^^^^^^^^

error: No transitions provided for a compact representation
   --> src\infrastructure\state_machine.rs:138:5
    |
138 |     Deleted => {},
    |     ^^^^^^^

error: No transitions provided for a compact representation
   --> src\infrastructure\state_machine.rs:179:5
    |
179 |     Success => {},
    |     ^^^^^^^

error[E0432]: unresolved imports `crate::services::DeploymentService`, `crate::services::EnvironmentService`, `crate::state`, 
`crate::utils::auth`
 --> src\controllers\applications.rs:7:16
  |
7 |     services::{DeploymentService, EnvironmentService},
  |                ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^ no `EnvironmentService` in `services`
  |                |
  |                no `DeploymentService` in `services`
8 |     state::AppState,
  |     ^^^^^
  |     |
  |     unresolved import
  |     help: a similar path exists: `governor::state`
9 |     utils::auth::Claims,
  |            ^^^^ could not find `auth` in `utils`
  |
  = help: consider importing this struct instead:
          crate::services::deployment::DeploymentService
  = help: consider importing this struct instead:
          crate::services::environment::EnvironmentService

error[E0432]: unresolved imports `crate::models::EnvironmentGroup`, `crate::services::EnvironmentService`, `crate::state`, `crate::utils::auth`
 --> src\controllers\environment.rs:3:27
  |
3 |     models::{ApiResponse, EnvironmentGroup},
  |                           ^^^^^^^^^^^^^^^^ no `EnvironmentGroup` in `models`
4 |     services::EnvironmentService,
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ no `EnvironmentService` in `services`
5 |     state::AppState,
  |     ^^^^^ unresolved import
6 |     utils::auth::Claims,
  |            ^^^^ could not find `auth` in `utils`
  |
  = help: consider importing this struct instead:
          crate::services::environment::EnvironmentGroup
  = help: consider importing this struct instead:
          crate::services::environment::EnvironmentService
help: a similar name exists in the module
  |
3 -     models::{ApiResponse, EnvironmentGroup},
3 +     models::{ApiResponse, Environment},
  |
help: a similar path exists
  |
5 |     governor::state::AppState,
  |     ++++++++++

error[E0432]: unresolved imports `crate::services::BuildService`, `crate::state`, `crate::utils::auth`
 --> src\controllers\logs.rs:4:5
  |
4 |     services::BuildService,
  |     ^^^^^^^^^^^^^^^^^^^^^^ no `BuildService` in `services`
5 |     state::AppState,
  |     ^^^^^
  |     |
  |     unresolved import
  |     help: a similar path exists: `governor::state`
6 |     utils::auth::Claims,
  |            ^^^^ could not find `auth` in `utils`
  |
  = help: consider importing this struct instead:
          crate::services::build::BuildService

error[E0432]: unresolved import `tokio_stream`
  --> src\controllers\logs.rs:19:5
   |
19 | use tokio_stream::StreamExt as _;
   |     ^^^^^^^^^^^^ use of unresolved module or unlinked crate `tokio_stream`
   |
   = help: if you wanted to use a crate named `tokio_stream`, use `cargo add tokio_stream` to add it to your `Cargo.toml`

error[E0432]: unresolved imports `crate::services::GitService`, `crate::services::DeploymentService`, `crate::state`
 --> src\controllers\webhooks.rs:4:16
  |
4 |     services::{GitService, DeploymentService},
  |                ^^^^^^^^^^  ^^^^^^^^^^^^^^^^^ no `DeploymentService` in `services`
  |                |
  |                no `GitService` in `services`
5 |     state::AppState,
  |     ^^^^^
  |     |
  |     unresolved import
  |     help: a similar path exists: `governor::state`
  |
  = help: consider importing this struct instead:
          crate::services::git::GitService
  = help: consider importing this struct instead:
          crate::services::deployment::DeploymentService

error[E0432]: unresolved import `rand`
  --> src\infrastructure\rate_limiter.rs:10:5
   |
10 | use rand::Rng;
   |     ^^^^ use of unresolved module or unlinked crate `rand`
   |
   = help: if you wanted to use a crate named `rand`, use `cargo add rand` to add it to your `Cargo.toml`

error[E0432]: unresolved import `crate::infrastructure::BuildStateManager`
 --> src\services\build.rs:5:53
  |
5 |         CircuitBreakerService, CircuitBreakerAware, BuildStateManager, BuildState,
  |                                                     ^^^^^^^^^^^^^^^^^ no `BuildStateManager` in `infrastructure`

error[E0432]: unresolved import `sha2`
   --> src\services\build.rs:925:13
    |
925 |         use sha2::{Sha256, Digest};
    |             ^^^^ use of unresolved module or unlinked crate `sha2`
    |
    = help: if you wanted to use a crate named `sha2`, use `cargo add sha2` to add it to your `Cargo.toml`

error[E0432]: unresolved import `sha2`
   --> src\services\build.rs:957:13
    |
957 |         use sha2::{Sha256, Digest};
    |             ^^^^ use of unresolved module or unlinked crate `sha2`
    |
    = help: if you wanted to use a crate named `sha2`, use `cargo add sha2` to add it to your `Cargo.toml`

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `aes_gcm`
 --> src\services\environment.rs:7:5
  |
7 | use aes_gcm::{
  |     ^^^^^^^ use of unresolved module or unlinked crate `aes_gcm`
  |
  = help: if you wanted to use a crate named `aes_gcm`, use `cargo add aes_gcm` to add it to your `Cargo.toml`

error[E0432]: unresolved imports `crate::services::BuildService`, `crate::services::GitService`
  --> src\services\deployment.rs:14:45
   |
14 |     services::{ServiceError, ServiceResult, BuildService, GitService},
   |                                             ^^^^^^^^^^^^  ^^^^^^^^^^ no `GitService` in `services`
   |                                             |
   |                                             no `BuildService` in `services`
   |
   = help: consider importing this struct instead:
           crate::services::build::BuildService
   = help: consider importing this struct instead:
           crate::services::git::GitService

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `base64`
  --> src\services\environment.rs:11:5
   |
11 | use base64::{engine::general_purpose, Engine as _};
   |     ^^^^^^ use of unresolved module or unlinked crate `base64`
   |
   = help: if you wanted to use a crate named `base64`, use `cargo add base64` to add it to your `Cargo.toml`

error[E0432]: unresolved import `aes_gcm`
 --> src\services\environment.rs:7:5
  |
7 | use aes_gcm::{
  |     ^^^^^^^ use of unresolved module or unlinked crate `aes_gcm`
  |
  = help: if you wanted to use a crate named `aes_gcm`, use `cargo add aes_gcm` to add it to your `Cargo.toml`

error[E0432]: unresolved import `base64`
  --> src\services\environment.rs:11:5
   |
11 | use base64::{engine::general_purpose, Engine as _};
   |     ^^^^^^ use of unresolved module or unlinked crate `base64`
   |
   = help: if you wanted to use a crate named `base64`, use `cargo add base64` to add it to your `Cargo.toml`

error[E0432]: unresolved import `rand`
  --> src\services\environment.rs:14:5
   |
14 | use rand::RngCore;
   |     ^^^^ use of unresolved module or unlinked crate `rand`
   |
   = help: if you wanted to use a crate named `rand`, use `cargo add rand` to add it to your `Cargo.toml`

error[E0432]: unresolved import `sha2`
  --> src\services\environment.rs:73:13
   |
73 |         use sha2::{Digest, Sha256};
   |             ^^^^ use of unresolved module or unlinked crate `sha2`
   |
   = help: if you wanted to use a crate named `sha2`, use `cargo add sha2` to add it to your `Cargo.toml`

error[E0432]: unresolved import `hmac`
  --> src\services\git.rs:18:5
   |
18 | use hmac::{Hmac, Mac};
   |     ^^^^ use of unresolved module or unlinked crate `hmac`
   |
   = help: if you wanted to use a crate named `hmac`, use `cargo add hmac` to add it to your `Cargo.toml`

error[E0432]: unresolved import `sha2`
  --> src\services\git.rs:19:5
   |
19 | use sha2::Sha256;
   |     ^^^^ use of unresolved module or unlinked crate `sha2`
   |
   = help: if you wanted to use a crate named `sha2`, use `cargo add sha2` to add it to your `Cargo.toml`

error[E0432]: unresolved import `hex`
  --> src\services\git.rs:20:5
   |
20 | use hex;
   |     ^^^ no external crate `hex`

error[E0432]: unresolved imports `crate::models::ServiceError`, `crate::models::ServiceResult`
 --> src\services\intelligent_hosting.rs:3:27
  |
3 |     models::{ApiResponse, ServiceError, ServiceResult},
  |                           ^^^^^^^^^^^^  ^^^^^^^^^^^^^ no `ServiceResult` in `models`
  |                           |
  |                           no `ServiceError` in `models`
  |
  = help: consider importing one of these items instead:
          crate::services::ServiceError
          tower::buffer::error::ServiceError
  = help: consider importing this type alias instead:
          crate::services::ServiceResult

error[E0432]: unresolved imports `crate::models::ServiceError`, `crate::models::ServiceResult`
 --> src\services\kubernetes_deployment.rs:3:27
  |
3 |     models::{ApiResponse, ServiceError, ServiceResult},
  |                           ^^^^^^^^^^^^  ^^^^^^^^^^^^^ no `ServiceResult` in `models`
  |                           |
  |                           no `ServiceError` in `models`
  |
  = help: consider importing one of these items instead:
          crate::services::ServiceError
          tower::buffer::error::ServiceError
  = help: consider importing this type alias instead:
          crate::services::ServiceResult

error[E0432]: unresolved import `crate::utils::retry::RetryClient`
 --> src\vultr\mod.rs:1:44
  |
1 | use crate::{config::Config, utils::retry::{RetryClient, RateLimiter}};
  |                                            ^^^^^^^^^^^ no `RetryClient` in `utils::retry`

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `tokio_stream`
   --> src\controllers\logs.rs:216:18
    |
216 |     let stream = tokio_stream::wrappers::IntervalStream::new(interval);
    |                  ^^^^^^^^^^^^ use of unresolved module or unlinked crate `tokio_stream`
    |
    = help: if you wanted to use a crate named `tokio_stream`, use `cargo add tokio_stream` to add it to your `Cargo.toml`

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `tokio_stream`
   --> src\controllers\logs.rs:242:18
    |
242 |     let stream = tokio_stream::wrappers::IntervalStream::new(interval);
    |                  ^^^^^^^^^^^^ use of unresolved module or unlinked crate `tokio_stream`
    |
    = help: if you wanted to use a crate named `tokio_stream`, use `cargo add tokio_stream` to add it to your `Cargo.toml`

error[E0433]: failed to resolve: could not find `State` in `failsafe`
  --> src\infrastructure\circuit_breaker.rs:95:52
   |
95 |                 is_open: matches!(state, failsafe::State::Open),
   |                                                    ^^^^^ could not find `State` in `failsafe`
   |
help: consider importing this struct
   |
1  + use axum::extract::State;
   |
help: if you import `State`, refer to it directly
   |
95 -                 is_open: matches!(state, failsafe::State::Open),
95 +                 is_open: matches!(state, State::Open),
   |

error[E0412]: cannot find type `DeploymentStateMachine` in this scope
   --> src\infrastructure\state_machine.rs:233:20
    |
233 |     state_machine: DeploymentStateMachine,
    |                    ^^^^^^^^^^^^^^^^^^^^^^ not found in this scope

error[E0412]: cannot find type `ApplicationStateMachine` in this scope
   --> src\infrastructure\state_machine.rs:311:20
    |
311 |     state_machine: ApplicationStateMachine,
    |                    ^^^^^^^^^^^^^^^^^^^^^^^ not found in this scope

error[E0412]: cannot find type `Database` in module `crate::models`
   --> src\services\billing.rs:280:70
    |
280 |         let databases_collection: mongodb::Collection<crate::models::Database> =
    |                                                                      ^^^^^^^^ not found in `crate::models`
    |
help: consider importing one of these structs
    |
1   + use crate::Database;
    |
1   + use mongodb::Database;
    |
help: if you import `Database`, refer to it directly
    |
280 -         let databases_collection: mongodb::Collection<crate::models::Database> =
280 +         let databases_collection: mongodb::Collection<Database> =
    |

error[E0412]: cannot find type `Database` in module `crate::models`
   --> src\services\billing.rs:343:73
    |
343 |     async fn calculate_database_monthly_cost(&self, db: &crate::models::Database) -> ServiceResult<f64> {
    |                                                                         ^^^^^^^^ not found in `crate::models`
    |
help: consider importing one of these structs
    |
1   + use crate::Database;
    |
1   + use mongodb::Database;
    |
help: if you import `Database`, refer to it directly
    |
343 -     async fn calculate_database_monthly_cost(&self, db: &crate::models::Database) -> ServiceResult<f64> {
343 +     async fn calculate_database_monthly_cost(&self, db: &Database) -> ServiceResult<f64> {
    |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `regex`
   --> src\services\blueprint.rs:418:33
    |
418 |         if let Some(captures) = regex::Regex::new(r"https://github\.com/([^/]+)/([^/]+)")
    |                                 ^^^^^ use of unresolved module or unlinked crate `regex`
    |
    = help: if you wanted to use a crate named `regex`, use `cargo add regex` to add it to your `Cargo.toml`
help: consider importing this struct
    |
1   + use bson::Regex;
    |
help: if you import `Regex`, refer to it directly
    |
418 -         if let Some(captures) = regex::Regex::new(r"https://github\.com/([^/]+)/([^/]+)")
418 +         if let Some(captures) = Regex::new(r"https://github\.com/([^/]+)/([^/]+)")
    |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `regex`
   --> src\services\blueprint.rs:427:33
    |
427 |         if let Some(captures) = regex::Regex::new(r"https://gitlab\.com/([^/]+)/([^/]+)")
    |                                 ^^^^^ use of unresolved module or unlinked crate `regex`
    |
    = help: if you wanted to use a crate named `regex`, use `cargo add regex` to add it to your `Cargo.toml`
help: consider importing this struct
    |
1   + use bson::Regex;
    |
help: if you import `Regex`, refer to it directly
    |
427 -         if let Some(captures) = regex::Regex::new(r"https://gitlab\.com/([^/]+)/([^/]+)")
427 +         if let Some(captures) = Regex::new(r"https://gitlab\.com/([^/]+)/([^/]+)")
    |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `regex`
   --> src\services\blueprint.rs:436:33
    |
436 |         if let Some(captures) = regex::Regex::new(r"https://bitbucket\.org/([^/]+)/([^/]+)")
    |                                 ^^^^^ use of unresolved module or unlinked crate `regex`
    |
    = help: if you wanted to use a crate named `regex`, use `cargo add regex` to add it to your `Cargo.toml`
help: consider importing this struct
    |
1   + use bson::Regex;
    |
help: if you import `Regex`, refer to it directly
    |
436 -         if let Some(captures) = regex::Regex::new(r"https://bitbucket\.org/([^/]+)/([^/]+)")
436 +         if let Some(captures) = Regex::new(r"https://bitbucket\.org/([^/]+)/([^/]+)")
    |

error[E0422]: cannot find struct, variant or union type `ChunkProcessorConfig` in this scope
  --> src\services\build.rs:46:51
   |
46 |         let chunk_processor = ChunkProcessor::new(ChunkProcessorConfig {
   |                                                   ^^^^^^^^^^^^^^^^^^^^
   |
  ::: src\infrastructure\chunk_processor.rs:32:1
   |
32 | pub struct ChunkProcessor {
   | ------------------------- similarly named struct `ChunkProcessor` defined here
   |
help: a struct with a similar name exists
   |
46 -         let chunk_processor = ChunkProcessor::new(ChunkProcessorConfig {
46 +         let chunk_processor = ChunkProcessor::new(ChunkProcessor {
   |
help: consider importing this struct through its public re-export
   |
1  + use crate::infrastructure::ChunkProcessorConfig;
   |

error[E0412]: cannot find type `Path` in this scope
    --> src\services\build.rs:1003:56
     |
1003 |     async fn collect_files_recursive(&self, dir_path: &Path, file_paths: &mut Vec<std::path::PathBuf>) -> ServiceResult<()> {
     |                                                        ^^^^ not found in this scope
     |
help: consider importing one of these structs
     |
1    + use std::path::Path;
     |
1    + use axum::extract::Path;
     |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `regex`
   --> src\services\deployment.rs:635:21
    |
635 |         let regex = regex::Regex::new(r"https://github\.com/([^/]+)/([^/]+)")
    |                     ^^^^^ use of unresolved module or unlinked crate `regex`
    |
    = help: if you wanted to use a crate named `regex`, use `cargo add regex` to add it to your `Cargo.toml`
help: consider importing this struct
    |
1   + use bson::Regex;
    |
help: if you import `Regex`, refer to it directly
    |
635 -         let regex = regex::Regex::new(r"https://github\.com/([^/]+)/([^/]+)")
635 +         let regex = Regex::new(r"https://github\.com/([^/]+)/([^/]+)")
    |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `regex`
   --> src\services\deployment.rs:648:21
    |
648 |         let regex = regex::Regex::new(r"https://gitlab\.com/([^/]+)/([^/]+)")
    |                     ^^^^^ use of unresolved module or unlinked crate `regex`
    |
    = help: if you wanted to use a crate named `regex`, use `cargo add regex` to add it to your `Cargo.toml`
help: consider importing this struct
    |
1   + use bson::Regex;
    |
help: if you import `Regex`, refer to it directly
    |
648 -         let regex = regex::Regex::new(r"https://gitlab\.com/([^/]+)/([^/]+)")
648 +         let regex = Regex::new(r"https://gitlab\.com/([^/]+)/([^/]+)")
    |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `regex`
   --> src\services\deployment.rs:661:21
    |
661 |         let regex = regex::Regex::new(r"https://bitbucket\.org/([^/]+)/([^/]+)")
    |                     ^^^^^ use of unresolved module or unlinked crate `regex`
    |
    = help: if you wanted to use a crate named `regex`, use `cargo add regex` to add it to your `Cargo.toml`
help: consider importing this struct
    |
1   + use bson::Regex;
    |
help: if you import `Regex`, refer to it directly
    |
661 -         let regex = regex::Regex::new(r"https://bitbucket\.org/([^/]+)/([^/]+)")
661 +         let regex = Regex::new(r"https://bitbucket\.org/([^/]+)/([^/]+)")
    |

error[E0659]: `BuildStep` is ambiguous
  --> src\services\build.rs:11:69
   |
11 |         CreateBuildJobRequest, BuildJobResponse, BuildLogsResponse, BuildStep,
   |                                                                     ^^^^^^^^^ ambiguous name
   |
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `BuildStep` could refer to the enum imported here
  --> src\models\mod.rs:19:9
   |
19 | pub use deployment::*;
   |         ^^^^^^^^^^^^^
   = help: consider adding an explicit import of `BuildStep` to disambiguate
note: `BuildStep` could also refer to the enum imported here
  --> src\models\mod.rs:20:9
   |
20 | pub use build::*;
   |         ^^^^^^^^
   = help: consider adding an explicit import of `BuildStep` to disambiguate

error[E0659]: `LogLevel` is ambiguous
  --> src\services\build.rs:12:9
   |
12 |         LogLevel, LogStream, ArtifactType, BuildRuntime
   |         ^^^^^^^^ ambiguous name
   |
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `LogLevel` could refer to the enum imported here
  --> src\models\mod.rs:19:9
   |
19 | pub use deployment::*;
   |         ^^^^^^^^^^^^^
   = help: consider adding an explicit import of `LogLevel` to disambiguate
note: `LogLevel` could also refer to the enum imported here
  --> src\models\mod.rs:20:9
   |
20 | pub use build::*;
   |         ^^^^^^^^
   = help: consider adding an explicit import of `LogLevel` to disambiguate

error[E0659]: `ServiceType` is ambiguous
  --> src\services\deployment.rs:12:58
   |
12 |         Environment, EnvironmentResponse, RuntimeConfig, ServiceType, AutoScalingConfig, Pagination
   |                                                          ^^^^^^^^^^^ ambiguous name
   |
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ServiceType` could refer to the enum imported here
  --> src\models\mod.rs:18:9
   |
18 | pub use billing::*;
   |         ^^^^^^^^^^
   = help: consider adding an explicit import of `ServiceType` to disambiguate
note: `ServiceType` could also refer to the enum imported here
  --> src\models\mod.rs:21:9
   |
21 | pub use blueprint::*;
   |         ^^^^^^^^^^^^
   = help: consider adding an explicit import of `ServiceType` to disambiguate

error[E0659]: `AutoScalingConfig` is ambiguous
  --> src\services\deployment.rs:12:71
   |
12 |         Environment, EnvironmentResponse, RuntimeConfig, ServiceType, AutoScalingConfig, Pagination
   |                                                                       ^^^^^^^^^^^^^^^^^ ambiguous name
   |
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `AutoScalingConfig` could refer to the struct imported here
  --> src\models\mod.rs:19:9
   |
19 | pub use deployment::*;
   |         ^^^^^^^^^^^^^
   = help: consider adding an explicit import of `AutoScalingConfig` to disambiguate
note: `AutoScalingConfig` could also refer to the struct imported here
  --> src\models\mod.rs:21:9
   |
21 | pub use blueprint::*;
   |         ^^^^^^^^^^^^
   = help: consider adding an explicit import of `AutoScalingConfig` to disambiguate

error[E0659]: `BuildStep` is ambiguous
   --> src\services\build.rs:293:37
    |
293 |         self.log_build_step(job_id, BuildStep::Clone, LogLevel::Info, "Cloning repository...").await?;
    |                                     ^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `BuildStep` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate
note: `BuildStep` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate

error[E0659]: `LogLevel` is ambiguous
   --> src\services\build.rs:293:55
    |
293 |         self.log_build_step(job_id, BuildStep::Clone, LogLevel::Info, "Cloning repository...").await?;
    |                                                       ^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `LogLevel` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate
note: `LogLevel` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate

error[E0659]: `BuildStep` is ambiguous
   --> src\services\build.rs:299:37
    |
299 |         self.log_build_step(job_id, BuildStep::InstallDependencies, LogLevel::Info, "Installing dependencies...").await?;
    |                                     ^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `BuildStep` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate
note: `BuildStep` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate

error[E0659]: `LogLevel` is ambiguous
   --> src\services\build.rs:299:69
    |
299 |         self.log_build_step(job_id, BuildStep::InstallDependencies, LogLevel::Info, "Installing dependencies...").await?;
    |                                                                     ^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `LogLevel` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate
note: `LogLevel` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate

error[E0659]: `BuildStep` is ambiguous
   --> src\services\build.rs:305:37
    |
305 |         self.log_build_step(job_id, BuildStep::Build, LogLevel::Info, "Building application...").await?;
    |                                     ^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `BuildStep` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate
note: `BuildStep` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate

error[E0659]: `LogLevel` is ambiguous
   --> src\services\build.rs:305:55
    |
305 |         self.log_build_step(job_id, BuildStep::Build, LogLevel::Info, "Building application...").await?;
    |                                                       ^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `LogLevel` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate
note: `LogLevel` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate

error[E0659]: `BuildStep` is ambiguous
   --> src\services\build.rs:311:37
    |
311 |         self.log_build_step(job_id, BuildStep::Package, LogLevel::Info, "Packaging artifacts...").await?;
    |                                     ^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `BuildStep` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate
note: `BuildStep` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate

error[E0659]: `LogLevel` is ambiguous
   --> src\services\build.rs:311:57
    |
311 |         self.log_build_step(job_id, BuildStep::Package, LogLevel::Info, "Packaging artifacts...").await?;
    |                                                         ^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `LogLevel` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate
note: `LogLevel` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate

error[E0659]: `BuildStep` is ambiguous
   --> src\services\build.rs:362:37
    |
362 |         self.log_build_step(job_id, BuildStep::Build, LogLevel::Error, error_message).await?;
    |                                     ^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `BuildStep` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate
note: `BuildStep` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate

error[E0659]: `LogLevel` is ambiguous
   --> src\services\build.rs:362:55
    |
362 |         self.log_build_step(job_id, BuildStep::Build, LogLevel::Error, error_message).await?;
    |                                                       ^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `LogLevel` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate
note: `LogLevel` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate

error[E0659]: `BuildStep` is ambiguous
   --> src\services\build.rs:392:60
    |
392 |     async fn log_build_step(&self, job_id: ObjectId, step: BuildStep, level: LogLevel, message: &str) -> ServiceResult<()> {
    |                                                            ^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `BuildStep` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate
note: `BuildStep` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `BuildStep` to disambiguate

error[E0659]: `LogLevel` is ambiguous
   --> src\services\build.rs:392:78
    |
392 |     async fn log_build_step(&self, job_id: ObjectId, step: BuildStep, level: LogLevel, message: &str) -> ServiceResult<()> {
    |                                                                              ^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `LogLevel` could refer to the enum imported here
   --> src\models\mod.rs:19:9
    |
19  | pub use deployment::*;
    |         ^^^^^^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate
note: `LogLevel` could also refer to the enum imported here
   --> src\models\mod.rs:20:9
    |
20  | pub use build::*;
    |         ^^^^^^^^
    = help: consider adding an explicit import of `LogLevel` to disambiguate

error[E0659]: `ServiceType` is ambiguous
   --> src\services\deployment.rs:676:13
    |
676 |             ServiceType::WebService => {
    |             ^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ServiceType` could refer to the enum imported here
   --> src\models\mod.rs:18:9
    |
18  | pub use billing::*;
    |         ^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate
note: `ServiceType` could also refer to the enum imported here
   --> src\models\mod.rs:21:9
    |
21  | pub use blueprint::*;
    |         ^^^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate

error[E0659]: `ServiceType` is ambiguous
   --> src\services\deployment.rs:680:13
    |
680 |             ServiceType::BackgroundWorker => {
    |             ^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ServiceType` could refer to the enum imported here
   --> src\models\mod.rs:18:9
    |
18  | pub use billing::*;
    |         ^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate
note: `ServiceType` could also refer to the enum imported here
   --> src\models\mod.rs:21:9
    |
21  | pub use blueprint::*;
    |         ^^^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate

error[E0659]: `ServiceType` is ambiguous
   --> src\services\deployment.rs:684:13
    |
684 |             ServiceType::CronJob { .. } => {
    |             ^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ServiceType` could refer to the enum imported here
   --> src\models\mod.rs:18:9
    |
18  | pub use billing::*;
    |         ^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate
note: `ServiceType` could also refer to the enum imported here
   --> src\models\mod.rs:21:9
    |
21  | pub use blueprint::*;
    |         ^^^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate

error[E0659]: `ServiceType` is ambiguous
   --> src\services\deployment.rs:688:13
    |
688 |             ServiceType::StaticSite => {
    |             ^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ServiceType` could refer to the enum imported here
   --> src\models\mod.rs:18:9
    |
18  | pub use billing::*;
    |         ^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate
note: `ServiceType` could also refer to the enum imported here
   --> src\models\mod.rs:21:9
    |
21  | pub use blueprint::*;
    |         ^^^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate

error[E0659]: `ServiceType` is ambiguous
   --> src\services\deployment.rs:721:13
    |
721 |             ServiceType::WebService | ServiceType::StaticSite => {
    |             ^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ServiceType` could refer to the enum imported here
   --> src\models\mod.rs:18:9
    |
18  | pub use billing::*;
    |         ^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate
note: `ServiceType` could also refer to the enum imported here
   --> src\models\mod.rs:21:9
    |
21  | pub use blueprint::*;
    |         ^^^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate

error[E0659]: `ServiceType` is ambiguous
   --> src\services\deployment.rs:721:39
    |
721 |             ServiceType::WebService | ServiceType::StaticSite => {
    |                                       ^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ServiceType` could refer to the enum imported here
   --> src\models\mod.rs:18:9
    |
18  | pub use billing::*;
    |         ^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate
note: `ServiceType` could also refer to the enum imported here
   --> src\models\mod.rs:21:9
    |
21  | pub use blueprint::*;
    |         ^^^^^^^^^^^^
    = help: consider adding an explicit import of `ServiceType` to disambiguate

warning: unused import: `validator::Validate`
  --> src\controllers\auth.rs:10:5
   |
10 | use validator::Validate;
   |     ^^^^^^^^^^^^^^^^^^^
   |
   = note: `#[warn(unused_imports)]` on by default

warning: unused imports: `AddDatabaseReadReplicaRequest`, `BareMetalBandwidth`, `BareMetalIpv4Info`, `BareMetalIpv6Info`, `BareMetalUpgrades`, 
`BareMetalUserData`, `BareMetalVncInfo`, `BareMetalVpcInfo`, `CreateBareMetalRequest`, `CreateDatabaseConnectionPoolRequest`, 
`CreateDatabaseConnectorRequest`, `CreateDatabaseDBRequest`, `CreateDatabaseQuotaRequest`, `CreateDatabaseTopicRequest`, 
`CreateStorageGatewayExportRequest`, `ForkDatabaseRequest`, `RestoreDatabaseFromBackupRequest`, `StartDatabaseMigrationRequest`, 
`StartDatabaseVersionUpgradeRequest`, `UpdateBareMetalRequest`, `UpdateDatabaseConnectionPoolRequest`, `UpdateDatabaseConnectorRequest`, 
`UpdateDatabaseTopicRequest`, `VultrBareMetal`, `VultrDatabaseAdvancedOptions`, `VultrDatabaseBackupInfo`, `VultrDatabaseConnectionPool`, 
`VultrDatabaseConnectorStatus`, `VultrDatabaseConnector`, `VultrDatabaseDB`, `VultrDatabaseMaintenanceUpdate`, `VultrDatabaseMigrationStatus`, 
`VultrDatabaseQuota`, `VultrDatabaseReadReplica`, `VultrDatabaseServiceAlert`, `VultrDatabaseTopic`, `VultrDatabaseVersions`, `VultrOS`, 
`VultrPlan`, `VultrRegion`, `VultrStorageGatewayExportConfig`, and `VultrVFSAttachment`
  --> src\controllers\vultr.rs:4:9
   |
4  |         BareMetalBandwidth, BareMetalIpv4Info, BareMetalIpv6Info, BareMetalUpgrades,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^
5  |         BareMetalUserData, BareMetalVncInfo, BareMetalVpcInfo, CreateBareMetalRequest,
   |         ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |         ^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^  ^^^^^^^^^  ^^^^^^^^^^^  ^^^^^^^^^^^^^^
...
37 |         SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,
   |                                    ^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
38 |         VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
39 |         VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
40 |         CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
41 |         VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
42 |         VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
43 |         AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
44 |         RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
45 |         CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
46 |         VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
58 |         CreateStorageGatewayExportRequest, VultrStorageGatewayExportConfig, VultrUser,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
59 |         CreateUserRequest, UpdateUserRequest, VultrVFS, CreateVFSRequest,
60 |         UpdateVFSRequest, VultrVFSAttachment, VultrVFSRegion, BareMetalActionRequest,
   |                           ^^^^^^^^^^^^^^^^^^

warning: unused import: `http::StatusCode`
  --> src\controllers\applications.rs:13:5
   |
13 |     http::StatusCode,
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `self`
  --> src\controllers\logs.rs:14:23
   |
14 | use futures::stream::{self, Stream};
   |                       ^^^^

warning: unused import: `StatusCode`
 --> src\controllers\webhooks.rs:9:23
  |
9 |     http::{HeaderMap, StatusCode},
  |                       ^^^^^^^^^^

warning: unused import: `error`
  --> src\controllers\webhooks.rs:14:15
   |
14 | use tracing::{error, info, instrument, warn};
   |               ^^^^^

warning: unused import: `serde_json::json`
  --> src\controllers\mod.rs:20:5
   |
20 | use serde_json::json;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `error`
 --> src\infrastructure\state_machine.rs:4:27
  |
4 | use tracing::{info, warn, error};
  |                           ^^^^^

warning: unused imports: `clock::DefaultClock` and `state::InMemoryState`
 --> src\infrastructure\rate_limiter.rs:1:62
  |
1 | use governor::{Quota, RateLimiter, DefaultDirectRateLimiter, clock::DefaultClock, state::InMemoryState};
  |                                                              ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
 --> src\infrastructure\metrics.rs:4:21
  |
4 | use tracing::{info, error};
  |                     ^^^^^

warning: unused import: `std::collections::VecDeque`
 --> src\infrastructure\chunk_processor.rs:7:5
  |
7 | use std::collections::VecDeque;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\middleware\auth.rs:14:15
   |
14 | use tracing::{error, warn};
   |               ^^^^^

warning: unused imports: `HeaderValue` and `response::Response`
 --> src\middleware\cors.rs:2:20
  |
2 |     http::{header, HeaderValue, Method},
  |                    ^^^^^^^^^^^
3 |     response::Response,
  |     ^^^^^^^^^^^^^^^^^^

warning: unused imports: `HeaderMap` and `extract::Query`
  --> src\middleware\mod.rs:7:46
   |
7  |     http::{Request, StatusCode, HeaderValue, HeaderMap},
   |                                              ^^^^^^^^^
...
12 |     extract::Query,
   |     ^^^^^^^^^^^^^^

warning: unused import: `bson::oid::ObjectId`
 --> src\models\mod.rs:1:5
  |
1 | use bson::oid::ObjectId;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\models\mod.rs:2:14
  |
2 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

warning: unused import: `uuid::Uuid`
 --> src\models\mod.rs:4:5
  |
4 | use uuid::Uuid;
  |     ^^^^^^^^^^

warning: unused import: `validator::Validate`
 --> src\models\user.rs:4:5
  |
4 | use validator::Validate;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `validator::Validate`
 --> src\models\instance.rs:4:5
  |
4 | use validator::Validate;
  |     ^^^^^^^^^^^^^^^^^^^

warning: ambiguous glob re-exports
  --> src\models\mod.rs:18:9
   |
18 | pub use billing::*;
   |         ^^^^^^^^^^ the name `ServiceType` in the type namespace is first re-exported here
...
21 | pub use blueprint::*;
   |         ------------ but the name `ServiceType` in the type namespace is also re-exported here
   |
   = note: `#[warn(ambiguous_glob_reexports)]` on by default

warning: ambiguous glob re-exports
  --> src\models\mod.rs:19:9
   |
19 | pub use deployment::*;
   |         ^^^^^^^^^^^^^ the name `BuildStep` in the type namespace is first re-exported here
20 | pub use build::*;
   |         -------- but the name `BuildStep` in the type namespace is also re-exported here

warning: ambiguous glob re-exports
  --> src\models\mod.rs:19:9
   |
19 | pub use deployment::*;
   |         ^^^^^^^^^^^^^ the name `LogLevel` in the type namespace is first re-exported here
20 | pub use build::*;
   |         -------- but the name `LogLevel` in the type namespace is also re-exported here

warning: ambiguous glob re-exports
  --> src\models\mod.rs:19:9
   |
19 | pub use deployment::*;
   |         ^^^^^^^^^^^^^ the name `AutoScalingConfig` in the type namespace is first re-exported here
20 | pub use build::*;
21 | pub use blueprint::*;
   |         ------------ but the name `AutoScalingConfig` in the type namespace is also re-exported here

warning: ambiguous glob re-exports
  --> src\models\mod.rs:19:9
   |
19 | pub use deployment::*;
   |         ^^^^^^^^^^^^^ the name `HealthCheckConfig` in the type namespace is first re-exported here
20 | pub use build::*;
21 | pub use blueprint::*;
   |         ------------ but the name `HealthCheckConfig` in the type namespace is also re-exported here

warning: ambiguous glob re-exports
  --> src\models\mod.rs:19:9
   |
19 | pub use deployment::*;
   |         ^^^^^^^^^^^^^ the name `NetworkingConfig` in the type namespace is first re-exported here
20 | pub use build::*;
21 | pub use blueprint::*;
   |         ------------ but the name `NetworkingConfig` in the type namespace is also re-exported here

warning: unused import: `Level`
  --> src\observability\mod.rs:13:21
   |
13 | use tracing::{info, Level};
   |                     ^^^^^

warning: unused import: `error`
  --> src\services\auth.rs:15:15
   |
15 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\blueprint.rs:17:5
   |
17 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `BuildEvent`, `BuildPriority`, `BuildState`, `StateMachineManager`, and `WorkerStatus`
  --> src\services\build.rs:5:72
   |
5  |         CircuitBreakerService, CircuitBreakerAware, BuildStateManager, BuildState,
   |                                                                        ^^^^^^^^^^
6  |         BuildEvent, StateMachineManager, ChunkProcessor, MetricsService, RateLimiterService
   |         ^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^
...
10 |         BuildMetrics, BuildWorker, WorkerStatus, BuildQueue, BuildPriority,
   |                                    ^^^^^^^^^^^^              ^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\build.rs:22:5
   |
22 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::process::Stdio`
  --> src\services\build.rs:23:5
   |
23 | use std::process::Stdio;
   |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `tokio::process::Command`
  --> src\services\build.rs:24:5
   |
24 | use tokio::process::Command;
   |     ^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `AsyncBufReadExt` and `BufReader`
  --> src\services\build.rs:25:17
   |
25 | use tokio::io::{AsyncBufReadExt, BufReader};
   |                 ^^^^^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `warn`
  --> src\services\build.rs:26:40
   |
26 | use tracing::{error, info, instrument, warn};
   |                                        ^^^^

warning: unused import: `uuid::Uuid`
  --> src\services\build.rs:27:5
   |
27 | use uuid::Uuid;
   |     ^^^^^^^^^^

warning: unused imports: `AutoScalingConfig` and `Environment`
  --> src\services\deployment.rs:12:9
   |
12 |         Environment, EnvironmentResponse, RuntimeConfig, ServiceType, AutoScalingConfig, Pagination
   |         ^^^^^^^^^^^                                                   ^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\deployment.rs:22:5
   |
22 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\services\deployment.rs:23:15
   |
23 | use tracing::{error, info, instrument, warn};
   |               ^^^^^

warning: unused import: `Pagination`
 --> src\services\disk.rs:8:46
  |
8 |     models::{Disk, DiskStatus, DiskSnapshot, Pagination},
  |                                              ^^^^^^^^^^

warning: unused imports: `error` and `warn`
  --> src\services\disk.rs:18:15
   |
18 | use tracing::{error, info, instrument, warn};
   |               ^^^^^                    ^^^^

warning: unused imports: `error` and `warn`
  --> src\services\domain.rs:17:15
   |
17 | use tracing::{error, info, instrument, warn};
   |               ^^^^^                    ^^^^

warning: unused import: `Environment`
 --> src\services\environment.rs:4:27
  |
4 |     models::{Application, Environment},
  |                           ^^^^^^^^^^^

warning: unused import: `error`
  --> src\services\environment.rs:17:15
   |
17 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused imports: `GitCommit` and `GitUser`
 --> src\services\git.rs:6:53
  |
6 |         CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser
  |                                                     ^^^^^^^^^  ^^^^^^^

warning: unused import: `anyhow::Result`
  --> src\services\git.rs:10:5
   |
10 | use anyhow::Result;
   |     ^^^^^^^^^^^^^^

warning: unused import: `chrono::Utc`
  --> src\services\git.rs:12:5
   |
12 | use chrono::Utc;
   |     ^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\git.rs:16:5
   |
16 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `error` and `info`
  --> src\services\git.rs:17:15
   |
17 | use tracing::{error, info, instrument, warn};
   |               ^^^^^  ^^^^

warning: unused import: `error`
  --> src\services\instance.rs:14:15
   |
14 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused imports: `ApiResponse` and `CreateInstanceRequest`
 --> src\services\intelligent_hosting.rs:3:14
  |
3 |     models::{ApiResponse, ServiceError, ServiceResult},
  |              ^^^^^^^^^^^
4 |     vultr::{VultrClient, CreateInstanceRequest},
  |                          ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `anyhow::Result`
 --> src\services\intelligent_hosting.rs:6:5
  |
6 | use anyhow::Result;
  |     ^^^^^^^^^^^^^^

warning: unused imports: `error` and `warn`
 --> src\services\intelligent_hosting.rs:9:33
  |
9 | use tracing::{info, instrument, warn, error};
  |                                 ^^^^  ^^^^^

warning: unused imports: `Duration` and `sleep`
  --> src\services\intelligent_hosting.rs:10:19
   |
10 | use tokio::time::{sleep, Duration};
   |                   ^^^^^  ^^^^^^^^

warning: unused import: `ApiResponse`
 --> src\services\kubernetes_deployment.rs:3:14
  |
3 |     models::{ApiResponse, ServiceError, ServiceResult},
  |              ^^^^^^^^^^^

warning: unused import: `anyhow::Result`
 --> src\services\kubernetes_deployment.rs:6:5
  |
6 | use anyhow::Result;
  |     ^^^^^^^^^^^^^^

warning: unused import: `warn`
 --> src\services\kubernetes_deployment.rs:9:33
  |
9 | use tracing::{info, instrument, warn};
  |                                 ^^^^

warning: unused import: `crate::controllers::ControllerError`
  --> src\services\mod.rs:16:5
   |
16 | use crate::controllers::ControllerError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::config::RetryConfig`
 --> src\utils\retry.rs:1:5
  |
1 | use crate::config::RetryConfig;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `ExponentialBackoff` and `future::retry`
 --> src\utils\retry.rs:2:15
  |
2 | use backoff::{future::retry, ExponentialBackoff};
  |               ^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^

warning: unused imports: `Client`, `Request`, and `Response`
 --> src\utils\retry.rs:3:15
  |
3 | use reqwest::{Client, Request, Response};
  |               ^^^^^^  ^^^^^^^  ^^^^^^^^

warning: unused imports: `instrument` and `warn`
 --> src\utils\retry.rs:8:22
  |
8 | use tracing::{error, warn, instrument, debug};
  |                      ^^^^  ^^^^^^^^^^

warning: unused import: `DateTime`
 --> src\utils\mod.rs:3:14
  |
3 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^

warning: unused import: `Response`
 --> src\vultr\mod.rs:3:42
  |
3 | use reqwest::{header::HeaderMap, Client, Response};
  |                                          ^^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src\vultr\mod.rs:4:13
  |
4 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
 --> src\vultr\mod.rs:7:5
  |
7 | use futures::TryStreamExt;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\vultr\models.rs:1:14
  |
1 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\controllers\logs.rs:349:22
    |
349 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\controllers\logs.rs:353:57
    |
353 |                 while let Some(log) = cursor.try_next().await.map_err(|e| crate::services::ServiceError::Database(e))? {
    |                                                         ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\controllers\logs.rs:456:22
    |
456 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\controllers\logs.rs:460:57
    |
460 |                 while let Some(log) = cursor.try_next().await.map_err(|e| crate::services::ServiceError::Database(e))? {
    |                                                         ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\blueprint.rs:263:22
    |
263 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\blueprint.rs:276:22
    |
276 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\blueprint.rs:314:30
    |
314 | ...                   .await
    |                        ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\blueprint.rs:331:30
    |
331 | ...                   .await
    |                        ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\blueprint.rs:365:22
    |
365 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\build.rs:423:53
    |
423 |                 self.execute_git_clone(&job.source).await
    |                                                     ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:517:22
    |
517 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:559:22
    |
559 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:601:22
    |
601 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:754:22
    |
754 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:760:57
    |
760 |                 while let Some(app) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
    |                                                         ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:802:22
    |
802 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:828:26
    |
828 |                         .await
    |                          ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:858:22
    |
858 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:890:22
    |
890 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:917:87
    |
917 |             url: self.generate_application_url(&app.id.unwrap(), &app.runtime_config).await,
    |                                                                                       ^^^^^ only allowed inside `async` functions and 
blocks

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\blueprint.rs:115:62
    |
115 |                 self.blueprints.insert_one(&blueprint, None).await
    |                                                              ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\blueprint.rs:173:22
    |
173 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\blueprint.rs:194:22
    |
194 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\blueprint.rs:236:62
    |
236 |                 self.blueprint_syncs.insert_one(&sync, None).await
    |                                                              ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:326:22
    |
326 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\deployment.rs:364:68
    |
364 |                 self.build_service.create_build_job(build_request).await
    |                                                                    ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\disk.rs:124:52
    |
124 |                 self.disks.insert_one(&disk, None).await
    |                                                    ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\disk.rs:172:22
    |
172 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\disk.rs:213:22
    |
213 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\disk.rs:242:22
    |
242 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\disk.rs:276:22
    |
276 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\disk.rs:292:22
    |
292 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\disk.rs:318:22
    |
318 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\disk.rs:322:62
    |
322 |                 while let Some(snapshot) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
    |                                                              ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\disk.rs:365:22
    |
365 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\domain.rs:87:22
    |
87  |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\domain.rs:124:56
    |
124 |                 self.domains.insert_one(&domain, None).await
    |                                                        ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\domain.rs:167:22
    |
167 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\domain.rs:203:26
    |
203 |                         .await
    |                          ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\domain.rs:236:22
    |
236 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\domain.rs:270:22
    |
270 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0728]: `await` is only allowed inside `async` functions and blocks
   --> src\services\domain.rs:291:22
    |
291 |                     .await
    |                      ^^^^^ only allowed inside `async` functions and blocks
    |
   ::: src\infrastructure\circuit_breaker.rs:145:46
    |
145 |         $breaker_service.call($service_name, || $operation).await
    |                                              -- this is not `async`

error[E0782]: expected a type, found a trait
  --> src\infrastructure\circuit_breaker.rs:10:39
   |
10 |     breakers: Arc<DashMap<String, Arc<CircuitBreaker<ServiceError>>>>,
   |                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: you can add the `dyn` keyword if you want a trait object
   |
10 |     breakers: Arc<DashMap<String, Arc<dyn CircuitBreaker<ServiceError>>>>,
   |                                       +++

warning: unused variable: `state`
   --> src\controllers\auth.rs:113:11
    |
113 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `state`
   --> src\controllers\intelligent_hosting.rs:324:11
    |
324 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
   --> src\controllers\intelligent_hosting.rs:367:11
    |
367 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

error[E0282]: type annotations needed
  --> src\controllers\applications.rs:31:29
   |
31 |     let name = request.name.as_ref().ok_or_else(|| {
   |                             ^^^^^^   ---------- type must be known at this point
   |
help: try using a fully qualified path to specify the expected types
   |
31 -     let name = request.name.as_ref().ok_or_else(|| {
31 +     let name = <std::string::String as AsRef<T>>::as_ref(&request.name).ok_or_else(|| {
   |

error[E0599]: no method named `validate` found for struct `models::disk::Pagination` in the current scope
  --> src\controllers\applications.rs:89:16
   |
89 |     pagination.validate().map_err(|e| {
   |                ^^^^^^^^ method not found in `Pagination`
   |
  ::: src\models\disk.rs:45:1
   |
45 | pub struct Pagination {
   | --------------------- method `validate` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following trait defines an item `validate`, perhaps you need to implement it:
           candidate #1: `Validate`

error[E0308]: mismatched types
   --> src\controllers\applications.rs:121:43
    |
121 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
121 -             _ => ControllerError::Service(e.to_string()),
121 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\applications.rs:150:43
    |
150 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
150 -             _ => ControllerError::Service(e.to_string()),
150 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\applications.rs:174:43
    |
174 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
174 -             _ => ControllerError::Service(e.to_string()),
174 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\applications.rs:203:43
    |
203 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
203 -             _ => ControllerError::Service(e.to_string()),
203 +             _ => ControllerError::Service(e),
    |

error[E0599]: no method named `validate` found for struct `models::disk::Pagination` in the current scope
   --> src\controllers\applications.rs:221:16
    |
221 |     pagination.validate().map_err(|e| {
    |                ^^^^^^^^ method not found in `Pagination`
    |
   ::: src\models\disk.rs:45:1
    |
45  | pub struct Pagination {
    | --------------------- method `validate` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `validate`, perhaps you need to implement it:
            candidate #1: `Validate`

error[E0308]: mismatched types
   --> src\controllers\applications.rs:232:43
    |
232 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
232 -             _ => ControllerError::Service(e.to_string()),
232 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\applications.rs:260:43
    |
260 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
260 -             _ => ControllerError::Service(e.to_string()),
260 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\applications.rs:288:43
    |
288 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
288 -             _ => ControllerError::Service(e.to_string()),
288 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\environment.rs:175:43
    |
175 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
175 -             _ => ControllerError::Service(e.to_string()),
175 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\environment.rs:227:43
    |
227 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
227 -             _ => ControllerError::Service(e.to_string()),
227 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\environment.rs:268:43
    |
268 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
268 -             _ => ControllerError::Service(e.to_string()),
268 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\environment.rs:296:43
    |
296 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
296 -             _ => ControllerError::Service(e.to_string()),
296 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\environment.rs:324:43
    |
324 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
324 -             _ => ControllerError::Service(e.to_string()),
324 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\environment.rs:357:43
    |
357 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
357 -             _ => ControllerError::Service(e.to_string()),
357 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
  --> src\controllers\logs.rs:73:43
   |
73 |             _ => ControllerError::Service(e.to_string()),
   |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
   |                  |
   |                  arguments to this enum variant are incorrect
   |
note: tuple variant defined here
  --> src\controllers\mod.rs:51:5
   |
51 |     Service(services::ServiceError),
   |     ^^^^^^^
help: try removing the method call
   |
73 -             _ => ControllerError::Service(e.to_string()),
73 +             _ => ControllerError::Service(e),
   |

error[E0308]: mismatched types
   --> src\controllers\logs.rs:106:43
    |
106 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
106 -             _ => ControllerError::Service(e.to_string()),
106 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\logs.rs:135:43
    |
135 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
135 -             _ => ControllerError::Service(e.to_string()),
135 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\logs.rs:168:43
    |
168 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
168 -             _ => ControllerError::Service(e.to_string()),
168 +             _ => ControllerError::Service(e),
    |

error[E0308]: mismatched types
   --> src\controllers\logs.rs:203:43
    |
203 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
203 -             _ => ControllerError::Service(e.to_string()),
203 +             _ => ControllerError::Service(e),
    |

error[E0609]: no field `start_time` on type `LogsQuery`
   --> src\controllers\logs.rs:315:41
    |
315 |         if let Some(start_time) = query.start_time {
    |                                         ^^^^^^^^^^ unknown field
    |
    = note: available fields are: `cursor`, `limit`, `follow`, `level`, `source`

error[E0609]: no field `end_time` on type `LogsQuery`
   --> src\controllers\logs.rs:318:39
    |
318 |         if let Some(end_time) = query.end_time {
    |                                       ^^^^^^^^ unknown field
    |
    = note: available fields are: `cursor`, `limit`, `follow`, `level`, `source`

error[E0609]: no field `search` on type `LogsQuery`
   --> src\controllers\logs.rs:334:38
    |
334 |         if let Some(search) = &query.search {
    |                                      ^^^^^^ unknown field
    |
    = note: available fields are: `cursor`, `limit`, `follow`, `level`, `source`

error[E0609]: no field `timestamp` on type `&_`
   --> src\controllers\logs.rs:369:49
    |
369 |             Some(logs_to_return.last().unwrap().timestamp.to_rfc3339())
    |                                                 ^^^^^^^^^ unknown field

error[E0609]: no field `start_time` on type `LogsQuery`
   --> src\controllers\logs.rs:422:41
    |
422 |         if let Some(start_time) = query.start_time {
    |                                         ^^^^^^^^^^ unknown field
    |
    = note: available fields are: `cursor`, `limit`, `follow`, `level`, `source`

error[E0609]: no field `end_time` on type `LogsQuery`
   --> src\controllers\logs.rs:425:39
    |
425 |         if let Some(end_time) = query.end_time {
    |                                       ^^^^^^^^ unknown field
    |
    = note: available fields are: `cursor`, `limit`, `follow`, `level`, `source`

error[E0609]: no field `search` on type `LogsQuery`
   --> src\controllers\logs.rs:441:38
    |
441 |         if let Some(search) = &query.search {
    |                                      ^^^^^^ unknown field
    |
    = note: available fields are: `cursor`, `limit`, `follow`, `level`, `source`

error[E0609]: no field `timestamp` on type `&_`
   --> src\controllers\logs.rs:476:49
    |
476 |             Some(logs_to_return.last().unwrap().timestamp.to_rfc3339())
    |                                                 ^^^^^^^^^ unknown field

error[E0308]: mismatched types
   --> src\controllers\webhooks.rs:137:43
    |
137 |             _ => ControllerError::Service(e.to_string()),
    |                  ------------------------ ^^^^^^^^^^^^^ expected `ServiceError`, found `String`
    |                  |
    |                  arguments to this enum variant are incorrect
    |
note: tuple variant defined here
   --> src\controllers\mod.rs:51:5
    |
51  |     Service(services::ServiceError),
    |     ^^^^^^^
help: try removing the method call
    |
137 -             _ => ControllerError::Service(e.to_string()),
137 +             _ => ControllerError::Service(e),
    |

error[E0782]: expected a type, found a trait
  --> src\infrastructure\circuit_breaker.rs:20:68
   |
20 |     pub fn get_or_create_breaker(&self, service_name: &str) -> Arc<CircuitBreaker<ServiceError>> {
   |                                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: you can add the `dyn` keyword if you want a trait object
   |
20 |     pub fn get_or_create_breaker(&self, service_name: &str) -> Arc<dyn CircuitBreaker<ServiceError>> {
   |                                                                    +++

error[E0599]: no variant or associated item named `TimedOut` found for enum `failsafe::Error` in the current scope
  --> src\infrastructure\circuit_breaker.rs:73:32
   |
73 |             Err(FailsafeError::TimedOut) => {
   |                                ^^^^^^^^ variant or associated item not found in `Error<_>`

error[E0599]: no variant or associated item named `InnerError` found for enum `failsafe::Error` in the current scope
  --> src\infrastructure\circuit_breaker.rs:80:32
   |
80 |             Err(FailsafeError::InnerError(e)) => {
   |                                ^^^^^^^^^^ variant or associated item not found in `Error<_>`

warning: unused variable: `chunk`
   --> src\infrastructure\rate_limiter.rs:168:13
    |
168 |         for chunk in requests.chunks(5) {
    |             ^^^^^ help: if this is intentional, prefix it with an underscore: `_chunk`

warning: unused variable: `chunk`
   --> src\infrastructure\rate_limiter.rs:184:13
    |
184 |         for chunk in requests.chunks(10) {
    |             ^^^^^ help: if this is intentional, prefix it with an underscore: `_chunk`

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `rand`
   --> src\infrastructure\rate_limiter.rs:417:23
    |
417 |         let mut rng = rand::thread_rng();
    |                       ^^^^ use of unresolved module or unlinked crate `rand`
    |
    = help: if you wanted to use a crate named `rand`, use `cargo add rand` to add it to your `Cargo.toml`

error[E0308]: mismatched types
   --> src\infrastructure\rate_limiter.rs:504:36
    |
504 |       if let Some(user_id) = user_id {
    |  ____________________________________^
505 | |         rate_limiter.check_api_rate_limit(&user_id).await?;
506 | |     } else {
    | |_____^ expected `Result<(), ServiceError>`, found `()`
    |
    = note:   expected enum `Result<(), services::ServiceError>`
            found unit type `()`

error[E0382]: borrow of partially moved value: `current_version`
   --> src\infrastructure\schema_migration.rs:179:36
    |
162 |         if let Some(current) = current_version {
    |                     ------- value partially moved here
...
179 |             if let Some(current) = &current_version {
    |                                    ^^^^^^^^^^^^^^^^ value borrowed here after partial move
    |
    = note: partial move occurs because value has type `semver::Version`, which does not implement the `Copy` trait
help: borrow this binding in the pattern to avoid moving the value
    |
162 |         if let Some(ref current) = current_version {
    |                     +++

error[E0599]: no method named `try_next` found for struct `mongodb::Cursor` in the current scope
   --> src\infrastructure\schema_migration.rs:610:41
    |
610 |         while let Some(record) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
    |                                         ^^^^^^^^ method not found in `Cursor<MigrationRecord>`
    |
    = help: items from traits can only be used if the trait is in scope
help: the following traits which provide `try_next` are implemented but not in scope; perhaps you want to import one of them
    |
1   + use futures::TryStreamExt;
    |
1   + use tokio_stream::stream_ext::StreamExt;
    |

error[E0609]: no field `applied_at` on type `&_`
   --> src\infrastructure\schema_migration.rs:615:34
    |
615 |         records.sort_by(|a, b| a.applied_at.cmp(&b.applied_at));
    |                                  ^^^^^^^^^^ unknown field

error[E0382]: borrow of moved value: `items`
   --> src\infrastructure\chunk_processor.rs:168:67
    |
144 |     async fn send_to_dead_letter_queue<T: Serialize>(&self, queue_name: &str, items: Vec<T>, error: String) {
    |                                                                               ----- move occurs because `items` has type `Vec<T>`, which 
does not implement the `Copy` trait
145 |         for (index, item) in items.into_iter().enumerate() {
    |                                    ----------- `items` moved due to this method call
...
168 |         warn!("Sent {} items to dead letter queue for queue: {}", items.len(), queue_name);
    |                                                                   ^^^^^ value borrowed here after move
    |
note: `into_iter` takes ownership of the receiver `self`, which moves `items`
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\iter\traits\collect.rs:313:18
    |
313 |     fn into_iter(self) -> Self::IntoIter;
    |                  ^^^^
help: you could `clone` the value and consume it, if the `T: Clone` trait bound could be satisfied
    |
145 |         for (index, item) in items.clone().into_iter().enumerate() {
    |                                   ++++++++

error[E0308]: mismatched types
  --> src\infrastructure\database_indexes.rs:23:13
   |
23 |             self.create_application_indexes(),
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected future, found a different future
   |
   = help: consider `await`ing on both `Future`s
   = note: distinct uses of `impl Trait` result in different opaque types

warning: unused variable: `collection`
  --> src\infrastructure\database_indexes.rs:54:13
   |
54 |         let collection = self.database.collection::<Document>("users");
   |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
  --> src\infrastructure\database_indexes.rs:99:13
   |
99 |         let collection = self.database.collection::<Document>("applications");
   |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
   --> src\infrastructure\database_indexes.rs:161:13
    |
161 |         let collection = self.database.collection::<Document>("deployments");
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
   --> src\infrastructure\database_indexes.rs:223:13
    |
223 |         let collection = self.database.collection::<Document>("build_jobs");
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
   --> src\infrastructure\database_indexes.rs:290:13
    |
290 |         let collection = self.database.collection::<Document>("environment_groups");
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

error[E0716]: temporary value dropped while borrowed
   --> src\infrastructure\database_indexes.rs:445:29
    |
445 |                 .unwrap_or(&format!("index_{}", attempt));
    |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ - temporary value is freed at the end of this statement
    |                             |
    |                             creates a temporary value which is freed while still in use
...
453 |                         info!("Created index '{}' on collection '{}'", index_name, collection_name);
    |                                                                        ---------- borrow later used here
    |
    = note: consider using a `let` binding to create a longer lived value
    = note: this error originates in the macro `format` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0599]: no method named `get_str` found for struct `IndexModel` in the current scope
   --> src\infrastructure\database_indexes.rs:511:53
    |
511 |                         if let Ok(name) = index_doc.get_str("name") {
    |                                                     ^^^^^^^ method not found in `IndexModel`
    |
help: one of the expressions' fields has a method of the same name
    |
511 |                         if let Ok(name) = index_doc.keys.get_str("name") {
    |                                                     +++++

error[E0599]: no method named `get_document` found for struct `IndexModel` in the current scope
   --> src\infrastructure\database_indexes.rs:515:49
    |
515 | ...                   keys: index_doc.get_document("key").unwrap_or(&doc! {}).clone(),
    |                                       ^^^^^^^^^^^^ method not found in `IndexModel`
    |
help: one of the expressions' fields has a method of the same name
    |
515 |                                 keys: index_doc.keys.get_document("key").unwrap_or(&doc! {}).clone(),
    |                                                 +++++

error[E0599]: no method named `get_bool` found for struct `IndexModel` in the current scope
   --> src\infrastructure\database_indexes.rs:516:51
    |
516 | ...                   unique: index_doc.get_bool("unique").unwrap_or(false),
    |                                         ^^^^^^^^ method not found in `IndexModel`
    |
help: one of the expressions' fields has a method of the same name
    |
516 |                                 unique: index_doc.keys.get_bool("unique").unwrap_or(false),
    |                                                   +++++

error[E0599]: no method named `get_bool` found for struct `IndexModel` in the current scope
   --> src\infrastructure\database_indexes.rs:517:55
    |
517 | ...                   background: index_doc.get_bool("background").unwrap_or(false),
    |                                             ^^^^^^^^ method not found in `IndexModel`
    |
help: one of the expressions' fields has a method of the same name
    |
517 |                                 background: index_doc.keys.get_bool("background").unwrap_or(false),
    |                                                       +++++

warning: unused variable: `method`
   --> src\observability\mod.rs:108:9
    |
108 |     let method = req.method().clone();
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_method`

warning: unused variable: `path`
   --> src\observability\mod.rs:109:9
    |
109 |     let path = req.uri().path().to_string();
    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_path`

warning: unused variable: `duration`
   --> src\observability\mod.rs:113:9
    |
113 |     let duration = start.elapsed();
    |         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_duration`

warning: unused variable: `status`
   --> src\observability\mod.rs:114:9
    |
114 |     let status = response.status().as_u16();
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_status`

error[E0609]: no field `database` on type `&AdminService`
   --> src\services\admin.rs:255:18
    |
255 |             self.database.collection("applications");
    |                  ^^^^^^^^ unknown field
    |
    = note: available fields are: `users`, `instances`

error[E0609]: no field `database` on type `&AdminService`
   --> src\services\admin.rs:267:18
    |
267 |             self.database.collection("deployments");
    |                  ^^^^^^^^ unknown field
    |
    = note: available fields are: `users`, `instances`

error[E0609]: no field `config` on type `&AdminService`
   --> src\services\admin.rs:320:37
    |
320 |                 "environment": self.config.environment.clone().unwrap_or_else(|| "production".to_string())
    |                                     ^^^^^^ unknown field
    |
    = note: available fields are: `users`, `instances`

error[E0609]: no field `database` on type `&BillingService`
   --> src\services\billing.rs:267:18
    |
267 |             self.database.collection("applications");
    |                  ^^^^^^^^ unknown field
    |
    = note: available fields are: `billing_accounts`, `invoices`, `usage_records`

error[E0609]: no field `database` on type `&BillingService`
   --> src\services\billing.rs:281:18
    |
281 |             self.database.collection("databases");
    |                  ^^^^^^^^ unknown field
    |
    = note: available fields are: `billing_accounts`, `invoices`, `usage_records`

error[E0609]: no field `database` on type `&BillingService`
   --> src\services\billing.rs:295:18
    |
295 |             self.database.collection("disks");
    |                  ^^^^^^^^ unknown field
    |
    = note: available fields are: `billing_accounts`, `invoices`, `usage_records`

error[E0609]: no field `plan` on type `models::deployment::RuntimeConfig`
   --> src\services\billing.rs:316:50
    |
316 |         let base_cost = match app.runtime_config.plan.as_str() {
    |                                                  ^^^^ unknown field
    |
    = note: available fields are: `service_type`, `instance_type`, `region`, `auto_scaling`, `health_check`, `networking`

error[E0609]: no field `instances` on type `models::deployment::RuntimeConfig`
   --> src\services\billing.rs:326:51
    |
326 |         let instance_cost = if app.runtime_config.instances > 1 {
    |                                                   ^^^^^^^^^ unknown field
    |
    = note: available fields are: `service_type`, `instance_type`, `region`, `auto_scaling`, `health_check`, `networking`

error[E0609]: no field `instances` on type `models::deployment::RuntimeConfig`
   --> src\services\billing.rs:327:33
    |
327 |             (app.runtime_config.instances - 1) as f64 * base_cost * 0.8 // 20% discount for additional instances
    |                                 ^^^^^^^^^ unknown field
    |
    = note: available fields are: `service_type`, `instance_type`, `region`, `auto_scaling`, `health_check`, `networking`

error[E0609]: no field `memory_mb` on type `models::deployment::RuntimeConfig`
   --> src\services\billing.rs:333:49
    |
333 |         let memory_cost = if app.runtime_config.memory_mb > 512 {
    |                                                 ^^^^^^^^^ unknown field
    |
    = note: available fields are: `service_type`, `instance_type`, `region`, `auto_scaling`, `health_check`, `networking`

error[E0609]: no field `memory_mb` on type `models::deployment::RuntimeConfig`
   --> src\services\billing.rs:334:48
    |
334 |             let extra_gb = (app.runtime_config.memory_mb - 512) as f64 / 1024.0;
    |                                                ^^^^^^^^^ unknown field
    |
    = note: available fields are: `service_type`, `instance_type`, `region`, `auto_scaling`, `health_check`, `networking`

error[E0599]: no method named `with_day` found for struct `chrono::DateTime` in the current scope
   --> src\services\billing.rs:368:14
    |
367 |           let start_of_month = chrono::Utc::now()
    |  ______________________________-
368 | |             .with_day(1)
    | |_____________-^^^^^^^^
    |
   ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\chrono-0.4.41\src\traits.rs:196:8
    |
196 |       fn with_day(&self, day: u32) -> Option<Self>;
    |          -------- the method is available for `chrono::DateTime<Utc>` here
    |
    = help: items from traits can only be used if the trait is in scope
help: trait `Datelike` which provides `with_day` is implemented but not in scope; perhaps you want to import it
    |
1   + use chrono::Datelike;
    |
help: there is a method `with_day0` with a similar name
    |
368 |             .with_day0(1)
    |                      +

error[E0609]: no field `database` on type `&BillingService`
   --> src\services\billing.rs:379:18
    |
379 |             self.database.collection("bandwidth_metrics");
    |                  ^^^^^^^^ unknown field
    |
    = note: available fields are: `billing_accounts`, `invoices`, `usage_records`

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `serde_yaml`
   --> src\services\blueprint.rs:390:43
    |
390 |         let achidas_config: AchidasYaml = serde_yaml::from_str(&yaml_content)
    |                                           ^^^^^^^^^^ use of unresolved module or unlinked crate `serde_yaml`
    |
    = help: if you wanted to use a crate named `serde_yaml`, use `cargo add serde_yaml` to add it to your `Cargo.toml`

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `base64`
   --> src\services\blueprint.rs:479:29
    |
479 |         let content_bytes = base64::decode(content_b64.replace('\n', ""))
    |                             ^^^^^^ use of unresolved module or unlinked crate `base64`
    |
    = help: if you wanted to use a crate named `base64`, use `cargo add base64` to add it to your `Cargo.toml`

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `urlencoding`
   --> src\services\blueprint.rs:487:28
    |
487 |         let encoded_path = urlencoding::encode(file_path);
    |                            ^^^^^^^^^^^ use of unresolved module or unlinked crate `urlencoding`
    |
    = help: if you wanted to use a crate named `urlencoding`, use `cargo add urlencoding` to add it to your `Cargo.toml`

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `base64`
   --> src\services\blueprint.rs:518:29
    |
518 |         let content_bytes = base64::decode(content_b64)
    |                             ^^^^^^ use of unresolved module or unlinked crate `base64`
    |
    = help: if you wanted to use a crate named `base64`, use `cargo add base64` to add it to your `Cargo.toml`

error[E0308]: mismatched types
   --> src\services\blueprint.rs:560:17
    |
560 |                 self.sync_resource_chunk(blueprint_id, resource_chunk)
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `Result<Vec<_>, ServiceError>`, found future

warning: unused variable: `blueprint_id`
   --> src\services\blueprint.rs:668:43
    |
668 |     async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {
    |                                           ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_blueprint_id`

warning: unused variable: `name`
   --> src\services\blueprint.rs:668:67
    |
668 |     async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {
    |                                                                   ^^^^ help: if this is intentional, prefix it with an underscore: 
`_name`

warning: unused variable: `blueprint_id`
   --> src\services\blueprint.rs:681:36
    |
681 |     async fn create_service(&self, blueprint_id: ObjectId, config: &serde_json::Value) -> ServiceResult<String> {
    |                                    ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_blueprint_id`

error[E0599]: no variant or associated item named `InstallDependencies` found for enum `models::deployment::BuildStep` in the current scope
   --> src\services\build.rs:299:48
    |
299 |         self.log_build_step(job_id, BuildStep::InstallDependencies, LogLevel::Info, "Installing dependencies...").await?;
    |                                                ^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `BuildStep`
    |
   ::: src\models\deployment.rs:159:1
    |
159 | pub enum BuildStep {
    | ------------------ variant or associated item `InstallDependencies` not found for this enum

error[E0277]: the trait bound `Bson: From<models::build::BuildArtifact>` is not satisfied
   --> src\services\build.rs:322:17
    |
322 | /                 doc! { 
323 | |                     "$set": { 
324 | |                         "status": "Success",
325 | |                         "completed_at": Utc::now(),
...   |
333 | |                 },
    | |_________________^ the trait `From<models::build::BuildArtifact>` is not implemented for `Bson`
    |
    = help: the following other types implement trait `From<T>`:
              `Bson` implements `From<&T>`
              `Bson` implements `From<&[T]>`
              `Bson` implements `From<&mut T>`
              `Bson` implements `From<&str>`
              `Bson` implements `From<Regex>`
              `Bson` implements `From<Vec<T>>`
              `Bson` implements `From<[u8; 12]>`
              `Bson` implements `From<bool>`
            and 22 others
    = note: required for `models::build::BuildArtifact` to implement `Into<Bson>`
    = note: required for `Bson` to implement `From<Vec<models::build::BuildArtifact>>`
    = note: 3 redundant requirements hidden
    = note: required for `&Vec<models::build::BuildArtifact>` to implement `Into<Bson>`
    = note: this error originates in the macro `$crate::bson` which comes from the expansion of the macro `doc` (in Nightly builds, run with 
-Z macro-backtrace for more info)

error[E0308]: mismatched types
   --> src\services\build.rs:395:13
    |
395 |             level,
    |             ^^^^^ expected `models::build::LogLevel`, found `models::deployment::LogLevel`
    |
    = note: `models::deployment::LogLevel` and `models::build::LogLevel` have similar names, but are actually distinct types
note: `models::deployment::LogLevel` is defined in module `crate::models::deployment` of the current crate
   --> src\models\deployment.rs:178:1
    |
178 | pub enum LogLevel {
    | ^^^^^^^^^^^^^^^^^
note: `models::build::LogLevel` is defined in module `crate::models::build` of the current crate
   --> src\models\build.rs:99:1
    |
99  | pub enum LogLevel {
    | ^^^^^^^^^^^^^^^^^

error[E0308]: mismatched types
   --> src\services\build.rs:397:13
    |
397 |             step,
    |             ^^^^ expected `models::build::BuildStep`, found `models::deployment::BuildStep`
    |
    = note: `models::deployment::BuildStep` and `models::build::BuildStep` have similar names, but are actually distinct types
note: `models::deployment::BuildStep` is defined in module `crate::models::deployment` of the current crate
   --> src\models\deployment.rs:159:1
    |
159 | pub enum BuildStep {
    | ^^^^^^^^^^^^^^^^^^
note: `models::build::BuildStep` is defined in module `crate::models::build` of the current crate
   --> src\models\build.rs:107:1
    |
107 | pub enum BuildStep {
    | ^^^^^^^^^^^^^^^^^^

error[E0277]: the trait bound `Bson: From<models::build::BuildLogEntry>` is not satisfied
   --> src\services\build.rs:404:17
    |
404 |                 doc! { "$push": { "logs": &log_entry } },
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `From<models::build::BuildLogEntry>` is not implemented for `Bson`
    |
    = help: the following other types implement trait `From<T>`:
              `Bson` implements `From<&T>`
              `Bson` implements `From<&[T]>`
              `Bson` implements `From<&mut T>`
              `Bson` implements `From<&str>`
              `Bson` implements `From<Regex>`
              `Bson` implements `From<Vec<T>>`
              `Bson` implements `From<[u8; 12]>`
              `Bson` implements `From<bool>`
            and 22 others
    = note: required for `models::build::BuildLogEntry` to implement `Into<Bson>`
    = note: required for `Bson` to implement `From<&models::build::BuildLogEntry>`
    = note: 1 redundant requirement hidden
    = note: required for `&models::build::BuildLogEntry` to implement `Into<Bson>`
    = note: this error originates in the macro `$crate::bson` which comes from the expansion of the macro `doc` (in Nightly builds, run with 
-Z macro-backtrace for more info)

warning: unused variable: `metadata`
   --> src\services\build.rs:808:25
    |
808 |                     let metadata = entry.metadata().await
    |                         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_metadata`

warning: unused variable: `deployment_id`
   --> src\services\deployment.rs:383:9
    |
383 |         deployment_id: ObjectId,
    |         ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_deployment_id`

warning: unused variable: `build_job_id`
   --> src\services\deployment.rs:384:9
    |
384 |         build_job_id: String,
    |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_build_job_id`

warning: unused variable: `state_manager`
   --> src\services\deployment.rs:385:13
    |
385 |         mut state_manager: DeploymentStateManager,
    |             ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_state_manager`

warning: variable does not need to be mutable
   --> src\services\deployment.rs:385:9
    |
385 |         mut state_manager: DeploymentStateManager,
    |         ----^^^^^^^^^^^^^
    |         |
    |         help: remove this `mut`
    |
    = note: `#[warn(unused_mut)]` on by default

warning: unused variable: `application`
   --> src\services\deployment.rs:465:46
    |
465 |     async fn determine_build_priority(&self, application: &Application) -> crate::models::BuildPriority {
    |                                              ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_application`

error[E0599]: no variant or associated item named `WebService` found for enum `models::billing::ServiceType` in the current scope
   --> src\services\deployment.rs:676:26
    |
676 |             ServiceType::WebService => {
    |                          ^^^^^^^^^^ variant or associated item not found in `ServiceType`
    |
   ::: src\models\billing.rs:54:1
    |
54  | pub enum ServiceType {
    | -------------------- variant or associated item `WebService` not found for this enum

error[E0599]: no variant or associated item named `BackgroundWorker` found for enum `models::billing::ServiceType` in the current scope
   --> src\services\deployment.rs:680:26
    |
680 |             ServiceType::BackgroundWorker => {
    |                          ^^^^^^^^^^^^^^^^ variant or associated item not found in `ServiceType`
    |
   ::: src\models\billing.rs:54:1
    |
54  | pub enum ServiceType {
    | -------------------- variant or associated item `BackgroundWorker` not found for this enum

error[E0599]: no variant named `CronJob` found for enum `models::billing::ServiceType`
   --> src\services\deployment.rs:684:26
    |
684 |             ServiceType::CronJob { .. } => {
    |                          ^^^^^^^ variant not found in `models::billing::ServiceType`
    |
   ::: src\models\billing.rs:54:1
    |
54  | pub enum ServiceType {
    | -------------------- variant `CronJob` not found here

error[E0599]: no variant or associated item named `StaticSite` found for enum `models::billing::ServiceType` in the current scope
   --> src\services\deployment.rs:688:26
    |
688 |             ServiceType::StaticSite => {
    |                          ^^^^^^^^^^ variant or associated item not found in `ServiceType`
    |
   ::: src\models\billing.rs:54:1
    |
54  | pub enum ServiceType {
    | -------------------- variant or associated item `StaticSite` not found for this enum

error[E0599]: no variant or associated item named `WebService` found for enum `models::billing::ServiceType` in the current scope
   --> src\services\deployment.rs:721:26
    |
721 |             ServiceType::WebService | ServiceType::StaticSite => {
    |                          ^^^^^^^^^^ variant or associated item not found in `ServiceType`
    |
   ::: src\models\billing.rs:54:1
    |
54  | pub enum ServiceType {
    | -------------------- variant or associated item `WebService` not found for this enum

error[E0599]: no variant or associated item named `StaticSite` found for enum `models::billing::ServiceType` in the current scope
   --> src\services\deployment.rs:721:52
    |
721 |             ServiceType::WebService | ServiceType::StaticSite => {
    |                                                    ^^^^^^^^^^ variant or associated item not found in `ServiceType`
    |
   ::: src\models\billing.rs:54:1
    |
54  | pub enum ServiceType {
    | -------------------- variant or associated item `StaticSite` not found for this enum

error[E0277]: `models::deployment::ApplicationResponse` is not an iterator
    --> src\services\deployment.rs:779:38
     |
779  |         Ok(app_responses.into_iter().flatten().collect())
     |                                      ^^^^^^^ `models::deployment::ApplicationResponse` is not an iterator
     |
     = help: the trait `Iterator` is not implemented for `models::deployment::ApplicationResponse`
     = note: required for `models::deployment::ApplicationResponse` to implement `IntoIterator`
note: required by a bound in `std::iter::Iterator::flatten`
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\iter\traits\iterator.rs:1517:21
     |
1514 |     fn flatten(self) -> Flatten<Self>
     |        ------- required by a bound in this associated function
...
1517 |         Self::Item: IntoIterator,
     |                     ^^^^^^^^^^^^ required by this bound in `Iterator::flatten`

error[E0599]: the method `collect` exists for struct `Flatten<IntoIter<ApplicationResponse>>`, but its trait bounds were not satisfied
   --> src\services\deployment.rs:779:48
    |
779 |         Ok(app_responses.into_iter().flatten().collect())
    |                                                ^^^^^^^ method cannot be called on `Flatten<IntoIter<ApplicationResponse>>` due to 
unsatisfied trait bounds
    |
   ::: src\models\deployment.rs:252:1
    |
252 | pub struct ApplicationResponse {
    | ------------------------------ doesn't satisfy `<_ as IntoIterator>::IntoIter = _`, `<_ as IntoIterator>::Item = _` or `_: IntoIterator`
    |
   ::: C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\iter\adapters\flatten.rs:239:1
    |
239 | pub struct Flatten<I: Iterator<Item: IntoIterator>> {
    | --------------------------------------------------- doesn't satisfy `_: Iterator`
    |
    = note: the following trait bounds were not satisfied:
            `<models::deployment::ApplicationResponse as IntoIterator>::IntoIter = _`
            which is required by `std::iter::Flatten<std::vec::IntoIter<models::deployment::ApplicationResponse>>: Iterator`
            `<models::deployment::ApplicationResponse as IntoIterator>::Item = _`
            which is required by `std::iter::Flatten<std::vec::IntoIter<models::deployment::ApplicationResponse>>: Iterator`
            `models::deployment::ApplicationResponse: IntoIterator`
            which is required by `std::iter::Flatten<std::vec::IntoIter<models::deployment::ApplicationResponse>>: Iterator`
            `std::iter::Flatten<std::vec::IntoIter<models::deployment::ApplicationResponse>>: Iterator`
            which is required by `&mut std::iter::Flatten<std::vec::IntoIter<models::deployment::ApplicationResponse>>: Iterator`
note: the trait `IntoIterator` must be implemented
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\iter\traits\collect.rs:285:1
    |
285 | pub trait IntoIterator {
    | ^^^^^^^^^^^^^^^^^^^^^^

warning: unused variable: `request`
   --> src\services\disk.rs:408:73
    |
408 |     async fn update_infrastructure_disk(&self, infrastructure_id: &str, request: &UpdateDiskRequest) -> ServiceResult<()> {
    |                                                                         ^^^^^^^ help: if this is intentional, prefix it with an 
underscore: `_request`

error[E0277]: the trait bound `Bson: From<EnvironmentVariable>` is not satisfied
   --> src\services\environment.rs:237:17
    |
237 | /                 doc! { 
238 | |                     "$set": { 
239 | |                         format!("variables.{}", key): &env_var,
240 | |                         "updated_at": now
241 | |                     }
242 | |                 },
    | |_________________^ the trait `From<EnvironmentVariable>` is not implemented for `Bson`
    |
    = help: the following other types implement trait `From<T>`:
              `Bson` implements `From<&T>`
              `Bson` implements `From<&[T]>`
              `Bson` implements `From<&mut T>`
              `Bson` implements `From<&str>`
              `Bson` implements `From<Regex>`
              `Bson` implements `From<Vec<T>>`
              `Bson` implements `From<[u8; 12]>`
              `Bson` implements `From<bool>`
            and 22 others
    = note: required for `EnvironmentVariable` to implement `Into<Bson>`
    = note: required for `Bson` to implement `From<&EnvironmentVariable>`
    = note: 1 redundant requirement hidden
    = note: required for `&EnvironmentVariable` to implement `Into<Bson>`
    = note: this error originates in the macro `$crate::bson` which comes from the expansion of the macro `doc` (in Nightly builds, run with 
-Z macro-backtrace for more info)

error[E0277]: the trait bound `Bson: From<Secret>` is not satisfied
   --> src\services\environment.rs:282:17
    |
282 | /                 doc! { 
283 | |                     "$set": { 
284 | |                         format!("secrets.{}", key): &secret,
285 | |                         "updated_at": now
286 | |                     }
287 | |                 },
    | |_________________^ the trait `From<Secret>` is not implemented for `Bson`
    |
    = help: the following other types implement trait `From<T>`:
              `Bson` implements `From<&T>`
              `Bson` implements `From<&[T]>`
              `Bson` implements `From<&mut T>`
              `Bson` implements `From<&str>`
              `Bson` implements `From<Regex>`
              `Bson` implements `From<Vec<T>>`
              `Bson` implements `From<[u8; 12]>`
              `Bson` implements `From<bool>`
            and 22 others
    = note: required for `Secret` to implement `Into<Bson>`
    = note: required for `Bson` to implement `From<&Secret>`
    = note: 1 redundant requirement hidden
    = note: required for `&Secret` to implement `Into<Bson>`
    = note: this error originates in the macro `$crate::bson` which comes from the expansion of the macro `doc` (in Nightly builds, run with 
-Z macro-backtrace for more info)

error[E0599]: no method named `try_next` found for struct `mongodb::Cursor` in the current scope
   --> src\services\environment.rs:427:40
    |
427 |         while let Some(group) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
    |                                        ^^^^^^^^ method not found in `Cursor<EnvironmentGroup>`
    |
    = help: items from traits can only be used if the trait is in scope
help: the following traits which provide `try_next` are implemented but not in scope; perhaps you want to import one of them
    |
1   + use futures::TryStreamExt;
    |
1   + use tokio_stream::stream_ext::StreamExt;
    |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `urlencoding`
   --> src\services\git.rs:122:28
    |
122 |         let encoded_path = urlencoding::encode(&project_path);
    |                            ^^^^^^^^^^^ use of unresolved module or unlinked crate `urlencoding`
    |
    = help: if you wanted to use a crate named `urlencoding`, use `cargo add urlencoding` to add it to your `Cargo.toml`

warning: unused variable: `application_id`
   --> src\services\git.rs:249:42
    |
249 |     async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> 
ServiceResult<Strin...
    |                                          ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_application_id`

warning: unused variable: `repository`
   --> src\services\git.rs:249:69
    |
249 | ...plication_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {
    |                             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_repository`

warning: unused variable: `webhook_secret`
   --> src\services\git.rs:249:94
    |
249 | ...sitory: &Repository, webhook_secret: &str) -> ServiceResult<String> {
    |                         ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_webhook_secret`

warning: unused variable: `application_id`
   --> src\services\git.rs:257:45
    |
257 | ...ucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {
    |                         ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_application_id`

warning: unused variable: `repository`
   --> src\services\git.rs:257:72
    |
257 | ...plication_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {
    |                             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_repository`

warning: unused variable: `webhook_secret`
   --> src\services\git.rs:257:97
    |
257 | ...sitory: &Repository, webhook_secret: &str) -> ServiceResult<String> {
    |                         ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_webhook_secret`

warning: unused variable: `applications`
   --> src\services\git.rs:278:13
    |
278 |         let applications = self.applications
    |             ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_applications`

warning: variable does not need to be mutable
   --> src\services\git.rs:286:13
    |
286 |         let mut triggered_deployments = Vec::new();
    |             ----^^^^^^^^^^^^^^^^^^^^^
    |             |
    |             help: remove this `mut`

error[E0277]: the trait bound `PoolType: std::cmp::Eq` is not satisfied
    --> src\services\intelligent_hosting.rs:899:20
     |
899  |             status.insert(pool_type.clone(), pool_status);
     |                    ^^^^^^ the trait `std::cmp::Eq` is not implemented for `PoolType`
     |
note: required by a bound in `HashMap::<K, V, S>::insert`
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\std\src\collections\hash\map.rs:752:8
     |
752  |     K: Eq + Hash,
     |        ^^ required by this bound in `HashMap::<K, V, S>::insert`
...
1189 |     pub fn insert(&mut self, k: K, v: V) -> Option<V> {
     |            ------ required by a bound in this associated function
help: consider annotating `PoolType` with `#[derive(Eq)]`
     |
68   + #[derive(Eq)]
69   | pub enum PoolType {
     |

error[E0277]: the trait bound `PoolType: Hash` is not satisfied
    --> src\services\intelligent_hosting.rs:899:20
     |
899  |             status.insert(pool_type.clone(), pool_status);
     |                    ^^^^^^ the trait `Hash` is not implemented for `PoolType`
     |
note: required by a bound in `HashMap::<K, V, S>::insert`
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\std\src\collections\hash\map.rs:752:13
     |
752  |     K: Eq + Hash,
     |             ^^^^ required by this bound in `HashMap::<K, V, S>::insert`
...
1189 |     pub fn insert(&mut self, k: K, v: V) -> Option<V> {
     |            ------ required by a bound in this associated function
help: consider annotating `PoolType` with `#[derive(Hash)]`
     |
68   + #[derive(Hash)]
69   | pub enum PoolType {
     |

error[E0277]: the trait bound `vultr::models::CreateInstanceRequest: std::default::Default` is not satisfied
  --> src\services\kubernetes_deployment.rs:85:15
   |
85 |             ..Default::default()
   |               ^^^^^^^^^^^^^^^^^^ the trait `std::default::Default` is not implemented for `vultr::models::CreateInstanceRequest`
   |
help: consider annotating `vultr::models::CreateInstanceRequest` with `#[derive(Default)]`
  --> src\vultr\models.rs:1174:1
   |
117+ #[derive(Default)]
117| pub struct CreateInstanceRequest {
   |

error[E0277]: the trait bound `vultr::models::CreateInstanceRequest: std::default::Default` is not satisfied
   --> src\services\kubernetes_deployment.rs:141:15
    |
141 |             ..Default::default()
    |               ^^^^^^^^^^^^^^^^^^ the trait `std::default::Default` is not implemented for `vultr::models::CreateInstanceRequest`
    |
help: consider annotating `vultr::models::CreateInstanceRequest` with `#[derive(Default)]`
   --> src\vultr\models.rs:1174:1
    |
1174+ #[derive(Default)]
1175| pub struct CreateInstanceRequest {
    |

error[E0592]: duplicate definitions with name `detect_runtime`
   --> src\services\deployment.rs:728:5
    |
409 |     async fn detect_runtime(&self, application: &Application) -> ServiceResult<crate::models::BuildRuntime> {
    |     ------------------------------------------------------------------------------------------------------- other definition for 
`detect_runtime`
...
728 |     async fn detect_runtime(&self, _application: &Application) -> ServiceResult<crate::models::BuildRuntime> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ duplicate definitions for 
`detect_runtime`

error[E0277]: the trait bound `PoolType: std::cmp::Eq` is not satisfied
    --> src\controllers\intelligent_hosting.rs:43:23
     |
43   |     pub server_pools: HashMap<PoolType, ServerPoolStatus>,
     |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `std::cmp::Eq` is not implemented for `PoolType`
     |
     = help: the trait `Deserialize<'_>` is implemented for `HashMap<K, V, S>`
     = note: required for `HashMap<PoolType, ServerPoolStatus>` to implement `Deserialize<'_>`
note: required by a bound in `next_element`
    --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\serde-1.0.219\src\de\mod.rs:1732:12
     |
1730 |     fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>
     |        ------------ required by a bound in this associated function
1731 |     where
1732 |         T: Deserialize<'de>,
     |            ^^^^^^^^^^^^^^^^ required by this bound in `SeqAccess::next_element`
help: consider annotating `PoolType` with `#[derive(Eq)]`
    -->  src\services\intelligent_hosting.rs:68:1
     |
68   + #[derive(Eq)]
69   | pub enum PoolType {
     |

error[E0277]: the trait bound `PoolType: Hash` is not satisfied
    --> src\controllers\intelligent_hosting.rs:43:23
     |
43   |     pub server_pools: HashMap<PoolType, ServerPoolStatus>,
     |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Hash` is not implemented for `PoolType`
     |
     = help: the trait `Deserialize<'_>` is implemented for `HashMap<K, V, S>`
     = note: required for `HashMap<PoolType, ServerPoolStatus>` to implement `Deserialize<'_>`
note: required by a bound in `next_element`
    --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\serde-1.0.219\src\de\mod.rs:1732:12
     |
1730 |     fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>
     |        ------------ required by a bound in this associated function
1731 |     where
1732 |         T: Deserialize<'de>,
     |            ^^^^^^^^^^^^^^^^ required by this bound in `SeqAccess::next_element`
help: consider annotating `PoolType` with `#[derive(Hash)]`
    -->  src\services\intelligent_hosting.rs:68:1
     |
68   + #[derive(Hash)]
69   | pub enum PoolType {
     |

error[E0277]: the trait bound `PoolType: std::cmp::Eq` is not satisfied
    --> src\controllers\intelligent_hosting.rs:43:23
     |
43   |     pub server_pools: HashMap<PoolType, ServerPoolStatus>,
     |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `std::cmp::Eq` is not implemented for `PoolType`
     |
     = help: the trait `Deserialize<'_>` is implemented for `HashMap<K, V, S>`
     = note: required for `HashMap<PoolType, ServerPoolStatus>` to implement `Deserialize<'_>`
note: required by a bound in `next_value`
    --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\serde-1.0.219\src\de\mod.rs:1871:12
     |
1869 |     fn next_value<V>(&mut self) -> Result<V, Self::Error>
     |        ---------- required by a bound in this associated function
1870 |     where
1871 |         V: Deserialize<'de>,
     |            ^^^^^^^^^^^^^^^^ required by this bound in `MapAccess::next_value`
help: consider annotating `PoolType` with `#[derive(Eq)]`
    -->  src\services\intelligent_hosting.rs:68:1
     |
68   + #[derive(Eq)]
69   | pub enum PoolType {
     |

error[E0277]: the trait bound `PoolType: Hash` is not satisfied
    --> src\controllers\intelligent_hosting.rs:43:23
     |
43   |     pub server_pools: HashMap<PoolType, ServerPoolStatus>,
     |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Hash` is not implemented for `PoolType`
     |
     = help: the trait `Deserialize<'_>` is implemented for `HashMap<K, V, S>`
     = note: required for `HashMap<PoolType, ServerPoolStatus>` to implement `Deserialize<'_>`
note: required by a bound in `next_value`
    --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\serde-1.0.219\src\de\mod.rs:1871:12
     |
1869 |     fn next_value<V>(&mut self) -> Result<V, Self::Error>
     |        ---------- required by a bound in this associated function
1870 |     where
1871 |         V: Deserialize<'de>,
     |            ^^^^^^^^^^^^^^^^ required by this bound in `MapAccess::next_value`
help: consider annotating `PoolType` with `#[derive(Hash)]`
    -->  src\services\intelligent_hosting.rs:68:1
     |
68   + #[derive(Hash)]
69   | pub enum PoolType {
     |

error[E0277]: the trait bound `PoolType: std::cmp::Eq` is not satisfied
  --> src\controllers\intelligent_hosting.rs:41:28
   |
41 | #[derive(Debug, Serialize, Deserialize)]
   |                            ^^^^^^^^^^^ the trait `std::cmp::Eq` is not implemented for `PoolType`
   |
   = help: the trait `Deserialize<'_>` is implemented for `HashMap<K, V, S>`
   = note: required for `HashMap<PoolType, ServerPoolStatus>` to implement `Deserialize<'_>`
note: required by a bound in `config::_::_serde::__private::de::missing_field`
  --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\serde-1.0.219\src\private\de.rs:25:8
   |
23 | pub fn missing_field<'de, V, E>(field: &'static str) -> Result<V, E>
   |        ------------- required by a bound in this function
24 | where
25 |     V: Deserialize<'de>,
   |        ^^^^^^^^^^^^^^^^ required by this bound in `missing_field`
   = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider annotating `PoolType` with `#[derive(Eq)]`
  --> src\services\intelligent_hosting.rs:68:1
   |
68 + #[derive(Eq)]
69 | pub enum PoolType {
   |

error[E0277]: the trait bound `PoolType: Hash` is not satisfied
  --> src\controllers\intelligent_hosting.rs:41:28
   |
41 | #[derive(Debug, Serialize, Deserialize)]
   |                            ^^^^^^^^^^^ the trait `Hash` is not implemented for `PoolType`
   |
   = help: the trait `Deserialize<'_>` is implemented for `HashMap<K, V, S>`
   = note: required for `HashMap<PoolType, ServerPoolStatus>` to implement `Deserialize<'_>`
note: required by a bound in `config::_::_serde::__private::de::missing_field`
  --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\serde-1.0.219\src\private\de.rs:25:8
   |
23 | pub fn missing_field<'de, V, E>(field: &'static str) -> Result<V, E>
   |        ------------- required by a bound in this function
24 | where
25 |     V: Deserialize<'de>,
   |        ^^^^^^^^^^^^^^^^ required by this bound in `missing_field`
   = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider annotating `PoolType` with `#[derive(Hash)]`
  --> src\services\intelligent_hosting.rs:68:1
   |
68 + #[derive(Hash)]
69 | pub enum PoolType {
   |

error[E0599]: no method named `error_threshold` found for struct `Config` in the current scope
  --> src\infrastructure\circuit_breaker.rs:26:26
   |
25 |                       "vultr_api" => Config::new()
   |  ____________________________________-
26 | |                         .error_threshold(10)
   | |                         -^^^^^^^^^^^^^^^ method not found in `Config<OrElse<SuccessRateOverTimeWindow<EqualJittered>, ...>, ()>`
   | |_________________________|
   |

error[E0599]: no method named `error_threshold` found for struct `Config` in the current scope
  --> src\infrastructure\circuit_breaker.rs:31:26
   |
30 |                       "database" => Config::new()
   |  ___________________________________-
31 | |                         .error_threshold(5)
   | |                         -^^^^^^^^^^^^^^^ method not found in `Config<OrElse<SuccessRateOverTimeWindow<EqualJittered>, ...>, ()>`
   | |_________________________|
   |

error[E0599]: no method named `error_threshold` found for struct `Config` in the current scope
  --> src\infrastructure\circuit_breaker.rs:36:26
   |
35 |                       "git_api" => Config::new()
   |  __________________________________-
36 | |                         .error_threshold(8)
   | |                         -^^^^^^^^^^^^^^^ method not found in `Config<OrElse<SuccessRateOverTimeWindow<EqualJittered>, ...>, ()>`
   | |_________________________|
   |

error[E0599]: no method named `error_threshold` found for struct `Config` in the current scope
  --> src\infrastructure\circuit_breaker.rs:41:26
   |
40 |                       "build_system" => Config::new()
   |  _______________________________________-
41 | |                         .error_threshold(15)
   | |                         -^^^^^^^^^^^^^^^ method not found in `Config<OrElse<SuccessRateOverTimeWindow<EqualJittered>, ...>, ()>`
   | |_________________________|
   |

error[E0599]: no method named `error_threshold` found for struct `Config` in the current scope
  --> src\infrastructure\circuit_breaker.rs:46:26
   |
45 |                       _ => Config::new()
   |  __________________________-
46 | |                         .error_threshold(10)
   | |                         -^^^^^^^^^^^^^^^ method not found in `Config<OrElse<SuccessRateOverTimeWindow<EqualJittered>, ...>, ()>`
   | |_________________________|
   |

error[E0782]: expected a type, found a trait
  --> src\infrastructure\circuit_breaker.rs:53:26
   |
53 |                 Arc::new(CircuitBreaker::new(config))
   |                          ^^^^^^^^^^^^^^
   |
help: you can add the `dyn` keyword if you want a trait object
   |
53 |                 Arc::new(<dyn CircuitBreaker>::new(config))
   |                          ++++               +

error[E0433]: failed to resolve: use of undeclared type `DeploymentStateMachine`
   --> src\infrastructure\state_machine.rs:240:28
    |
240 |             state_machine: DeploymentStateMachine::new(DeploymentState::Created),
    |                            ^^^^^^^^^^^^^^^^^^^^^^
    |                            |
    |                            use of undeclared type `DeploymentStateMachine`
    |                            help: a struct with a similar name exists: `DeploymentStateManager`

error[E0433]: failed to resolve: use of undeclared type `DeploymentStateMachine`
   --> src\infrastructure\state_machine.rs:247:28
    |
247 |             state_machine: DeploymentStateMachine::new(state),
    |                            ^^^^^^^^^^^^^^^^^^^^^^
    |                            |
    |                            use of undeclared type `DeploymentStateMachine`
    |                            help: a struct with a similar name exists: `DeploymentStateManager`

error[E0433]: failed to resolve: use of undeclared type `ApplicationStateMachine`
   --> src\infrastructure\state_machine.rs:318:28
    |
318 |             state_machine: ApplicationStateMachine::new(ApplicationState::Creating),
    |                            ^^^^^^^^^^^^^^^^^^^^^^^
    |                            |
    |                            use of undeclared type `ApplicationStateMachine`
    |                            help: a struct with a similar name exists: `ApplicationStateManager`

error[E0433]: failed to resolve: use of undeclared type `ApplicationStateMachine`
   --> src\infrastructure\state_machine.rs:325:28
    |
325 |             state_machine: ApplicationStateMachine::new(state),
    |                            ^^^^^^^^^^^^^^^^^^^^^^^
    |                            |
    |                            use of undeclared type `ApplicationStateMachine`
    |                            help: a struct with a similar name exists: `ApplicationStateManager`

error[E0599]: no method named `clone` found for struct `RefMut<'_, String, RateLimiter<NotKeyed, InMemoryState, ..., ...>>` in the current 
scope
   --> src\infrastructure\rate_limiter.rs:263:14
    |
260 | /         self.limiters
261 | |             .entry(key.to_string())
262 | |             .or_insert_with(|| RateLimiter::direct(quota))
263 | |             .clone()
    | |             -^^^^^ method not found in `RefMut<'_, String, RateLimiter<NotKeyed, InMemoryState, ..., ...>>`
    | |_____________|
    |

error[E0599]: no method named `snapshot` found for struct `Ref<'_, String, RateLimiter<NotKeyed, InMemoryState, ..., ...>>` in the current 
scope
   --> src\infrastructure\rate_limiter.rs:375:36
    |
375 |             let snapshot = limiter.snapshot();
    |                                    ^^^^^^^^ method not found in `Ref<'_, String, RateLimiter<NotKeyed, InMemoryState, ..., ...>>`

error[E0599]: no method named `snapshot` found for reference `&governor::RateLimiter<NotKeyed, InMemoryState, QuantaClock, 
NoOpMiddleware<QuantaInstant>>` in the current scope
   --> src\infrastructure\rate_limiter.rs:389:46
    |
389 |                 let snapshot = entry.value().snapshot();
    |                                              ^^^^^^^^ method not found in `&RateLimiter<NotKeyed, InMemoryState, QuantaClock, 
NoOpMiddleware<QuantaInstant>>`

error[E0600]: cannot apply unary operator `-` to type `u64`
  --> src\infrastructure\metrics.rs:92:50
   |
92 |         counter!("applications_total").increment(-1);
   |                                                  ^^ cannot apply unary operator `-`
   |
   = note: unsigned values cannot be negated
help: you may have meant the maximum value of `u64`
   |
92 -         counter!("applications_total").increment(-1);
92 +         counter!("applications_total").increment(u64::MAX);
   |

error[E0277]: the trait bound `semver::Version: Serialize` is not satisfied
    --> src\infrastructure\schema_migration.rs:44:24
     |
44   | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ the trait `Serialize` is not implemented for `semver::Version`
45   | pub struct Migration {
46   |     pub version: Version,
     |     --- required by a bound introduced by this call
     |
     = note: for local types consider adding `#[derive(serde::Serialize)]` to your `semver::Version` type
     = note: for types from other crates check whether the crate offers a `serde` feature flag
     = help: the following other types implement trait `Serialize`:
               &'a T
               &'a mut T
               &RawArray
               &RawDocument
               ()
               (T,)
               (T0, T1)
               (T0, T1, T2)
             and 858 others
note: required by a bound in `config::_::_serde::ser::SerializeStruct::serialize_field`
    --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\serde-1.0.219\src\ser\mod.rs:1866:21
     |
1864 |     fn serialize_field<T>(&mut self, key: &'static str, value: &T) -> Result<(), Self::Error>
     |        --------------- required by a bound in this associated function
1865 |     where
1866 |         T: ?Sized + Serialize;
     |                     ^^^^^^^^^ required by this bound in `SerializeStruct::serialize_field`
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `semver::Version: Deserialize<'_>` is not satisfied
    --> src\infrastructure\schema_migration.rs:46:18
     |
46   |     pub version: Version,
     |                  ^^^^^^^ the trait `Deserialize<'_>` is not implemented for `semver::Version`
     |
     = note: for local types consider adding `#[derive(serde::Deserialize)]` to your `semver::Version` type
     = note: for types from other crates check whether the crate offers a `serde` feature flag
     = help: the following other types implement trait `Deserialize<'de>`:
               &'a RawArray
               &'a RawDocument
               &'a [u8]
               &'a serde_bytes::bytearray::ByteArray<N>
               &'a serde_bytes::bytes::Bytes
               &'a serde_json::value::RawValue
               &'a std::path::Path
               &'a str
             and 871 others
note: required by a bound in `next_element`
    --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\serde-1.0.219\src\de\mod.rs:1732:12
     |
1730 |     fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>
     |        ------------ required by a bound in this associated function
1731 |     where
1732 |         T: Deserialize<'de>,
     |            ^^^^^^^^^^^^^^^^ required by this bound in `SeqAccess::next_element`

error[E0277]: the trait bound `semver::Version: Deserialize<'_>` is not satisfied
    --> src\infrastructure\schema_migration.rs:46:18
     |
46   |     pub version: Version,
     |                  ^^^^^^^ the trait `Deserialize<'_>` is not implemented for `semver::Version`
     |
     = note: for local types consider adding `#[derive(serde::Deserialize)]` to your `semver::Version` type
     = note: for types from other crates check whether the crate offers a `serde` feature flag
     = help: the following other types implement trait `Deserialize<'de>`:
               &'a RawArray
               &'a RawDocument
               &'a [u8]
               &'a serde_bytes::bytearray::ByteArray<N>
               &'a serde_bytes::bytes::Bytes
               &'a serde_json::value::RawValue
               &'a std::path::Path
               &'a str
             and 871 others
note: required by a bound in `next_value`
    --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\serde-1.0.219\src\de\mod.rs:1871:12
     |
1869 |     fn next_value<V>(&mut self) -> Result<V, Self::Error>
     |        ---------- required by a bound in this associated function
1870 |     where
1871 |         V: Deserialize<'de>,
     |            ^^^^^^^^^^^^^^^^ required by this bound in `MapAccess::next_value`

error[E0277]: the trait bound `semver::Version: Deserialize<'_>` is not satisfied
  --> src\infrastructure\schema_migration.rs:44:35
   |
44 | #[derive(Debug, Clone, Serialize, Deserialize)]
   |                                   ^^^^^^^^^^^ the trait `Deserialize<'_>` is not implemented for `semver::Version`
   |
   = note: for local types consider adding `#[derive(serde::Deserialize)]` to your `semver::Version` type
   = note: for types from other crates check whether the crate offers a `serde` feature flag
   = help: the following other types implement trait `Deserialize<'de>`:
             &'a RawArray
             &'a RawDocument
             &'a [u8]
             &'a serde_bytes::bytearray::ByteArray<N>
             &'a serde_bytes::bytes::Bytes
             &'a serde_json::value::RawValue
             &'a std::path::Path
             &'a str
           and 871 others
note: required by a bound in `config::_::_serde::__private::de::missing_field`
  --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\serde-1.0.219\src\private\de.rs:25:8
   |
23 | pub fn missing_field<'de, V, E>(field: &'static str) -> Result<V, E>
   |        ------------- required by a bound in this function
24 | where
25 |     V: Deserialize<'de>,
   |        ^^^^^^^^^^^^^^^^ required by this bound in `missing_field`
   = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `std::time::Instant: Serialize` is not satisfied
    --> src\infrastructure\chunk_processor.rs:48:24
     |
48   | #[derive(Debug, Clone, Serialize)]
     |                        ^^^^^^^^^ the trait `Serialize` is not implemented for `std::time::Instant`
...
56   |     pub last_updated: Instant,
     |     --- required by a bound introduced by this call
     |
     = note: for local types consider adding `#[derive(serde::Serialize)]` to your `std::time::Instant` type
     = note: for types from other crates check whether the crate offers a `serde` feature flag
     = help: the following other types implement trait `Serialize`:
               &'a T
               &'a mut T
               &RawArray
               &RawDocument
               ()
               (T,)
               (T0, T1)
               (T0, T1, T2)
             and 858 others
note: required by a bound in `config::_::_serde::ser::SerializeStruct::serialize_field`
    --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\serde-1.0.219\src\ser\mod.rs:1866:21
     |
1864 |     fn serialize_field<T>(&mut self, key: &'static str, value: &T) -> Result<(), Self::Error>
     |        --------------- required by a bound in this associated function
1865 |     where
1866 |         T: ?Sized + Serialize;
     |                     ^^^^^^^^^ required by this bound in `SerializeStruct::serialize_field`
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {list_applications}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:62:37
    |
62  |         .route("/applications", get(controllers::applications::list_applications))
    |                                 --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for fn 
item `fn(State<Arc<{type error}, ...>>, ..., ...) -> ... {list_applications}`
    |                                 |
    |                                 required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-11911928728887182494.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {create_application}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:63:38
    |
63  |         .route("/applications", post(controllers::applications::create_application))
    |                                 ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for fn 
item `fn(State<Arc<..., ...>>, ..., ...) -> ... {create_application}`
    |                                 |
    |                                 required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-10487258943508143033.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {get_application}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:64:45
    |
64  |         .route("/applications/:app_id", get(controllers::applications::get_application))
    |                                         --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for 
fn item `fn(State<Arc<{type error}, ...>>, ..., ...) -> ... {get_application}`
    |                                         |
    |                                         required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-11385688775585744878.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(..., ..., ..., ...) -> ... {update_application}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:65:45
    |
65  |         .route("/applications/:app_id", put(controllers::applications::update_application))
    |                                         --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented 
for fn item `fn(State<Arc<..., ...>>, ..., ..., ...) -> ... {update_application}`
    |                                         |
    |                                         required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `put`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:444:1
    |
444 | top_level_handler_fn!(put, PUT);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `put`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-8923759171194302482.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {delete_application}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:66:48
    |
66  |         .route("/applications/:app_id", delete(controllers::applications::delete_application))
    |                                         ------ ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not 
implemented for fn item `fn(State<Arc<..., ...>>, ..., ...) -> ... {delete_application}`
    |                                         |
    |                                         required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `delete`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:438:1
    |
438 | top_level_handler_fn!(delete, DELETE);
    | ^^^^^^^^^^^^^^^^^^^^^^------^^^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `delete`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-17574195881861020910.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(..., ..., ..., ...) -> ... {trigger_deployment}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:67:53
    |
67  |         .route("/applications/:app_id/deploy", post(controllers::applications::trigger_deployment))
    |                                                ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not 
implemented for fn item `fn(State<Arc<..., ...>>, ..., ..., ...) -> ... {trigger_deployment}`
    |                                                |
    |                                                required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-838601843440679263.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ..., ...) -> ... {list_deployments}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:68:57
    |
68  |         .route("/applications/:app_id/deployments", get(controllers::applications::list_deployments))
    |                                                     --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not 
implemented for fn item `fn(State<Arc<..., ...>>, ..., ..., ...) -> ... {list_deployments}`
    |                                                     |
    |                                                     required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-17320737182241364536.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {get_deployment}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:69:72
    |
69  |         .route("/applications/:app_id/deployments/:deployment_id", get(controllers::applications::get_deployment))
    |                                                                    --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, 
_>` is not implemented for fn item `fn(State<Arc<{type error}, ...>>, ..., ...) -> ... {get_deployment}`
    |                                                                    |
    |                                                                    required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-10269852050885224530.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {rollback_deployment}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:70:82
    |
70  |         .route("/applications/:app_id/deployments/:deployment_id/rollback", post(controllers::applications::rollback_deployment))
    |                                                                             ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the 
trait `Handler<_, _>` is not implemented for fn item `fn(State<Arc<..., ...>>, ..., ...) -> ... {rollback_deployment}`
    |                                                                             |
    |                                                                             required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-16414322081119733252.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ...) -> ... {list_environment_groups}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:73:43
    |
73  |         .route("/environment-groups", get(controllers::environment::list_environment_groups))
    |                                       --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented 
for fn item `fn(State<Arc<..., ...>>, ...) -> ... {list_environment_groups}`
    |                                       |
    |                                       required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-11993574584241725032.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(..., ..., ...) -> ... {create_environment_group}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:74:44
    |
74  |         .route("/environment-groups", post(controllers::environment::create_environment_group))
    |                                       ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not 
implemented for fn item `fn(State<Arc<..., ...>>, ..., ...) -> ... {create_environment_group}`
    |                                       |
    |                                       required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-13199969364522976092.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {get_environment_group}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:75:53
    |
75  |         .route("/environment-groups/:group_id", get(controllers::environment::get_environment_group))
    |                                                 --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not 
implemented for fn item `fn(State<Arc<..., ...>>, ..., ...) -> ... {get_environment_group}`
    |                                                 |
    |                                                 required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-14556939770796452568.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(..., ..., ..., ...) -> ... {set_environment_variable}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:76:64
    |
76  |         .route("/environment-groups/:group_id/variables", post(controllers::environment::set_environment_variable))
    |                                                           ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, 
_>` is not implemented for fn item `fn(State<...>, ..., ..., ...) -> ... {set_environment_variable}`
    |                                                           |
    |                                                           required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-6946570025811342326.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(..., ..., ...) -> ... {delete_environment_variable}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:77:71
    |
77  |         .route("/environment-groups/:group_id/variables/:key", delete(controllers::environment::delete_environment_variable))
    |                                                                ------ ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait 
`Handler<_, _>` is not implemented for fn item `fn(State<...>, ..., ...) -> ... {delete_environment_variable}`
    |                                                                |
    |                                                                required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `delete`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:438:1
    |
438 | top_level_handler_fn!(delete, DELETE);
    | ^^^^^^^^^^^^^^^^^^^^^^------^^^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `delete`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-14805166030479536352.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ..., ...) -> ... {set_secret}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:78:62
    |
78  |         .route("/environment-groups/:group_id/secrets", post(controllers::environment::set_secret))
    |                                                         ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not 
implemented for fn item `fn(State<Arc<{type error}, ...>>, ..., ..., ...) -> ... {set_secret}`
    |                                                         |
    |                                                         required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-17351655703905600450.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ...) -> ... {delete_secret}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:79:69
    |
79  |         .route("/environment-groups/:group_id/secrets/:key", delete(controllers::environment::delete_secret))
    |                                                              ------ ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is 
not implemented for fn item `fn(State<Arc<{type error}, ...>>, ..., ...) -> ... {delete_secret}`
    |                                                              |
    |                                                              required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `delete`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:438:1
    |
438 | top_level_handler_fn!(delete, DELETE);
    | ^^^^^^^^^^^^^^^^^^^^^^------^^^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `delete`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-13459406517003347421.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ..., ...) -> ... {link_application}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:80:59
    |
80  |         .route("/environment-groups/:group_id/link", post(controllers::environment::link_application))
    |                                                      ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not 
implemented for fn item `fn(State<Arc<..., ...>>, ..., ..., ...) -> ... {link_application}`
    |                                                      |
    |                                                      required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-10113850558202097947.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(..., ..., ..., ...) -> ... {get_application_logs}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:83:50
    |
83  |         .route("/applications/:app_id/logs", get(controllers::logs::get_application_logs))
    |                                              --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented 
for fn item `fn(State<Arc<..., ...>>, ..., ..., ...) -> ... {get_application_logs}`
    |                                              |
    |                                              required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-9648021129982167780.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(..., ..., ..., ...) -> ... {stream_application_logs}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:84:57
    |
84  |         .route("/applications/:app_id/logs/stream", get(controllers::logs::stream_application_logs))
    |                                                     --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not 
implemented for fn item `fn(State<...>, ..., ..., ...) -> ... {stream_application_logs}`
    |                                                     |
    |                                                     required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-16817045147640728220.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(..., ..., ..., ...) -> ... {get_deployment_logs}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:85:77
    |
85  |         .route("/applications/:app_id/deployments/:deployment_id/logs", get(controllers::logs::get_deployment_logs))
    |                                                                         --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, 
_>` is not implemented for fn item `fn(State<Arc<..., ...>>, ..., ..., ...) -> ... {get_deployment_logs}`
    |                                                                         |
    |                                                                         required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-2050744881107001637.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ..., ...) -> ... {get_build_logs}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:86:44
    |
86  |         .route("/builds/:job_id/logs", get(controllers::logs::get_build_logs))
    |                                        --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for fn item 
`fn(State<Arc<..., ...>>, ..., ..., ...) -> ... {get_build_logs}`
    |                                        |
    |                                        required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-10940563276379111974.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(..., ..., ..., ...) -> ... {stream_build_logs}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:87:51
    |
87  |         .route("/builds/:job_id/logs/stream", get(controllers::logs::stream_build_logs))
    |                                               --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for 
fn item `fn(State<Arc<..., ...>>, ..., ..., ...) -> ... {stream_build_logs}`
    |                                               |
    |                                               required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `axum::routing::get`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:439:1
    |
439 | top_level_handler_fn!(get, GET);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `get`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-8490ae068bebf550.long-type-11852525500548021740.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `PoolType: std::cmp::Eq` is not satisfied
    --> src\services\intelligent_hosting.rs:222:22
     |
222  |         server_pools.insert(PoolType::SharedHot, ServerPool {
     |                      ^^^^^^ the trait `std::cmp::Eq` is not implemented for `PoolType`
     |
note: required by a bound in `HashMap::<K, V, S>::insert`
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\std\src\collections\hash\map.rs:752:8
     |
752  |     K: Eq + Hash,
     |        ^^ required by this bound in `HashMap::<K, V, S>::insert`
...
1189 |     pub fn insert(&mut self, k: K, v: V) -> Option<V> {
     |            ------ required by a bound in this associated function
help: consider annotating `PoolType` with `#[derive(Eq)]`
     |
68   + #[derive(Eq)]
69   | pub enum PoolType {
     |

error[E0277]: the trait bound `PoolType: Hash` is not satisfied
    --> src\services\intelligent_hosting.rs:222:22
     |
222  |         server_pools.insert(PoolType::SharedHot, ServerPool {
     |                      ^^^^^^ the trait `Hash` is not implemented for `PoolType`
     |
note: required by a bound in `HashMap::<K, V, S>::insert`
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\std\src\collections\hash\map.rs:752:13
     |
752  |     K: Eq + Hash,
     |             ^^^^ required by this bound in `HashMap::<K, V, S>::insert`
...
1189 |     pub fn insert(&mut self, k: K, v: V) -> Option<V> {
     |            ------ required by a bound in this associated function
help: consider annotating `PoolType` with `#[derive(Hash)]`
     |
68   + #[derive(Hash)]
69   | pub enum PoolType {
     |

error[E0599]: the method `insert` exists for struct `HashMap<PoolType, ServerPool>`, but its trait bounds were not satisfied
   --> src\services\intelligent_hosting.rs:236:22
    |
68  | pub enum PoolType {
    | ----------------- doesn't satisfy `PoolType: Hash` or `PoolType: std::cmp::Eq`
...
236 |         server_pools.insert(PoolType::SharedCold, ServerPool {
    |         -------------^^^^^^
    |
    = note: the following trait bounds were not satisfied:
            `PoolType: std::cmp::Eq`
            `PoolType: Hash`
help: consider annotating `PoolType` with `#[derive(Eq, Hash, PartialEq)]`
    |
68  + #[derive(Eq, Hash, PartialEq)]
69  | pub enum PoolType {
    |

warning: use of deprecated method `chrono::DateTime::<Tz>::timestamp_nanos`: use `timestamp_nanos_opt()` instead
  --> src\utils\mod.rs:40:9
   |
40 |     now.timestamp_nanos().hash(&mut hasher);
   |         ^^^^^^^^^^^^^^^
   |
   = note: `#[warn(deprecated)]` on by default

error[E0716]: temporary value dropped while borrowed
   --> src\infrastructure\metrics.rs:140:30
    |
140 |             ("status_code", &status_code.to_string()),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^ creates a temporary value which is freed while still in use
...
143 |         counter!("api_requests_total", &labels).increment(1);
    |         --------------------------------------- argument requires that borrow lasts for `'static`
...
149 |     }
    |     - temporary value is freed at the end of this statement

error[E0521]: borrowed data escapes outside of method
   --> src\infrastructure\metrics.rs:143:9
    |
136 |     pub fn record_api_request(&self, method: &str, path: &str, status_code: u16, duration: Duration) {
    |                                      ------  - let's call the lifetime of this reference `'1`
    |                                      |
    |                                      `method` is a reference that is only valid in the method body
...
143 |         counter!("api_requests_total", &labels).increment(1);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         |
    |         `method` escapes the method body here
    |         argument requires that `'1` must outlive `'static`
    |
    = note: this error originates in the macro `$crate::key_var` which comes from the expansion of the macro `counter` (in Nightly builds, run 
with -Z macro-backtrace for more info)

error[E0521]: borrowed data escapes outside of method
   --> src\infrastructure\metrics.rs:143:9
    |
136 |     pub fn record_api_request(&self, method: &str, path: &str, status_code: u16, duration: Duration) {
    |                                                    ----  - let's call the lifetime of this reference `'2`
    |                                                    |
    |                                                    `path` is a reference that is only valid in the method body
...
143 |         counter!("api_requests_total", &labels).increment(1);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         |
    |         `path` escapes the method body here
    |         argument requires that `'2` must outlive `'static`
    |
    = note: this error originates in the macro `$crate::key_var` which comes from the expansion of the macro `counter` (in Nightly builds, run 
with -Z macro-backtrace for more info)

error[E0521]: borrowed data escapes outside of method
   --> src\infrastructure\metrics.rs:154:9
    |
152 |     pub fn set_circuit_breaker_status(&self, service: &str, is_open: bool) {
    |                                              -------  - let's call the lifetime of this reference `'1`
    |                                              |
    |                                              `service` is a reference that is only valid in the method body
153 |         let labels = [("service", service)];
154 |         gauge!("circuit_breaker_open", &labels).set(if is_open { 1.0 } else { 0.0 });
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         |
    |         `service` escapes the method body here
    |         argument requires that `'1` must outlive `'static`
    |
    = note: this error originates in the macro `$crate::key_var` which comes from the expansion of the macro `gauge` (in Nightly builds, run 
with -Z macro-backtrace for more info)

error[E0521]: borrowed data escapes outside of method
   --> src\infrastructure\metrics.rs:159:9
    |
157 |     pub fn increment_circuit_breaker_trips(&self, service: &str) {
    |                                                   -------  - let's call the lifetime of this reference `'1`
    |                                                   |
    |                                                   `service` is a reference that is only valid in the method body
158 |         let labels = [("service", service)];
159 |         counter!("circuit_breaker_trips_total", &labels).increment(1);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         |
    |         `service` escapes the method body here
    |         argument requires that `'1` must outlive `'static`
    |
    = note: this error originates in the macro `$crate::key_var` which comes from the expansion of the macro `counter` (in Nightly builds, run 
with -Z macro-backtrace for more info)

error[E0521]: borrowed data escapes outside of method
   --> src\infrastructure\metrics.rs:165:9
    |
163 |     pub fn set_rate_limit_remaining(&self, key: &str, remaining: u32) {
    |                                            ---  - let's call the lifetime of this reference `'1`
    |                                            |
    |                                            `key` is a reference that is only valid in the method body
164 |         let labels = [("key", key)];
165 |         gauge!("rate_limit_remaining", &labels).set(remaining as f64);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         |
    |         `key` escapes the method body here
    |         argument requires that `'1` must outlive `'static`
    |
    = note: this error originates in the macro `$crate::key_var` which comes from the expansion of the macro `gauge` (in Nightly builds, run 
with -Z macro-backtrace for more info)

error[E0521]: borrowed data escapes outside of method
   --> src\infrastructure\metrics.rs:172:9
    |
169 |     pub fn record_database_operation(&self, operation: &str, duration: Duration, success: bool) {
    |                                             ---------  - let's call the lifetime of this reference `'1`
    |                                             |
    |                                             `operation` is a reference that is only valid in the method body
...
172 |         counter!("database_operations_total", &labels).increment(1);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         |
    |         `operation` escapes the method body here
    |         argument requires that `'1` must outlive `'static`
    |
    = note: this error originates in the macro `$crate::key_var` which comes from the expansion of the macro `counter` (in Nightly builds, run 
with -Z macro-backtrace for more info)

error[E0521]: borrowed data escapes outside of method
   --> src\infrastructure\metrics.rs:184:9
    |
181 |     pub fn record_external_api_call(&self, service: &str, operation: &str, duration: Duration, success: bool) {
    |                                            -------  - let's call the lifetime of this reference `'1`
    |                                            |
    |                                            `service` is a reference that is only valid in the method body
...
184 |         counter!("external_api_calls_total", &labels).increment(1);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         |
    |         `service` escapes the method body here
    |         argument requires that `'1` must outlive `'static`
    |
    = note: this error originates in the macro `$crate::key_var` which comes from the expansion of the macro `counter` (in Nightly builds, run 
with -Z macro-backtrace for more info)

error[E0521]: borrowed data escapes outside of method
   --> src\infrastructure\metrics.rs:184:9
    |
181 |     pub fn record_external_api_call(&self, service: &str, operation: &str, duration: Duration, success: bool) {
    |                                                           ---------  - let's call the lifetime of this reference `'2`
    |                                                           |
    |                                                           `operation` is a reference that is only valid in the method body
...
184 |         counter!("external_api_calls_total", &labels).increment(1);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         |
    |         `operation` escapes the method body here
    |         argument requires that `'2` must outlive `'static`
    |
    = note: this error originates in the macro `$crate::key_var` which comes from the expansion of the macro `counter` (in Nightly builds, run 
with -Z macro-backtrace for more info)

error[E0521]: borrowed data escapes outside of method
   --> src\infrastructure\metrics.rs:212:9
    |
209 |     pub fn record_health_check(&self, component: &str, healthy: bool, response_time: Duration) {
    |                                       ---------  - let's call the lifetime of this reference `'1`
    |                                       |
    |                                       `component` is a reference that is only valid in the method body
...
212 |         gauge!("health_status", &labels).set(if healthy { 1.0 } else { 0.0 });
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         |
    |         `component` escapes the method body here
    |         argument requires that `'1` must outlive `'static`
    |
    = note: this error originates in the macro `$crate::key_var` which comes from the expansion of the macro `gauge` (in Nightly builds, run 
with -Z macro-backtrace for more info)

error[E0382]: borrow of moved value: `to_remove`
   --> src\infrastructure\chunk_processor.rs:230:13
    |
220 |         let to_remove: Vec<_> = self.dead_letter_queue
    |             --------- move occurs because `to_remove` has type `Vec<std::string::String>`, which does not implement the `Copy` trait
...
226 |         for key in to_remove {
    |                    --------- `to_remove` moved due to this implicit call to `.into_iter()`
...
230 |         if !to_remove.is_empty() {
    |             ^^^^^^^^^ value borrowed here after move
    |
note: `into_iter` takes ownership of the receiver `self`, which moves `to_remove`
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\iter\traits\collect.rs:313:18
    |
313 |     fn into_iter(self) -> Self::IntoIter;
    |                  ^^^^
help: consider iterating over a slice of the `Vec<std::string::String>`'s content to avoid moving into the `for` loop
    |
226 |         for key in &to_remove {
    |                    +

warning: unused variable: `handle`
  --> src\observability\mod.rs:82:9
   |
82 |     let handle = builder
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_handle`

error[E0733]: recursion in an async fn requires boxing
  --> src\utils\retry.rs:47:5
   |
47 |     pub async fn wait_if_needed(&self) {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: a recursive `async fn` call must introduce indirection such as `Box::pin` to avoid an infinitely sized future

Some errors have detailed explanations: E0277, E0282, E0308, E0382, E0412, E0422, E0432, E0433, E0521...
For more information about an error, try `rustc --explain E0277`.
warning: `achidas` (lib) generated 100 warnings
error: could not compile `achidas` (lib) due to 257 previous errors; 100 warnings emitted
