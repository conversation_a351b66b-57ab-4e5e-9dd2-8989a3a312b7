use std::time::{Duration, Instant};
use tokio::time::sleep;
use tracing::{info, warn, error};
use futures::stream::{self, StreamExt};
use crate::services::ServiceError;
use serde::{Serialize, Deserialize};
use std::collections::VecDeque;
use std::sync::Arc;
use dashmap::DashMap;

#[derive(Clone, Debug)]
pub struct ChunkProcessorConfig {
    pub chunk_size: usize,
    pub delay_between_chunks: Duration,
    pub max_retries: u32,
    pub retry_delay: Duration,
    pub max_concurrent_chunks: usize,
}

impl Default for ChunkProcessorConfig {
    fn default() -> Self {
        Self {
            chunk_size: 100,
            delay_between_chunks: Duration::from_millis(100),
            max_retries: 3,
            retry_delay: Duration::from_secs(1),
            max_concurrent_chunks: 5,
        }
    }
}

pub struct ChunkProcessor {
    config: ChunkProcessorConfig,
    dead_letter_queue: Arc<DashMap<String, DeadLetterItem>>,
    queue_metrics: Arc<DashMap<String, QueueMetrics>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeadLetterItem {
    pub id: String,
    pub payload: serde_json::Value,
    pub error: String,
    pub attempts: u32,
    pub first_attempt: chrono::DateTime<chrono::Utc>,
    pub last_attempt: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize)]
pub struct QueueMetrics {
    pub queue_name: String,
    pub current_depth: usize,
    pub max_depth_seen: usize,
    pub total_processed: u64,
    pub total_failed: u64,
    pub average_processing_time: Duration,
    pub last_updated: Instant,
}

impl ChunkProcessor {
    pub fn new(config: ChunkProcessorConfig) -> Self {
        Self {
            config,
            dead_letter_queue: Arc::new(DashMap::new()),
            queue_metrics: Arc::new(DashMap::new()),
        }
    }

    pub fn with_defaults() -> Self {
        Self::new(ChunkProcessorConfig::default())
    }

    /// Process items with queue depth monitoring and dead letter queue
    pub async fn process_with_queue_monitoring<T, F, R>(
        &self,
        queue_name: &str,
        items: Vec<T>,
        processor: F,
    ) -> Result<Vec<R>, ServiceError>
    where
        T: Clone + Send + Serialize + 'static,
        F: Fn(Vec<T>) -> Result<Vec<R>, ServiceError> + Clone + Send + 'static,
        R: Send + 'static,
    {
        let start_time = Instant::now();

        // Initialize queue metrics
        self.init_queue_metrics(queue_name, items.len());

        // Check queue depth alarm
        if items.len() > 1000 {
            warn!("Queue {} has {} items, approaching capacity limit", queue_name, items.len());
        }

        let result = self.process_chunks(items.clone(), processor).await;

        // Update metrics
        let processing_time = start_time.elapsed();
        self.update_queue_metrics(queue_name, items.len(), result.is_ok(), processing_time);

        // Handle failures with dead letter queue
        if let Err(ref e) = result {
            self.send_to_dead_letter_queue(queue_name, items, e.to_string()).await;
        }

        result
    }

    fn init_queue_metrics(&self, queue_name: &str, queue_depth: usize) {
        let metrics = QueueMetrics {
            queue_name: queue_name.to_string(),
            current_depth: queue_depth,
            max_depth_seen: queue_depth,
            total_processed: 0,
            total_failed: 0,
            average_processing_time: Duration::from_millis(0),
            last_updated: Instant::now(),
        };

        self.queue_metrics.insert(queue_name.to_string(), metrics);
    }

    fn update_queue_metrics(&self, queue_name: &str, processed_count: usize, success: bool, processing_time: Duration) {
        if let Some(mut metrics) = self.queue_metrics.get_mut(queue_name) {
            metrics.current_depth = metrics.current_depth.saturating_sub(processed_count);
            metrics.total_processed += processed_count as u64;

            if !success {
                metrics.total_failed += processed_count as u64;
            }

            // Update average processing time (simple moving average)
            let total_operations = metrics.total_processed + metrics.total_failed;
            if total_operations > 0 {
                let current_avg_ms = metrics.average_processing_time.as_millis() as u64;
                let new_time_ms = processing_time.as_millis() as u64;
                let new_avg_ms = (current_avg_ms * (total_operations - 1) + new_time_ms) / total_operations;
                metrics.average_processing_time = Duration::from_millis(new_avg_ms);
            }

            metrics.last_updated = Instant::now();
        }
    }

    async fn send_to_dead_letter_queue<T: Serialize>(&self, queue_name: &str, items: Vec<T>, error: String) {
        for (index, item) in items.into_iter().enumerate() {
            let item_id = format!("{}_{}", queue_name, index);

            let payload = match serde_json::to_value(&item) {
                Ok(value) => value,
                Err(e) => {
                    error!("Failed to serialize item for dead letter queue: {}", e);
                    continue;
                }
            };

            let dead_letter_item = DeadLetterItem {
                id: item_id.clone(),
                payload,
                error: error.clone(),
                attempts: 1,
                first_attempt: chrono::Utc::now(),
                last_attempt: chrono::Utc::now(),
            };

            self.dead_letter_queue.insert(item_id, dead_letter_item);
        }

        warn!("Sent {} items to dead letter queue for queue: {}", items.len(), queue_name);
    }

    /// Get queue metrics for monitoring
    pub fn get_queue_metrics(&self, queue_name: &str) -> Option<QueueMetrics> {
        self.queue_metrics.get(queue_name).map(|m| m.clone())
    }

    /// Get all queue metrics
    pub fn get_all_queue_metrics(&self) -> Vec<QueueMetrics> {
        self.queue_metrics.iter().map(|entry| entry.value().clone()).collect()
    }

    /// Get dead letter queue items
    pub fn get_dead_letter_items(&self) -> Vec<DeadLetterItem> {
        self.dead_letter_queue.iter().map(|entry| entry.value().clone()).collect()
    }

    /// Retry items from dead letter queue
    pub async fn retry_dead_letter_items<F, R>(&self, processor: F) -> Result<usize, ServiceError>
    where
        F: Fn(serde_json::Value) -> Result<R, ServiceError>,
    {
        let items: Vec<_> = self.dead_letter_queue.iter().map(|entry| entry.value().clone()).collect();
        let mut retry_count = 0;

        for item in items {
            match processor(item.payload.clone()) {
                Ok(_) => {
                    self.dead_letter_queue.remove(&item.id);
                    retry_count += 1;
                    info!("Successfully retried dead letter item: {}", item.id);
                }
                Err(e) => {
                    // Update attempt count
                    if let Some(mut dead_item) = self.dead_letter_queue.get_mut(&item.id) {
                        dead_item.attempts += 1;
                        dead_item.last_attempt = chrono::Utc::now();
                        dead_item.error = e.to_string();
                    }
                    warn!("Failed to retry dead letter item {}: {}", item.id, e);
                }
            }
        }

        Ok(retry_count)
    }

    /// Clear old dead letter items (cleanup)
    pub fn cleanup_dead_letter_queue(&self, max_age: Duration) {
        let cutoff = chrono::Utc::now() - chrono::Duration::from_std(max_age).unwrap_or_default();

        let to_remove: Vec<_> = self.dead_letter_queue
            .iter()
            .filter(|entry| entry.value().first_attempt < cutoff)
            .map(|entry| entry.key().clone())
            .collect();

        for key in to_remove {
            self.dead_letter_queue.remove(&key);
        }

        if !to_remove.is_empty() {
            info!("Cleaned up {} old dead letter items", to_remove.len());
        }
    }

    /// Process items in chunks with rate limiting and error handling
    pub async fn process_chunks<T, F, R>(&self, items: Vec<T>, processor: F) -> Result<Vec<R>, ServiceError>
    where
        T: Clone + Send + 'static,
        F: Fn(Vec<T>) -> Result<Vec<R>, ServiceError> + Clone + Send + 'static,
        R: Send + 'static,
    {
        let chunks: Vec<Vec<T>> = items
            .chunks(self.config.chunk_size)
            .map(|chunk| chunk.to_vec())
            .collect();

        let total_chunks = chunks.len();
        info!("Processing {} items in {} chunks", items.len(), total_chunks);

        let mut results = Vec::new();
        let mut processed_chunks = 0;

        // Process chunks with concurrency limit
        let chunk_stream = stream::iter(chunks.into_iter().enumerate())
            .map(|(index, chunk)| {
                let processor = processor.clone();
                let config = self.config.clone();
                async move {
                    Self::process_single_chunk_with_retry(chunk, processor, config, index).await
                }
            })
            .buffer_unordered(self.config.max_concurrent_chunks);

        let chunk_results: Vec<Result<Vec<R>, ServiceError>> = chunk_stream.collect().await;

        for (index, result) in chunk_results.into_iter().enumerate() {
            match result {
                Ok(chunk_result) => {
                    results.extend(chunk_result);
                    processed_chunks += 1;
                    
                    if processed_chunks % 10 == 0 {
                        info!("Processed {}/{} chunks", processed_chunks, total_chunks);
                    }
                }
                Err(e) => {
                    error!("Failed to process chunk {}: {}", index, e);
                    return Err(ServiceError::Internal(format!(
                        "Chunk processing failed at chunk {}: {}",
                        index, e
                    )));
                }
            }

            // Rate limiting between chunks
            if processed_chunks < total_chunks {
                sleep(self.config.delay_between_chunks).await;
            }
        }

        info!("Successfully processed all {} chunks", total_chunks);
        Ok(results)
    }

    async fn process_single_chunk_with_retry<T, F, R>(
        chunk: Vec<T>,
        processor: F,
        config: ChunkProcessorConfig,
        chunk_index: usize,
    ) -> Result<Vec<R>, ServiceError>
    where
        T: Clone,
        F: Fn(Vec<T>) -> Result<Vec<R>, ServiceError>,
    {
        let mut attempts = 0;
        let chunk_size = chunk.len();

        loop {
            attempts += 1;
            
            match processor(chunk.clone()) {
                Ok(result) => {
                    if attempts > 1 {
                        info!(
                            "Chunk {} (size: {}) succeeded on attempt {}",
                            chunk_index, chunk_size, attempts
                        );
                    }
                    return Ok(result);
                }
                Err(e) => {
                    if attempts >= config.max_retries {
                        error!(
                            "Chunk {} (size: {}) failed after {} attempts: {}",
                            chunk_index, chunk_size, attempts, e
                        );
                        return Err(e);
                    }

                    warn!(
                        "Chunk {} (size: {}) failed on attempt {}, retrying: {}",
                        chunk_index, chunk_size, attempts, e
                    );

                    sleep(config.retry_delay * attempts).await;
                }
            }
        }
    }

    /// Process items in chunks with custom error handling
    pub async fn process_chunks_with_error_handler<T, F, R, E>(
        &self,
        items: Vec<T>,
        processor: F,
        error_handler: E,
    ) -> Result<Vec<R>, ServiceError>
    where
        T: Clone + Send + 'static,
        F: Fn(Vec<T>) -> Result<Vec<R>, ServiceError> + Clone + Send + 'static,
        R: Send + 'static,
        E: Fn(usize, &ServiceError) -> bool + Clone + Send + 'static, // Returns true to continue, false to abort
    {
        let chunks: Vec<Vec<T>> = items
            .chunks(self.config.chunk_size)
            .map(|chunk| chunk.to_vec())
            .collect();

        let total_chunks = chunks.len();
        info!("Processing {} items in {} chunks with error handler", items.len(), total_chunks);

        let mut results = Vec::new();
        let mut processed_chunks = 0;
        let mut failed_chunks = 0;

        for (index, chunk) in chunks.into_iter().enumerate() {
            match Self::process_single_chunk_with_retry(
                chunk,
                processor.clone(),
                self.config.clone(),
                index,
            ).await {
                Ok(chunk_result) => {
                    results.extend(chunk_result);
                    processed_chunks += 1;
                }
                Err(e) => {
                    failed_chunks += 1;
                    let should_continue = error_handler(index, &e);
                    
                    if !should_continue {
                        error!(
                            "Error handler requested abort after chunk {} failure: {}",
                            index, e
                        );
                        return Err(ServiceError::Internal(format!(
                            "Processing aborted at chunk {} due to error: {}",
                            index, e
                        )));
                    }
                    
                    warn!("Continuing processing despite chunk {} failure: {}", index, e);
                }
            }

            // Rate limiting
            if processed_chunks + failed_chunks < total_chunks {
                sleep(self.config.delay_between_chunks).await;
            }
        }

        info!(
            "Completed processing: {} successful chunks, {} failed chunks",
            processed_chunks, failed_chunks
        );

        Ok(results)
    }

    /// Process items one by one with rate limiting (for very sensitive operations)
    pub async fn process_sequential<T, F, R>(&self, items: Vec<T>, processor: F) -> Result<Vec<R>, ServiceError>
    where
        T: Clone,
        F: Fn(T) -> Result<R, ServiceError>,
    {
        let total_items = items.len();
        info!("Processing {} items sequentially", total_items);

        let mut results = Vec::with_capacity(total_items);
        
        for (index, item) in items.into_iter().enumerate() {
            let mut attempts = 0;
            
            loop {
                attempts += 1;
                
                match processor(item.clone()) {
                    Ok(result) => {
                        results.push(result);
                        break;
                    }
                    Err(e) => {
                        if attempts >= self.config.max_retries {
                            error!("Item {} failed after {} attempts: {}", index, attempts, e);
                            return Err(e);
                        }
                        
                        warn!("Item {} failed on attempt {}, retrying: {}", index, attempts, e);
                        sleep(self.config.retry_delay * attempts).await;
                    }
                }
            }

            // Progress reporting
            if (index + 1) % 100 == 0 {
                info!("Processed {}/{} items", index + 1, total_items);
            }

            // Rate limiting
            if index + 1 < total_items {
                sleep(self.config.delay_between_chunks).await;
            }
        }

        info!("Successfully processed all {} items sequentially", total_items);
        Ok(results)
    }
}

/// Utility function for safe bulk database operations
pub async fn safe_bulk_operation<T, F, R>(
    items: Vec<T>,
    operation: F,
    chunk_size: Option<usize>,
) -> Result<Vec<R>, ServiceError>
where
    T: Clone + Send + 'static,
    F: Fn(Vec<T>) -> Result<Vec<R>, ServiceError> + Clone + Send + 'static,
    R: Send + 'static,
{
    let config = ChunkProcessorConfig {
        chunk_size: chunk_size.unwrap_or(50), // Smaller default for DB operations
        delay_between_chunks: Duration::from_millis(50),
        max_retries: 3,
        retry_delay: Duration::from_millis(500),
        max_concurrent_chunks: 3, // Conservative for DB
    };

    let processor = ChunkProcessor::new(config);
    processor.process_chunks(items, operation).await
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;

    #[tokio::test]
    async fn test_chunk_processing() {
        let processor = ChunkProcessor::with_defaults();
        let items: Vec<i32> = (1..=10).collect();
        
        let result = processor.process_chunks(items, |chunk| {
            Ok(chunk.into_iter().map(|x| x * 2).collect())
        }).await;

        assert!(result.is_ok());
        let doubled: Vec<i32> = result.unwrap();
        assert_eq!(doubled, vec![2, 4, 6, 8, 10, 12, 14, 16, 18, 20]);
    }

    #[tokio::test]
    async fn test_chunk_processing_with_failure() {
        let processor = ChunkProcessor::with_defaults();
        let items: Vec<i32> = (1..=5).collect();
        
        let result = processor.process_chunks(items, |_chunk| {
            Err(ServiceError::Internal("Test error".to_string()))
        }).await;

        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_sequential_processing() {
        let processor = ChunkProcessor::with_defaults();
        let items: Vec<i32> = (1..=5).collect();
        
        let result = processor.process_sequential(items, |item| {
            Ok(item * 3)
        }).await;

        assert!(result.is_ok());
        let tripled: Vec<i32> = result.unwrap();
        assert_eq!(tripled, vec![3, 6, 9, 12, 15]);
    }
}
