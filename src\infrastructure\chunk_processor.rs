use std::time::Duration;
use tokio::time::sleep;
use tracing::{info, warn, error};
use futures::stream::{self, StreamExt};
use crate::services::ServiceError;

#[derive(Clone, Debug)]
pub struct ChunkProcessorConfig {
    pub chunk_size: usize,
    pub delay_between_chunks: Duration,
    pub max_retries: u32,
    pub retry_delay: Duration,
    pub max_concurrent_chunks: usize,
}

impl Default for ChunkProcessorConfig {
    fn default() -> Self {
        Self {
            chunk_size: 100,
            delay_between_chunks: Duration::from_millis(100),
            max_retries: 3,
            retry_delay: Duration::from_secs(1),
            max_concurrent_chunks: 5,
        }
    }
}

pub struct ChunkProcessor {
    config: ChunkProcessorConfig,
}

impl ChunkProcessor {
    pub fn new(config: ChunkProcessorConfig) -> Self {
        Self { config }
    }

    pub fn with_defaults() -> Self {
        Self::new(ChunkProcessorConfig::default())
    }

    /// Process items in chunks with rate limiting and error handling
    pub async fn process_chunks<T, F, R>(&self, items: Vec<T>, processor: F) -> Result<Vec<R>, ServiceError>
    where
        T: Clone + Send + 'static,
        F: Fn(Vec<T>) -> Result<Vec<R>, ServiceError> + Clone + Send + 'static,
        R: Send + 'static,
    {
        let chunks: Vec<Vec<T>> = items
            .chunks(self.config.chunk_size)
            .map(|chunk| chunk.to_vec())
            .collect();

        let total_chunks = chunks.len();
        info!("Processing {} items in {} chunks", items.len(), total_chunks);

        let mut results = Vec::new();
        let mut processed_chunks = 0;

        // Process chunks with concurrency limit
        let chunk_stream = stream::iter(chunks.into_iter().enumerate())
            .map(|(index, chunk)| {
                let processor = processor.clone();
                let config = self.config.clone();
                async move {
                    Self::process_single_chunk_with_retry(chunk, processor, config, index).await
                }
            })
            .buffer_unordered(self.config.max_concurrent_chunks);

        let chunk_results: Vec<Result<Vec<R>, ServiceError>> = chunk_stream.collect().await;

        for (index, result) in chunk_results.into_iter().enumerate() {
            match result {
                Ok(chunk_result) => {
                    results.extend(chunk_result);
                    processed_chunks += 1;
                    
                    if processed_chunks % 10 == 0 {
                        info!("Processed {}/{} chunks", processed_chunks, total_chunks);
                    }
                }
                Err(e) => {
                    error!("Failed to process chunk {}: {}", index, e);
                    return Err(ServiceError::Internal(format!(
                        "Chunk processing failed at chunk {}: {}",
                        index, e
                    )));
                }
            }

            // Rate limiting between chunks
            if processed_chunks < total_chunks {
                sleep(self.config.delay_between_chunks).await;
            }
        }

        info!("Successfully processed all {} chunks", total_chunks);
        Ok(results)
    }

    async fn process_single_chunk_with_retry<T, F, R>(
        chunk: Vec<T>,
        processor: F,
        config: ChunkProcessorConfig,
        chunk_index: usize,
    ) -> Result<Vec<R>, ServiceError>
    where
        T: Clone,
        F: Fn(Vec<T>) -> Result<Vec<R>, ServiceError>,
    {
        let mut attempts = 0;
        let chunk_size = chunk.len();

        loop {
            attempts += 1;
            
            match processor(chunk.clone()) {
                Ok(result) => {
                    if attempts > 1 {
                        info!(
                            "Chunk {} (size: {}) succeeded on attempt {}",
                            chunk_index, chunk_size, attempts
                        );
                    }
                    return Ok(result);
                }
                Err(e) => {
                    if attempts >= config.max_retries {
                        error!(
                            "Chunk {} (size: {}) failed after {} attempts: {}",
                            chunk_index, chunk_size, attempts, e
                        );
                        return Err(e);
                    }

                    warn!(
                        "Chunk {} (size: {}) failed on attempt {}, retrying: {}",
                        chunk_index, chunk_size, attempts, e
                    );

                    sleep(config.retry_delay * attempts).await;
                }
            }
        }
    }

    /// Process items in chunks with custom error handling
    pub async fn process_chunks_with_error_handler<T, F, R, E>(
        &self,
        items: Vec<T>,
        processor: F,
        error_handler: E,
    ) -> Result<Vec<R>, ServiceError>
    where
        T: Clone + Send + 'static,
        F: Fn(Vec<T>) -> Result<Vec<R>, ServiceError> + Clone + Send + 'static,
        R: Send + 'static,
        E: Fn(usize, &ServiceError) -> bool + Clone + Send + 'static, // Returns true to continue, false to abort
    {
        let chunks: Vec<Vec<T>> = items
            .chunks(self.config.chunk_size)
            .map(|chunk| chunk.to_vec())
            .collect();

        let total_chunks = chunks.len();
        info!("Processing {} items in {} chunks with error handler", items.len(), total_chunks);

        let mut results = Vec::new();
        let mut processed_chunks = 0;
        let mut failed_chunks = 0;

        for (index, chunk) in chunks.into_iter().enumerate() {
            match Self::process_single_chunk_with_retry(
                chunk,
                processor.clone(),
                self.config.clone(),
                index,
            ).await {
                Ok(chunk_result) => {
                    results.extend(chunk_result);
                    processed_chunks += 1;
                }
                Err(e) => {
                    failed_chunks += 1;
                    let should_continue = error_handler(index, &e);
                    
                    if !should_continue {
                        error!(
                            "Error handler requested abort after chunk {} failure: {}",
                            index, e
                        );
                        return Err(ServiceError::Internal(format!(
                            "Processing aborted at chunk {} due to error: {}",
                            index, e
                        )));
                    }
                    
                    warn!("Continuing processing despite chunk {} failure: {}", index, e);
                }
            }

            // Rate limiting
            if processed_chunks + failed_chunks < total_chunks {
                sleep(self.config.delay_between_chunks).await;
            }
        }

        info!(
            "Completed processing: {} successful chunks, {} failed chunks",
            processed_chunks, failed_chunks
        );

        Ok(results)
    }

    /// Process items one by one with rate limiting (for very sensitive operations)
    pub async fn process_sequential<T, F, R>(&self, items: Vec<T>, processor: F) -> Result<Vec<R>, ServiceError>
    where
        T: Clone,
        F: Fn(T) -> Result<R, ServiceError>,
    {
        let total_items = items.len();
        info!("Processing {} items sequentially", total_items);

        let mut results = Vec::with_capacity(total_items);
        
        for (index, item) in items.into_iter().enumerate() {
            let mut attempts = 0;
            
            loop {
                attempts += 1;
                
                match processor(item.clone()) {
                    Ok(result) => {
                        results.push(result);
                        break;
                    }
                    Err(e) => {
                        if attempts >= self.config.max_retries {
                            error!("Item {} failed after {} attempts: {}", index, attempts, e);
                            return Err(e);
                        }
                        
                        warn!("Item {} failed on attempt {}, retrying: {}", index, attempts, e);
                        sleep(self.config.retry_delay * attempts).await;
                    }
                }
            }

            // Progress reporting
            if (index + 1) % 100 == 0 {
                info!("Processed {}/{} items", index + 1, total_items);
            }

            // Rate limiting
            if index + 1 < total_items {
                sleep(self.config.delay_between_chunks).await;
            }
        }

        info!("Successfully processed all {} items sequentially", total_items);
        Ok(results)
    }
}

/// Utility function for safe bulk database operations
pub async fn safe_bulk_operation<T, F, R>(
    items: Vec<T>,
    operation: F,
    chunk_size: Option<usize>,
) -> Result<Vec<R>, ServiceError>
where
    T: Clone + Send + 'static,
    F: Fn(Vec<T>) -> Result<Vec<R>, ServiceError> + Clone + Send + 'static,
    R: Send + 'static,
{
    let config = ChunkProcessorConfig {
        chunk_size: chunk_size.unwrap_or(50), // Smaller default for DB operations
        delay_between_chunks: Duration::from_millis(50),
        max_retries: 3,
        retry_delay: Duration::from_millis(500),
        max_concurrent_chunks: 3, // Conservative for DB
    };

    let processor = ChunkProcessor::new(config);
    processor.process_chunks(items, operation).await
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;

    #[tokio::test]
    async fn test_chunk_processing() {
        let processor = ChunkProcessor::with_defaults();
        let items: Vec<i32> = (1..=10).collect();
        
        let result = processor.process_chunks(items, |chunk| {
            Ok(chunk.into_iter().map(|x| x * 2).collect())
        }).await;

        assert!(result.is_ok());
        let doubled: Vec<i32> = result.unwrap();
        assert_eq!(doubled, vec![2, 4, 6, 8, 10, 12, 14, 16, 18, 20]);
    }

    #[tokio::test]
    async fn test_chunk_processing_with_failure() {
        let processor = ChunkProcessor::with_defaults();
        let items: Vec<i32> = (1..=5).collect();
        
        let result = processor.process_chunks(items, |_chunk| {
            Err(ServiceError::Internal("Test error".to_string()))
        }).await;

        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_sequential_processing() {
        let processor = ChunkProcessor::with_defaults();
        let items: Vec<i32> = (1..=5).collect();
        
        let result = processor.process_sequential(items, |item| {
            Ok(item * 3)
        }).await;

        assert!(result.is_ok());
        let tripled: Vec<i32> = result.unwrap();
        assert_eq!(tripled, vec![3, 6, 9, 12, 15]);
    }
}
