use crate::{
    database::Database,
    models::{
        BillingAccount, BillingAccountResponse, BillingResponse, Invoice, InvoiceResponse,
        PaginatedResponse, PaginationQuery, UsageRecord,
    },
    services::{ServiceError, ServiceResult},
    utils::Pagination,
};
use bson::{doc, oid::ObjectId};
use mongodb::Collection;
use tracing::instrument;
use futures::TryStreamExt;

pub struct BillingService {
    billing_accounts: Collection<BillingAccount>,
    invoices: Collection<Invoice>,
    usage_records: Collection<UsageRecord>,
}

impl BillingService {
    pub fn new(database: &Database) -> Self {
        Self {
            billing_accounts: database.collection("billing_accounts"),
            invoices: database.collection("invoices"),
            usage_records: database.collection("usage_records"),
        }
    }


    #[instrument(skip(self))]
    pub async fn get_billing_info(&self, user_id: &str) -> ServiceResult<BillingResponse> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Get or create billing account
        let billing_account = self
            .billing_accounts
            .find_one(doc! { "user_id": user_object_id }, None)
            .await?
            .unwrap_or_else(|| BillingAccount {
                id: None,
                user_id: user_object_id,
                balance: 0.0,
                pending_charges: 0.0,
                last_payment_date: None,
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
            });

        // Calculate current usage
        let current_usage = self.calculate_current_usage(user_object_id).await?;

        // Get estimated monthly cost
        let estimated_monthly_cost = self.calculate_estimated_monthly_cost(user_object_id).await?;

        // Get recent invoices (last 5)
        let cursor = self
            .invoices
            .find(doc! { "user_id": user_object_id }, None)
            .await?;

        let recent_invoices: Vec<Invoice> = cursor
            .try_collect()
            .await?;

        let recent_invoice_responses: Vec<InvoiceResponse> = recent_invoices
            .into_iter()
            .map(|invoice| invoice.into())
            .collect();

        Ok(BillingResponse {
            account: BillingAccountResponse {
                balance: billing_account.balance,
                pending_charges: billing_account.pending_charges,
                last_payment_date: billing_account.last_payment_date,
            },
            current_usage,
            estimated_monthly_cost,
            recent_invoices: recent_invoice_responses,
        })
    }

    #[instrument(skip(self))]
    pub async fn list_user_invoices(
        &self,
        user_id: &str,
        pagination: PaginationQuery,
    ) -> ServiceResult<PaginatedResponse<InvoiceResponse>> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let page = pagination.page.unwrap_or(1);
        let per_page = pagination.per_page.unwrap_or(20);

        // Get total count
        let total = self
            .invoices
            .count_documents(doc! { "user_id": user_object_id }, None)
            .await?;

        // Get paginated results
        let pagination_info = Pagination::new(page, per_page, total);
        let cursor = self
            .invoices
            .find(doc! { "user_id": user_object_id }, None)
            .await?;

        let invoices: Vec<Invoice> = cursor
            .try_collect()
            .await?;

        let invoice_responses: Vec<InvoiceResponse> = invoices
            .into_iter()
            .map(|invoice| invoice.into())
            .collect();

        Ok(PaginatedResponse {
            data: invoice_responses,
            meta: crate::models::PaginationMeta {
                page: pagination_info.page,
                per_page: pagination_info.per_page,
                total: pagination_info.total,
                total_pages: pagination_info.total_pages,
            },
        })
    }

    #[instrument(skip(self))]
    pub async fn get_user_invoice(
        &self,
        user_id: &str,
        invoice_id: &str,
    ) -> ServiceResult<InvoiceResponse> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        let invoice_object_id = ObjectId::parse_str(invoice_id)
            .map_err(|_| ServiceError::Validation("Invalid invoice ID".to_string()))?;

        let invoice = self
            .invoices
            .find_one(
                doc! { 
                    "_id": invoice_object_id,
                    "user_id": user_object_id 
                },
                None,
            )
            .await?
            .ok_or_else(|| ServiceError::NotFound("Invoice not found".to_string()))?;

        Ok(invoice.into())
    }

    #[instrument(skip(self, params))]
    pub async fn get_usage_data(
        &self,
        user_id: &str,
        params: serde_json::Value,
    ) -> ServiceResult<serde_json::Value> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Extract date range from params
        let start_date = params
            .get("start_date")
            .and_then(|v| v.as_str())
            .unwrap_or("2024-01-01");
        let end_date = params
            .get("end_date")
            .and_then(|v| v.as_str())
            .unwrap_or("2024-12-31");

        // Get usage records for the period
        let usage_records: Vec<UsageRecord> = self
            .usage_records
            .find(
                doc! { 
                    "user_id": user_object_id,
                    "recorded_at": {
                        "$gte": start_date,
                        "$lte": end_date
                    }
                },
                None,
            )
            .await?
            .try_collect()
            .await?;

        // Aggregate usage data
        let mut total_cost = 0.0;
        let mut usage_by_service = std::collections::HashMap::new();

        for record in usage_records {
            total_cost += record.cost;
            let service_usage = usage_by_service
                .entry(record.service_type)
                .or_insert_with(|| serde_json::json!({
                    "quantity": 0.0,
                    "cost": 0.0
                }));

            if let Some(obj) = service_usage.as_object_mut() {
                if let Some(quantity) = obj.get_mut("quantity") {
                    *quantity = serde_json::Value::Number(
                        serde_json::Number::from_f64(
                            quantity.as_f64().unwrap_or(0.0) + record.quantity
                        ).unwrap()
                    );
                }
                if let Some(cost) = obj.get_mut("cost") {
                    *cost = serde_json::Value::Number(
                        serde_json::Number::from_f64(
                            cost.as_f64().unwrap_or(0.0) + record.cost
                        ).unwrap()
                    );
                }
            }
        }

        Ok(serde_json::json!({
            "total_cost": total_cost,
            "usage_by_service": usage_by_service,
            "period": {
                "start_date": start_date,
                "end_date": end_date
            }
        }))
    }

    async fn calculate_current_usage(&self, user_id: ObjectId) -> ServiceResult<f64> {
        // Calculate current month usage
        let current_month = chrono::Utc::now().format("%Y-%m").to_string();
        
        let pipeline = vec![
            doc! {
                "$match": {
                    "user_id": user_id,
                    "billing_period": current_month
                }
            },
            doc! {
                "$group": {
                    "_id": null,
                    "total_cost": { "$sum": "$cost" }
                }
            }
        ];

        let mut cursor = self.usage_records.aggregate(pipeline, None).await?;
        if let Some(result) = cursor.try_next().await? {
            Ok(result.get_f64("total_cost").unwrap_or(0.0))
        } else {
            Ok(0.0)
        }
    }

    async fn calculate_estimated_monthly_cost(&self, user_id: ObjectId) -> ServiceResult<f64> {
        use bson::doc;
        use futures::TryStreamExt;

        let mut total_cost = 0.0;

        // Calculate application costs
        let applications_collection: mongodb::Collection<crate::models::Application> =
            self.database.collection("applications");

        let mut app_cursor = applications_collection
            .find(doc! { "user_id": user_id, "status": { "$ne": "deleted" } }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        while let Some(app) = app_cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
            let app_cost = self.calculate_application_monthly_cost(&app).await?;
            total_cost += app_cost;
        }

        // Calculate database costs
        let databases_collection: mongodb::Collection<crate::models::Database> =
            self.database.collection("databases");

        let mut db_cursor = databases_collection
            .find(doc! { "user_id": user_id, "status": { "$ne": "deleted" } }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        while let Some(db) = db_cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
            let db_cost = self.calculate_database_monthly_cost(&db).await?;
            total_cost += db_cost;
        }

        // Calculate storage costs
        let disks_collection: mongodb::Collection<crate::models::Disk> =
            self.database.collection("disks");

        let mut disk_cursor = disks_collection
            .find(doc! { "user_id": user_id, "status": { "$ne": "deleted" } }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        while let Some(disk) = disk_cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
            let disk_cost = self.calculate_disk_monthly_cost(&disk).await?;
            total_cost += disk_cost;
        }

        // Calculate bandwidth costs
        let bandwidth_cost = self.calculate_bandwidth_monthly_cost(user_id).await?;
        total_cost += bandwidth_cost;

        Ok(total_cost)
    }

    async fn calculate_application_monthly_cost(&self, app: &crate::models::Application) -> ServiceResult<f64> {
        // Base cost per application based on plan
        let base_cost = match app.runtime_config.plan.as_str() {
            "free" => 0.0,
            "starter" => 7.0,
            "standard" => 25.0,
            "pro" => 85.0,
            "pro_plus" => 200.0,
            _ => 25.0, // Default to standard
        };

        // Additional cost for extra instances
        let instance_cost = if app.runtime_config.instances > 1 {
            (app.runtime_config.instances - 1) as f64 * base_cost * 0.8 // 20% discount for additional instances
        } else {
            0.0
        };

        // Memory-based pricing
        let memory_cost = if app.runtime_config.memory_mb > 512 {
            let extra_gb = (app.runtime_config.memory_mb - 512) as f64 / 1024.0;
            extra_gb * 10.0 // $10 per GB above 512MB
        } else {
            0.0
        };

        Ok(base_cost + instance_cost + memory_cost)
    }

    async fn calculate_database_monthly_cost(&self, db: &crate::models::Database) -> ServiceResult<f64> {
        // Database pricing based on plan
        let cost = match db.plan.as_str() {
            "free" => 0.0,
            "starter" => 7.0,
            "standard" => 15.0,
            "pro" => 50.0,
            "pro_max" => 200.0,
            _ => 15.0, // Default to standard
        };

        Ok(cost)
    }

    async fn calculate_disk_monthly_cost(&self, disk: &crate::models::Disk) -> ServiceResult<f64> {
        // Storage pricing: $0.10 per GB per month
        let cost = disk.size_gb as f64 * 0.10;
        Ok(cost)
    }

    async fn calculate_bandwidth_monthly_cost(&self, user_id: ObjectId) -> ServiceResult<f64> {
        use bson::doc;

        // Get current month's bandwidth usage
        let start_of_month = chrono::Utc::now()
            .with_day(1)
            .unwrap()
            .with_hour(0)
            .unwrap()
            .with_minute(0)
            .unwrap()
            .with_second(0)
            .unwrap();

        // Query bandwidth usage (would be stored in a metrics collection)
        let metrics_collection: mongodb::Collection<bson::Document> =
            self.database.collection("bandwidth_metrics");

        let pipeline = vec![
            doc! {
                "$match": {
                    "user_id": user_id,
                    "timestamp": { "$gte": start_of_month }
                }
            },
            doc! {
                "$group": {
                    "_id": null,
                    "total_bytes": { "$sum": "$bytes_transferred" }
                }
            }
        ];

        let mut cursor = metrics_collection
            .aggregate(pipeline, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        let total_gb = if let Some(result) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
            let total_bytes = result.get_i64("total_bytes").unwrap_or(0) as f64;
            total_bytes / (1024.0 * 1024.0 * 1024.0) // Convert to GB
        } else {
            0.0
        };

        // Free tier: 100GB, then $0.09 per GB
        let cost = if total_gb > 100.0 {
            (total_gb - 100.0) * 0.09
        } else {
            0.0
        };

        Ok(cost)
    }
}
