use failsafe::{<PERSON><PERSON>reaker, Config, Error as FailsafeError};
use std::sync::Arc;
use std::time::Duration;
use dashmap::DashMap;
use tracing::{error, info, warn};
use crate::services::ServiceError;

#[derive(Clone)]
pub struct CircuitBreakerService {
    breakers: Arc<DashMap<String, Arc<CircuitBreaker<ServiceError>>>>,
}

impl CircuitBreakerService {
    pub fn new() -> Self {
        Self {
            breakers: Arc::new(DashMap::new()),
        }
    }

    pub fn get_or_create_breaker(&self, service_name: &str) -> Arc<CircuitBreaker<ServiceError>> {
        self.breakers
            .entry(service_name.to_string())
            .or_insert_with(|| {
                let config = match service_name {
                    "vultr_api" => Config::new()
                        .error_threshold(10)
                        .failure_rate_threshold(0.5)
                        .timeout(Duration::from_secs(30))
                        .minimum_request_threshold(5),
                    "database" => Config::new()
                        .error_threshold(5)
                        .failure_rate_threshold(0.3)
                        .timeout(Duration::from_secs(10))
                        .minimum_request_threshold(3),
                    "git_api" => Config::new()
                        .error_threshold(8)
                        .failure_rate_threshold(0.4)
                        .timeout(Duration::from_secs(20))
                        .minimum_request_threshold(4),
                    "build_system" => Config::new()
                        .error_threshold(15)
                        .failure_rate_threshold(0.6)
                        .timeout(Duration::from_secs(60))
                        .minimum_request_threshold(3),
                    _ => Config::new()
                        .error_threshold(10)
                        .failure_rate_threshold(0.5)
                        .timeout(Duration::from_secs(30))
                        .minimum_request_threshold(5),
                };

                info!("Created circuit breaker for service: {}", service_name);
                Arc::new(CircuitBreaker::new(config))
            })
            .clone()
    }

    pub async fn call<F, T>(&self, service_name: &str, operation: F) -> Result<T, ServiceError>
    where
        F: FnOnce() -> Result<T, ServiceError>,
    {
        let breaker = self.get_or_create_breaker(service_name);
        
        match breaker.call(operation).await {
            Ok(result) => Ok(result),
            Err(FailsafeError::Rejected) => {
                warn!("Circuit breaker rejected call to service: {}", service_name);
                Err(ServiceError::ExternalApi(format!(
                    "Service {} is currently unavailable (circuit breaker open)",
                    service_name
                )))
            }
            Err(FailsafeError::TimedOut) => {
                warn!("Circuit breaker timed out for service: {}", service_name);
                Err(ServiceError::ExternalApi(format!(
                    "Service {} timed out",
                    service_name
                )))
            }
            Err(FailsafeError::InnerError(e)) => {
                error!("Service {} failed: {}", service_name, e);
                Err(e)
            }
        }
    }

    pub fn get_breaker_status(&self, service_name: &str) -> BreakerStatus {
        if let Some(breaker) = self.breakers.get(service_name) {
            let state = breaker.state();
            BreakerStatus {
                service_name: service_name.to_string(),
                state: format!("{:?}", state),
                failure_count: breaker.failure_count(),
                success_count: breaker.success_count(),
                is_open: matches!(state, failsafe::State::Open),
            }
        } else {
            BreakerStatus {
                service_name: service_name.to_string(),
                state: "NotInitialized".to_string(),
                failure_count: 0,
                success_count: 0,
                is_open: false,
            }
        }
    }

    pub fn get_all_breaker_status(&self) -> Vec<BreakerStatus> {
        self.breakers
            .iter()
            .map(|entry| self.get_breaker_status(entry.key()))
            .collect()
    }

    pub fn reset_breaker(&self, service_name: &str) -> bool {
        if let Some(breaker) = self.breakers.get(service_name) {
            breaker.reset();
            info!("Reset circuit breaker for service: {}", service_name);
            true
        } else {
            false
        }
    }
}

#[derive(Debug, Clone, serde::Serialize)]
pub struct BreakerStatus {
    pub service_name: String,
    pub state: String,
    pub failure_count: u64,
    pub success_count: u64,
    pub is_open: bool,
}

impl Default for CircuitBreakerService {
    fn default() -> Self {
        Self::new()
    }
}

// Macro for easy circuit breaker usage
#[macro_export]
macro_rules! with_circuit_breaker {
    ($breaker_service:expr, $service_name:expr, $operation:expr) => {
        $breaker_service.call($service_name, || $operation).await
    };
}

// Helper trait for services to use circuit breakers
pub trait CircuitBreakerAware {
    fn circuit_breaker(&self) -> &CircuitBreakerService;

    async fn call_with_breaker<F, T>(&self, service_name: &str, operation: F) -> Result<T, ServiceError>
    where
        F: FnOnce() -> Result<T, ServiceError>,
    {
        self.circuit_breaker().call(service_name, operation).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;

    #[tokio::test]
    async fn test_circuit_breaker_success() {
        let service = CircuitBreakerService::new();
        
        let result = service.call("test_service", || {
            Ok("success".to_string())
        }).await;

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "success");
    }

    #[tokio::test]
    async fn test_circuit_breaker_failure() {
        let service = CircuitBreakerService::new();
        
        let result = service.call("test_service", || {
            Err(ServiceError::Internal("test error".to_string()))
        }).await;

        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_circuit_breaker_status() {
        let service = CircuitBreakerService::new();
        
        // Create a breaker by making a call
        let _ = service.call("test_service", || {
            Ok("success".to_string())
        }).await;

        let status = service.get_breaker_status("test_service");
        assert_eq!(status.service_name, "test_service");
        assert_eq!(status.success_count, 1);
        assert_eq!(status.failure_count, 0);
        assert!(!status.is_open);
    }

    #[tokio::test]
    async fn test_breaker_reset() {
        let service = CircuitBreakerService::new();
        
        // Create a breaker
        let _ = service.call("test_service", || {
            Ok("success".to_string())
        }).await;

        let reset_result = service.reset_breaker("test_service");
        assert!(reset_result);

        let reset_nonexistent = service.reset_breaker("nonexistent_service");
        assert!(!reset_nonexistent);
    }
}
