use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Blueprint {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub name: String,
    pub status: BlueprintStatus,
    pub auto_sync: bool,
    pub repo: String,
    pub branch: String,
    pub owner_id: ObjectId,
    pub resources: Vec<BlueprintResource>,
    pub last_sync: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum BlueprintStatus {
    Created,
    Paused,
    InSync,
    Syncing,
    Error,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BlueprintResource {
    pub id: String,
    pub name: String,
    #[serde(rename = "type")]
    pub resource_type: String, // web_service, postgres, redis, etc.
    pub config: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlueprintSync {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub blueprint_id: ObjectId,
    pub commit_id: String,
    pub state: SyncState,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
    pub resources_synced: i32,
    pub resources_total: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum SyncState {
    Created,
    Pending,
    Running,
    Success,
    Error,
}

// Render.yaml structure models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RenderYaml {
    pub services: Option<Vec<ServiceConfig>>,
    pub databases: Option<Vec<DatabaseConfig>>,
    pub env_groups: Option<Vec<EnvGroupConfig>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    #[serde(rename = "type")]
    pub service_type: ServiceType,
    pub name: String,
    pub env: Option<String>,
    pub plan: Option<String>,
    pub region: Option<String>,
    pub branch: Option<String>,
    pub build_command: Option<String>,
    pub start_command: Option<String>,
    pub env_vars: Option<Vec<EnvVarConfig>>,
    pub domains: Option<Vec<DomainConfig>>,
    pub disk: Option<DiskConfig>,
    pub health_check_path: Option<String>,
    pub num_instances: Option<i32>,
    pub auto_deploy: Option<bool>,
    pub dockerfile_path: Option<String>,
    pub root_dir: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum ServiceType {
    WebService,
    PrivateService,
    BackgroundWorker,
    CronJob,
    StaticSite,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub name: String,
    pub plan: Option<String>,
    pub region: Option<String>,
    pub version: Option<String>,
    pub database_name: Option<String>,
    pub database_user: Option<String>,
    pub ip_allow_list: Option<Vec<IpAllowConfig>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvGroupConfig {
    pub name: String,
    pub env_vars: Vec<EnvVarConfig>,
    pub secret_files: Option<Vec<SecretFileConfig>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvVarConfig {
    pub key: String,
    pub value: Option<String>,
    pub from_service: Option<String>,
    pub from_database: Option<String>,
    pub from_group: Option<String>,
    pub property: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecretFileConfig {
    pub name: String,
    pub content: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DomainConfig {
    pub name: String,
    #[serde(rename = "type")]
    pub domain_type: Option<String>, // apex, subdomain
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskConfig {
    pub name: String,
    pub mount_path: String,
    pub size_gb: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpAllowConfig {
    pub cidr_block: String,
    pub description: String,
}

// Blueprint validation and parsing
impl RenderYaml {
    pub fn validate(&self) -> Result<(), String> {
        // Validate service names are unique
        if let Some(services) = &self.services {
            let mut names = std::collections::HashSet::new();
            for service in services {
                if !names.insert(&service.name) {
                    return Err(format!("Duplicate service name: {}", service.name));
                }
            }
        }

        // Validate database names are unique
        if let Some(databases) = &self.databases {
            let mut names = std::collections::HashSet::new();
            for database in databases {
                if !names.insert(&database.name) {
                    return Err(format!("Duplicate database name: {}", database.name));
                }
            }
        }

        // Validate env group names are unique
        if let Some(env_groups) = &self.env_groups {
            let mut names = std::collections::HashSet::new();
            for env_group in env_groups {
                if !names.insert(&env_group.name) {
                    return Err(format!("Duplicate env group name: {}", env_group.name));
                }
            }
        }

        Ok(())
    }

    pub fn to_blueprint_resources(&self) -> Vec<BlueprintResource> {
        let mut resources = Vec::new();

        // Convert services
        if let Some(services) = &self.services {
            for service in services {
                let mut config = HashMap::new();
                config.insert("type".to_string(), serde_json::json!(service.service_type));
                config.insert("name".to_string(), serde_json::json!(service.name));
                
                if let Some(plan) = &service.plan {
                    config.insert("plan".to_string(), serde_json::json!(plan));
                }
                if let Some(region) = &service.region {
                    config.insert("region".to_string(), serde_json::json!(region));
                }
                if let Some(build_command) = &service.build_command {
                    config.insert("build_command".to_string(), serde_json::json!(build_command));
                }
                if let Some(start_command) = &service.start_command {
                    config.insert("start_command".to_string(), serde_json::json!(start_command));
                }

                resources.push(BlueprintResource {
                    id: uuid::Uuid::new_v4().to_string(),
                    name: service.name.clone(),
                    resource_type: match service.service_type {
                        ServiceType::WebService => "web_service".to_string(),
                        ServiceType::PrivateService => "private_service".to_string(),
                        ServiceType::BackgroundWorker => "background_worker".to_string(),
                        ServiceType::CronJob => "cron_job".to_string(),
                        ServiceType::StaticSite => "static_site".to_string(),
                    },
                    config,
                });
            }
        }

        // Convert databases
        if let Some(databases) = &self.databases {
            for database in databases {
                let mut config = HashMap::new();
                config.insert("name".to_string(), serde_json::json!(database.name));
                
                if let Some(plan) = &database.plan {
                    config.insert("plan".to_string(), serde_json::json!(plan));
                }
                if let Some(region) = &database.region {
                    config.insert("region".to_string(), serde_json::json!(region));
                }
                if let Some(version) = &database.version {
                    config.insert("version".to_string(), serde_json::json!(version));
                }

                resources.push(BlueprintResource {
                    id: uuid::Uuid::new_v4().to_string(),
                    name: database.name.clone(),
                    resource_type: "postgres".to_string(), // Default to postgres
                    config,
                });
            }
        }

        // Convert env groups
        if let Some(env_groups) = &self.env_groups {
            for env_group in env_groups {
                let mut config = HashMap::new();
                config.insert("name".to_string(), serde_json::json!(env_group.name));
                config.insert("env_vars".to_string(), serde_json::json!(env_group.env_vars));

                resources.push(BlueprintResource {
                    id: uuid::Uuid::new_v4().to_string(),
                    name: env_group.name.clone(),
                    resource_type: "environment_group".to_string(),
                    config,
                });
            }
        }

        resources
    }
}
