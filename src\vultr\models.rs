use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// Account models
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VultrAccount {
    pub name: String,
    pub email: String,
    pub balance: f64,
    pub pending_charges: f64,
    pub last_payment_date: Option<String>,
    pub last_payment_amount: Option<f64>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VultrAccountResponse {
    pub account: VultrAccount,
}

// Account BGP models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrAccountBGP {
    pub bgp_available: bool,
    pub bgp_customer_count: u32,
    pub announcements: Vec<VultrBGPAnnouncement>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VultrBGPAnnouncement {
    pub id: String,
    pub description: String,
    pub ip_block: String,
    pub status: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct VultrAccountBGPResponse {
    pub bgp: VultrAccountBGP,
}

// Account Bandwidth models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrAccountBandwidth {
    pub incoming_bytes: u64,
    pub outgoing_bytes: u64,
    pub date_range: VultrBandwidthDateRange,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBandwidthDateRange {
    pub start_date: String,
    pub end_date: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrAccountBandwidthResponse {
    pub bandwidth: VultrAccountBandwidth,
}

// Instance models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInstance {
    pub id: String,
    pub os: String,
    pub ram: u32,
    pub disk: u32,
    pub main_ip: Option<String>,
    pub vcpu_count: u32,
    pub region: String,
    pub plan: String,
    pub date_created: String,
    pub status: String,
    pub allowed_bandwidth: u32,
    pub netmask_v4: Option<String>,
    pub gateway_v4: Option<String>,
    pub power_status: String,
    pub server_status: String,
    pub v6_network: Option<String>,
    pub v6_main_ip: Option<String>,
    pub v6_network_size: Option<u32>,
    pub label: String,
    pub internal_ip: Option<String>,
    pub kvm: Option<String>,
    pub hostname: Option<String>,
    pub tag: Option<String>,
    pub tags: Option<Vec<String>>,
    pub os_id: u32,
    pub app_id: Option<u32>,
    pub image_id: Option<String>,
    pub firewall_group_id: Option<String>,
    pub features: Option<Vec<String>>,
    pub user_data: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInstanceResponse {
    pub instance: VultrInstance,
}



#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateVultrInstanceRequest {
    pub region: String,
    pub plan: String,
    pub os_id: Option<u32>,
    pub image_id: Option<String>,
    pub label: Option<String>,
    pub hostname: Option<String>,
    pub tag: Option<String>,
    pub user_data: Option<String>,
    pub ssh_keys: Option<Vec<String>>,
    pub startup_script_id: Option<String>,
    pub firewall_group_id: Option<String>,
    pub enable_ipv6: Option<bool>,
    pub enable_private_network: Option<bool>,
    pub attach_private_network: Option<Vec<String>>,
    pub enable_ddos_protection: Option<bool>,
    pub backups: Option<String>,
    pub app_id: Option<u32>,
    pub snapshot_id: Option<String>,
    pub iso_id: Option<String>,
    pub script_id: Option<String>,
    pub activation_email: Option<bool>,
    pub ddos_protection: Option<bool>,
    pub enable_vpc: Option<bool>,
    pub attach_vpc: Option<Vec<String>>,
    pub tags: Option<Vec<String>>,
}

// Plan models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPlan {
    pub id: String,
    pub vcpu_count: u32,
    pub ram: u32,
    pub disk: u32,
    pub bandwidth: u32,
    pub monthly_cost: f64,
    #[serde(rename = "type")]
    pub plan_type: String,
    pub locations: Vec<String>,
    pub disk_count: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPlansResponse {
    pub plans: Vec<VultrPlan>,
    pub meta: VultrMeta,
}

// Region models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegion {
    pub id: String,
    pub city: String,
    pub country: String,
    pub continent: String,
    pub options: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegionAvailablePlans {
    pub available_plans: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegionsResponse {
    pub regions: Vec<VultrRegion>,
    pub meta: VultrMeta,
}

// OS models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrOS {
    pub id: u32,
    pub name: String,
    pub arch: String,
    pub family: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrOSResponse {
    pub os: Vec<VultrOS>,
    pub meta: VultrMeta,
}

// SSH Key models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrSSHKey {
    pub id: String,
    pub name: String,
    pub ssh_key: String,
    pub date_created: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrSSHKeysResponse {
    pub ssh_keys: Vec<VultrSSHKey>,
    pub meta: VultrMeta,
}

// Common models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrMeta {
    pub total: u32,
    pub links: Option<VultrLinks>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrLinks {
    pub next: Option<String>,
    pub prev: Option<String>,
}

// Backup models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBackup {
    pub id: String,
    pub date_created: String,
    pub description: String,
    pub size: u64,
    pub status: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBackupsResponse {
    pub backups: Vec<VultrBackup>,
    pub meta: VultrMeta,
}

// Bare Metal models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetal {
    pub id: String,
    pub os: String,
    pub ram: String,
    pub disk: String,
    pub main_ip: Option<String>,
    pub cpu_count: u32,
    pub region: String,
    pub plan: String,
    pub date_created: String,
    pub status: String,
    pub label: String,
    pub tag: Option<String>,
    pub mac_address: Option<String>,
    pub netmask_v4: Option<String>,
    pub gateway_v4: Option<String>,
    pub v6_network: Option<String>,
    pub v6_main_ip: Option<String>,
    pub v6_network_size: Option<u32>,
    pub os_id: u32,
    pub app_id: Option<u32>,
    pub image_id: Option<String>,
    pub features: Vec<String>,
    pub user_data: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalResponse {
    pub bare_metals: Vec<VultrBareMetal>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateBareMetalRequest {
    pub region: Option<String>,
    pub plan: Option<String>,
    pub os_id: Option<u32>,
    pub app_id: Option<u32>,
    pub snapshot_id: Option<String>,
    pub script_id: Option<String>,
    pub enable_ipv6: Option<bool>,
    pub ssh_key_ids: Option<Vec<String>>,
    pub user_data: Option<String>,
    pub label: Option<String>,
    pub hostname: Option<String>,
    pub tag: Option<String>,
    pub reserved_ipv4: Option<String>,
    pub activation_email: Option<bool>,
    pub hostname_prefix: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateBareMetalRequest {
    pub user_data: Option<String>,
    pub label: Option<String>,
    pub tag: Option<String>,
    pub os_id: Option<u32>,
    pub app_id: Option<u32>,
    pub image_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalIpv4Info {
    pub ip: String,
    pub netmask: String,
    pub gateway: String,
    pub type_: String,
    pub reverse: String,
    pub mac_address: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalIpv6Info {
    pub ip: String,
    pub network: String,
    pub network_size: u32,
    pub type_: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalBandwidth {
    pub incoming_bytes: u64,
    pub outgoing_bytes: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalUserData {
    pub data: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalUpgrades {
    pub os: Vec<u32>,
    pub applications: Vec<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalVncInfo {
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalVpcInfo {
    pub id: String,
    pub mac_address: String,
    pub ip_address: String,
}

// CDN models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrCDN {
    pub id: String,
    pub date_created: String,
    pub status: String,
    pub label: String,
    pub origin_scheme: String,
    pub origin_domain: String,
    pub cdn_domain: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrCDNResponse {
    pub cdns: Vec<VultrCDN>,
    pub meta: VultrMeta,
}

// Container Registry models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrContainerRegistry {
    pub id: String,
    pub name: String,
    pub urn: String,
    pub storage: VultrRegistryStorage,
    pub date_created: String,
    pub public: bool,
    pub root_user: VultrRegistryUser,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryStorage {
    pub used: VultrStorageUsage,
    pub allowed: VultrStorageUsage,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStorageUsage {
    pub bytes: u64,
    pub mb: f64,
    pub gb: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryUser {
    pub id: u32,
    pub username: String,
    pub password: String,
    pub root: bool,
}

// DNS models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSDomain {
    pub domain: String,
    pub date_created: String,
    pub dns_sec: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSRecord {
    pub id: String,
    pub type_: String,
    pub name: String,
    pub data: String,
    pub priority: Option<u32>,
    pub ttl: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSResponse {
    pub domains: Vec<VultrDNSDomain>,
    pub meta: VultrMeta,
}

// Firewall models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrFirewallGroup {
    pub id: String,
    pub description: String,
    pub date_created: String,
    pub date_modified: String,
    pub instance_count: u32,
    pub rule_count: u32,
    pub max_rule_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrFirewallRule {
    pub id: u32,
    pub type_: String,
    pub ip_type: String,
    pub action: String,
    pub protocol: String,
    pub port: String,
    pub source: String,
    pub notes: Option<String>,
}

// Kubernetes models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesCluster {
    pub id: String,
    pub label: String,
    pub date_created: String,
    pub cluster_subnet: String,
    pub service_subnet: String,
    pub ip: String,
    pub endpoint: String,
    pub version: String,
    pub region: String,
    pub status: String,
    pub node_pools: Vec<VultrNodePool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrNodePool {
    pub id: String,
    pub date_created: String,
    pub date_updated: String,
    pub label: String,
    pub tag: Option<String>,
    pub plan: String,
    pub status: String,
    pub node_quantity: u32,
    pub min_nodes: u32,
    pub max_nodes: u32,
    pub auto_scaler: bool,
    pub nodes: Vec<VultrKubernetesNode>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesNode {
    pub id: String,
    pub date_created: String,
    pub label: String,
    pub status: String,
}

// Load Balancer models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrLoadBalancer {
    pub id: String,
    pub date_created: String,
    pub region: String,
    pub label: String,
    pub status: String,
    pub ipv4: Option<String>,
    pub ipv6: Option<String>,
    pub generic_info: VultrLoadBalancerGenericInfo,
    pub health_check: VultrHealthCheck,
    pub has_ssl: bool,
    pub forwarding_rules: Vec<VultrForwardingRule>,
    pub firewall_rules: Vec<VultrFirewallRule>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrLoadBalancerGenericInfo {
    pub balancing_algorithm: String,
    pub ssl_redirect: bool,
    pub sticky_sessions: VultrStickySessions,
    pub proxy_protocol: bool,
    pub private_network: Option<String>,
    pub vpc: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStickySessions {
    pub cookie_name: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrHealthCheck {
    pub protocol: String,
    pub port: u32,
    pub path: String,
    pub check_interval: u32,
    pub response_timeout: u32,
    pub unhealthy_threshold: u32,
    pub healthy_threshold: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrForwardingRule {
    pub id: String,
    pub frontend_protocol: String,
    pub frontend_port: u32,
    pub backend_protocol: String,
    pub backend_port: u32,
}

// Error models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrError {
    pub error: String,
    pub status: u16,
}

// Managed Database models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrManagedDatabase {
    pub id: String,
    pub date_created: String,
    pub plan: String,
    pub plan_disk: u32,
    pub plan_ram: u32,
    pub plan_vcpus: u32,
    pub plan_replicas: u32,
    pub region: String,
    pub database_engine: String,
    pub database_engine_version: String,
    pub vpc_id: Option<String>,
    pub status: String,
    pub label: String,
    pub tag: Option<String>,
    pub dbname: String,
    pub host: String,
    pub public_host: Option<String>,
    pub user: String,
    pub password: String,
    pub port: String,
    pub maintenance_dow: String,
    pub maintenance_time: String,
    pub latest_backup: Option<String>,
    pub trusted_ips: Vec<String>,
    pub mysql_sql_modes: Option<Vec<String>>,
    pub mysql_require_primary_key: Option<bool>,
    pub mysql_slow_query_log: Option<bool>,
    pub mysql_long_query_time: Option<u32>,
    pub redis_eviction_policy: Option<String>,
}

// Object Storage models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrObjectStorage {
    pub id: String,
    pub date_created: String,
    pub cluster_id: u32,
    pub region: String,
    pub label: String,
    pub status: String,
    pub s3_hostname: String,
    pub s3_access_key: String,
    pub s3_secret_key: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateObjectStorageRequest {
    pub cluster_id: u32,
    pub label: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateObjectStorageRequest {
    pub label: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrObjectStorageCluster {
    pub id: u32,
    pub region: String,
    pub hostname: String,
    pub deploy: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrObjectStorageTier {
    pub id: String,
    pub name: String,
    pub monthly_price: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrObjectStoragesResponse {
    pub object_storages: Vec<VultrObjectStorage>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrObjectStorageClustersResponse {
    pub clusters: Vec<VultrObjectStorageCluster>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrObjectStorageTiersResponse {
    pub tiers: Vec<VultrObjectStorageTier>,
}

// VPC models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVPC {
    pub id: String,
    pub date_created: String,
    pub region: String,
    pub description: String,
    pub v4_subnet: String,
    pub v4_subnet_mask: u32,
}

// Block Storage models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBlockStorage {
    pub id: String,
    pub date_created: String,
    pub cost_per_month: f64,
    pub status: String,
    pub size_gb: u32,
    pub region: String,
    pub attached_to_instance: Option<String>,
    pub label: String,
    pub mount_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBlockStorageResponse {
    pub blocks: Vec<VultrBlockStorage>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateBlockStorageRequest {
    pub region: String,
    pub size_gb: u32,
    pub label: Option<String>,
    pub block_type: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateBlockStorageRequest {
    pub label: Option<String>,
    pub size_gb: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttachBlockStorageRequest {
    pub instance_id: String,
    pub live: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetachBlockStorageRequest {
    pub live: Option<bool>,
}

// Billing models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInvoiceItem {
    pub description: String,
    pub product: String,
    pub start_date: String,
    pub end_date: String,
    pub units: f64,
    pub unit_type: String,
    pub unit_price: f64,
    pub total: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInvoiceItemsResponse {
    pub invoice_items: Vec<VultrInvoiceItem>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPendingCharges {
    pub pending_charges: f64,
    pub billing_date: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPendingChargesResponse {
    pub billing: VultrPendingCharges,
}

// CDN Pull Zone models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPullZone {
    pub id: String,
    pub date_created: String,
    pub label: String,
    pub origin_scheme: String,
    pub origin_domain: String,
    pub origin_path: Option<String>,
    pub cdn_domain: String,
    pub status: String,
    pub regions: Vec<String>,
    pub cors: bool,
    pub gzip: bool,
    pub block_ai: bool,
    pub block_bad_bots: bool,
    pub vanity_domain: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPullZoneResponse {
    pub pullzones: Vec<VultrPullZone>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreatePullZoneRequest {
    pub label: String,
    pub origin_scheme: String,
    pub origin_domain: String,
    pub origin_path: Option<String>,
    pub regions: Option<Vec<String>>,
    pub cors: Option<bool>,
    pub gzip: Option<bool>,
    pub block_ai: Option<bool>,
    pub block_bad_bots: Option<bool>,
    pub vanity_domain: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdatePullZoneRequest {
    pub label: Option<String>,
    pub origin_scheme: Option<String>,
    pub origin_domain: Option<String>,
    pub origin_path: Option<String>,
    pub regions: Option<Vec<String>>,
    pub cors: Option<bool>,
    pub gzip: Option<bool>,
    pub block_ai: Option<bool>,
    pub block_bad_bots: Option<bool>,
    pub vanity_domain: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PurgePullZoneRequest {
    pub files: Vec<String>,
}

// CDN Push Zone models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPushZone {
    pub id: String,
    pub date_created: String,
    pub label: String,
    pub regions: Vec<String>,
    pub upload_endpoint: String,
    pub cdn_domain: String,
    pub status: String,
    pub vanity_domain: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPushZoneResponse {
    pub pushzones: Vec<VultrPushZone>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreatePushZoneRequest {
    pub label: String,
    pub regions: Option<Vec<String>>,
    pub vanity_domain: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdatePushZoneRequest {
    pub label: Option<String>,
    pub regions: Option<Vec<String>>,
    pub vanity_domain: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPushZoneFile {
    pub name: String,
    pub size: u64,
    pub date_created: String,
    pub content_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPushZoneFilesResponse {
    pub files: Vec<VultrPushZoneFile>,
    pub meta: VultrMeta,
}

// Enhanced Container Registry models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateRegistryRequest {
    pub name: String,
    pub public: Option<bool>,
    pub region: String,
    pub plan: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateRegistryRequest {
    pub public: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryReplication {
    pub id: String,
    pub date_created: String,
    pub region: String,
    pub status: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryReplicationResponse {
    pub replications: Vec<VultrRegistryReplication>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateReplicationRequest {
    pub region: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryRepository {
    pub name: String,
    pub image: String,
    pub description: Option<String>,
    pub added: String,
    pub updated: String,
    pub pull_count: u64,
    pub artifact_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryRepositoryResponse {
    pub repositories: Vec<VultrRegistryRepository>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateRepositoryRequest {
    pub description: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDockerCredentials {
    pub auths: HashMap<String, VultrDockerAuth>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDockerAuth {
    pub username: String,
    pub password: String,
    pub email: String,
    pub auth: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesDockerCredentials {
    #[serde(rename = "apiVersion")]
    pub api_version: String,
    pub kind: String,
    pub metadata: VultrK8sMetadata,
    pub data: HashMap<String, String>,
    #[serde(rename = "type")]
    pub secret_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrK8sMetadata {
    pub name: String,
    pub namespace: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateRegistryPasswordRequest {
    pub password: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryRobot {
    pub name: String,
    pub secret: String,
    pub level: String,
    pub description: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryRobotResponse {
    pub robots: Vec<VultrRegistryRobot>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateRobotRequest {
    pub level: Option<String>,
    pub description: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryArtifact {
    pub digest: String,
    pub media_type: String,
    pub size: u64,
    pub tags: Vec<String>,
    pub push_time: String,
    pub pull_time: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryArtifactResponse {
    pub artifacts: Vec<VultrRegistryArtifact>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryRegion {
    pub id: String,
    pub name: String,
    pub country: String,
    pub continent: String,
    pub available: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryRegionResponse {
    pub regions: Vec<VultrRegistryRegion>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryPlan {
    pub id: String,
    pub name: String,
    pub storage_gb: u32,
    pub bandwidth_gb: u32,
    pub repositories: u32,
    pub monthly_cost: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryPlanResponse {
    pub plans: Vec<VultrRegistryPlan>,
    pub meta: VultrMeta,
}

// Enhanced DNS models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSDomainDetailed {
    pub domain: String,
    pub date_created: String,
    pub dns_sec: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSDomainsResponse {
    pub domains: Vec<VultrDNSDomainDetailed>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateDNSDomainRequest {
    pub domain: String,
    pub ip: Option<String>,
    pub dns_sec: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateDNSDomainRequest {
    pub dns_sec: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSSOA {
    pub nsprimary: String,
    pub email: String,
    pub serial: u64,
    pub refresh: u32,
    pub retry: u32,
    pub expire: u32,
    pub ttl: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSSOAResponse {
    pub dns_soa: VultrDNSSOA,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateDNSSOARequest {
    pub nsprimary: Option<String>,
    pub email: Option<String>,
    pub refresh: Option<u32>,
    pub retry: Option<u32>,
    pub expire: Option<u32>,
    pub ttl: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSSec {
    pub dns_keys: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSSecResponse {
    pub dns_sec: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSRecordDetailed {
    pub id: String,
    #[serde(rename = "type")]
    pub record_type: String,
    pub name: String,
    pub data: String,
    pub priority: Option<u32>,
    pub ttl: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSRecordsResponse {
    pub records: Vec<VultrDNSRecordDetailed>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateDNSRecordRequest {
    pub name: String,
    #[serde(rename = "type")]
    pub record_type: String,
    pub data: String,
    pub ttl: Option<u32>,
    pub priority: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateDNSRecordRequest {
    pub name: Option<String>,
    pub data: Option<String>,
    pub ttl: Option<u32>,
    pub priority: Option<u32>,
}

// Enhanced Firewall models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrFirewallGroupDetailed {
    pub id: String,
    pub description: String,
    pub date_created: String,
    pub date_modified: String,
    pub instance_count: u32,
    pub rule_count: u32,
    pub max_rule_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrFirewallGroupsResponse {
    pub firewall_groups: Vec<VultrFirewallGroupDetailed>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateFirewallGroupRequest {
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateFirewallGroupRequest {
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrFirewallRuleDetailed {
    pub id: u32,
    pub ip_type: String,
    pub protocol: String,
    pub subnet: String,
    pub subnet_size: u32,
    pub port: String,
    pub notes: Option<String>,
    pub source: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrFirewallRulesResponse {
    pub firewall_rules: Vec<VultrFirewallRuleDetailed>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateFirewallRuleRequest {
    pub ip_type: String,
    pub protocol: String,
    pub subnet: String,
    pub subnet_size: u32,
    pub port: String,
    pub notes: Option<String>,
    pub source: Option<String>,
}

// Enhanced Instance models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInstanceDetailed {
    pub id: String,
    pub os: String,
    pub ram: u32,
    pub disk: u32,
    pub main_ip: Option<String>,
    pub vcpu_count: u32,
    pub region: String,
    pub plan: String,
    pub date_created: String,
    pub status: String,
    pub allowed_bandwidth: u32,
    pub netmask_v4: Option<String>,
    pub gateway_v4: Option<String>,
    pub power_status: String,
    pub server_status: String,
    pub v6_network: Option<String>,
    pub v6_main_ip: Option<String>,
    pub v6_network_size: Option<u32>,
    pub label: String,
    pub internal_ip: Option<String>,
    pub kvm: Option<String>,
    pub hostname: Option<String>,
    pub tag: Option<String>,
    pub tags: Option<Vec<String>>,
    pub os_id: u32,
    pub app_id: Option<u32>,
    pub image_id: Option<String>,
    pub firewall_group_id: Option<String>,
    pub features: Option<Vec<String>>,
    pub user_data: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInstancesResponse {
    pub instances: Vec<VultrInstanceDetailed>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateInstanceRequest {
    pub region: String,
    pub plan: String,
    pub os_id: Option<u32>,
    pub image_id: Option<String>,
    pub label: Option<String>,
    pub hostname: Option<String>,
    pub tag: Option<String>,
    pub user_data: Option<String>,
    pub ssh_keys: Option<Vec<String>>,
    pub startup_script_id: Option<String>,
    pub firewall_group_id: Option<String>,
    pub enable_ipv6: Option<bool>,
    pub enable_private_network: Option<bool>,
    pub attach_private_network: Option<Vec<String>>,
    pub enable_ddos_protection: Option<bool>,
    pub backups: Option<String>,
    pub app_id: Option<u32>,
    pub snapshot_id: Option<String>,
    pub iso_id: Option<String>,
    pub script_id: Option<String>,
    pub activation_email: Option<bool>,
    pub ddos_protection: Option<bool>,
    pub enable_vpc: Option<bool>,
    pub attach_vpc: Option<Vec<String>>,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateInstanceRequest {
    pub label: Option<String>,
    pub tag: Option<String>,
    pub plan: Option<String>,
    pub enable_ddos_protection: Option<bool>,
    pub attach_private_network: Option<Vec<String>>,
    pub detach_private_network: Option<Vec<String>>,
    pub enable_backups: Option<bool>,
    pub user_data: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceBandwidth {
    pub incoming_bytes: u64,
    pub outgoing_bytes: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceBandwidthResponse {
    pub bandwidth: HashMap<String, InstanceBandwidth>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceNeighbor {
    pub id: String,
    pub label: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceNeighborsResponse {
    pub neighbors: Vec<InstanceNeighbor>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceVPC {
    pub id: String,
    pub mac_address: String,
    pub ip_address: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceVPCsResponse {
    pub vpcs: Vec<InstanceVPC>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceISOStatus {
    pub iso_id: Option<String>,
    pub state: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttachISORequest {
    pub iso_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttachVPCRequest {
    pub vpc_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetachVPCRequest {
    pub vpc_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceBackupSchedule {
    pub enabled: bool,
    pub cron_type: Option<String>,
    pub next_scheduled_time_utc: Option<String>,
    pub hour: Option<u32>,
    pub dow: Option<u32>,
    pub dom: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateBackupScheduleRequest {
    pub cron_type: String,
    pub hour: Option<u32>,
    pub dow: Option<u32>,
    pub dom: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RestoreInstanceRequest {
    pub backup_id: Option<String>,
    pub snapshot_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceIPv4 {
    pub ip: String,
    pub netmask: String,
    pub gateway: String,
    #[serde(rename = "type")]
    pub ip_type: String,
    pub reverse: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceIPv4Response {
    pub ipv4s: Vec<InstanceIPv4>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateInstanceIPv4Request {
    pub reboot: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceIPv6 {
    pub ip: String,
    pub network: String,
    pub network_size: u32,
    #[serde(rename = "type")]
    pub ip_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceIPv6Response {
    pub ipv6s: Vec<InstanceIPv6>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateReverseIPv6Request {
    pub ip: String,
    pub reverse: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceIPv6Reverse {
    pub ip: String,
    pub reverse: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceIPv6ReverseResponse {
    pub reverse_ipv6s: Vec<InstanceIPv6Reverse>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateReverseIPv4Request {
    pub ip: String,
    pub reverse: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceUserData {
    pub data: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceUpgrades {
    pub applications: Vec<u32>,
    pub os: Vec<u32>,
    pub plans: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceJob {
    pub id: String,
    pub date_created: String,
    pub date_modified: String,
    pub status: String,
    pub progress: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkInstanceActionRequest {
    pub instance_ids: Vec<String>,
}

// ISO models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrISO {
    pub id: String,
    pub date_created: String,
    pub filename: String,
    pub size: u64,
    pub md5sum: String,
    pub sha512sum: String,
    pub status: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrISOsResponse {
    pub isos: Vec<VultrISO>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateISORequest {
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPublicISO {
    pub id: String,
    pub name: String,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPublicISOsResponse {
    pub public_isos: Vec<VultrPublicISO>,
    pub meta: VultrMeta,
}

// Enhanced Kubernetes models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesClusterDetailed {
    pub id: String,
    pub label: String,
    pub date_created: String,
    pub cluster_subnet: String,
    pub service_subnet: String,
    pub ip: String,
    pub endpoint: String,
    pub version: String,
    pub region: String,
    pub status: String,
    pub ha_controlplanes: bool,
    pub enable_firewall: bool,
    pub node_pools: Vec<VultrNodePoolDetailed>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesClustersResponse {
    pub vke_clusters: Vec<VultrKubernetesClusterDetailed>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateKubernetesClusterRequest {
    pub label: String,
    pub region: String,
    pub version: String,
    pub ha_controlplanes: Option<bool>,
    pub enable_firewall: Option<bool>,
    pub node_pools: Vec<CreateNodePoolRequest>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateKubernetesClusterRequest {
    pub label: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrNodePoolDetailed {
    pub id: String,
    pub date_created: String,
    pub date_updated: String,
    pub label: String,
    pub tag: Option<String>,
    pub plan: String,
    pub status: String,
    pub node_quantity: u32,
    pub min_nodes: u32,
    pub max_nodes: u32,
    pub auto_scaler: bool,
    pub nodes: Vec<VultrKubernetesNodeDetailed>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesNodeDetailed {
    pub id: String,
    pub date_created: String,
    pub label: String,
    pub status: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateNodePoolRequest {
    pub node_quantity: u32,
    pub label: String,
    pub plan: String,
    pub tag: Option<String>,
    pub auto_scaler: Option<bool>,
    pub min_nodes: Option<u32>,
    pub max_nodes: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrNodePoolsResponse {
    pub node_pools: Vec<VultrNodePoolDetailed>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateNodePoolRequest {
    pub node_quantity: Option<u32>,
    pub tag: Option<String>,
    pub auto_scaler: Option<bool>,
    pub min_nodes: Option<u32>,
    pub max_nodes: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesResources {
    pub block_storage: Vec<VultrKubernetesBlockStorage>,
    pub load_balancers: Vec<VultrKubernetesLoadBalancer>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesBlockStorage {
    pub id: String,
    pub date_created: String,
    pub size_gb: u32,
    pub status: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesLoadBalancer {
    pub id: String,
    pub date_created: String,
    pub region: String,
    pub label: String,
    pub status: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesUpgrades {
    pub available_upgrades: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StartKubernetesUpgradeRequest {
    pub upgrade_version: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesConfig {
    pub kube_config: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesVersions {
    pub versions: Vec<String>,
}

// Enhanced Load Balancer models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrLoadBalancerDetailed {
    pub id: String,
    pub date_created: String,
    pub region: String,
    pub label: String,
    pub status: String,
    pub ipv4: Option<String>,
    pub ipv6: Option<String>,
    pub generic_info: VultrLoadBalancerGenericInfo,
    pub health_check: VultrHealthCheck,
    pub has_ssl: bool,
    pub http2: bool,
    pub nodes: u32,
    pub forwarding_rules: Vec<VultrForwardingRuleDetailed>,
    pub firewall_rules: Vec<VultrLoadBalancerFirewallRule>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrLoadBalancersResponse {
    pub load_balancers: Vec<VultrLoadBalancerDetailed>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateLoadBalancerRequest {
    pub region: String,
    pub label: String,
    pub balancing_algorithm: Option<String>,
    pub ssl_redirect: Option<bool>,
    pub http2: Option<bool>,
    pub nodes: Option<u32>,
    pub proxy_protocol: Option<bool>,
    pub health_check: Option<CreateHealthCheckRequest>,
    pub forwarding_rules: Vec<CreateForwardingRuleRequest>,
    pub sticky_session: Option<CreateStickySessionRequest>,
    pub ssl: Option<CreateSSLRequest>,
    pub firewall_rules: Option<Vec<CreateLoadBalancerFirewallRuleRequest>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateLoadBalancerRequest {
    pub label: Option<String>,
    pub balancing_algorithm: Option<String>,
    pub ssl_redirect: Option<bool>,
    pub http2: Option<bool>,
    pub nodes: Option<u32>,
    pub proxy_protocol: Option<bool>,
    pub health_check: Option<CreateHealthCheckRequest>,
    pub sticky_session: Option<CreateStickySessionRequest>,
    pub ssl: Option<CreateSSLRequest>,
    pub firewall_rules: Option<Vec<CreateLoadBalancerFirewallRuleRequest>>,
}



#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStickySession {
    pub cookie_name: String,
}



#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateHealthCheckRequest {
    pub protocol: String,
    pub port: u32,
    pub path: Option<String>,
    pub check_interval: Option<u32>,
    pub response_timeout: Option<u32>,
    pub unhealthy_threshold: Option<u32>,
    pub healthy_threshold: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrForwardingRuleDetailed {
    pub id: String,
    pub frontend_protocol: String,
    pub frontend_port: u32,
    pub backend_protocol: String,
    pub backend_port: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateForwardingRuleRequest {
    pub frontend_protocol: String,
    pub frontend_port: u32,
    pub backend_protocol: String,
    pub backend_port: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrLoadBalancerFirewallRule {
    pub id: String,
    pub port: u32,
    pub source: String,
    pub ip_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrLoadBalancerFirewallRulesResponse {
    pub firewall_rules: Vec<VultrLoadBalancerFirewallRule>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateLoadBalancerFirewallRuleRequest {
    pub port: u32,
    pub source: String,
    pub ip_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateStickySessionRequest {
    pub cookie_name: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSSLRequest {
    pub private_key: String,
    pub certificate: String,
    pub chain: Option<String>,
}

// Managed Database models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabasePlan {
    pub id: String,
    pub number_of_nodes: u32,
    pub type_: String,
    pub vcpu_count: u32,
    pub ram: u32,
    pub disk: u32,
    pub monthly_cost: f64,
    pub supported_engines: HashMap<String, Vec<String>>,
    pub max_connections: HashMap<String, u32>,
    pub locations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabasePlansResponse {
    pub plans: Vec<VultrDatabasePlan>,
    pub meta: VultrMeta,
}



#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrManagedDatabasesResponse {
    pub databases: Vec<VultrManagedDatabase>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateManagedDatabaseRequest {
    pub database_engine: String,
    pub database_engine_version: String,
    pub region: String,
    pub plan: String,
    pub label: String,
    pub tag: Option<String>,
    pub vpc_id: Option<String>,
    pub maintenance_dow: Option<String>,
    pub maintenance_time: Option<String>,
    pub trusted_ips: Option<Vec<String>>,
    pub mysql_sql_modes: Option<Vec<String>>,
    pub mysql_require_primary_key: Option<bool>,
    pub mysql_slow_query_log: Option<bool>,
    pub mysql_long_query_time: Option<f64>,
    pub redis_eviction_policy: Option<String>,
    pub cluster_time_zone: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateManagedDatabaseRequest {
    pub label: Option<String>,
    pub tag: Option<String>,
    pub maintenance_dow: Option<String>,
    pub maintenance_time: Option<String>,
    pub trusted_ips: Option<Vec<String>>,
    pub mysql_sql_modes: Option<Vec<String>>,
    pub mysql_require_primary_key: Option<bool>,
    pub mysql_slow_query_log: Option<bool>,
    pub mysql_long_query_time: Option<f64>,
    pub redis_eviction_policy: Option<String>,
    pub cluster_time_zone: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrFerretDBCredentials {
    pub host: String,
    pub port: String,
    pub username: String,
    pub password: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseReadReplica {
    pub id: String,
    pub date_created: String,
    pub plan: String,
    pub region: String,
    pub status: String,
    pub label: String,
    pub host: String,
    pub port: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseUsage {
    pub disk: VultrDatabaseDiskUsage,
    pub memory: VultrDatabaseMemoryUsage,
    pub cpu: VultrDatabaseCpuUsage,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseDiskUsage {
    pub current_gb: f64,
    pub max_gb: f64,
    pub percentage: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseMemoryUsage {
    pub current_mb: f64,
    pub max_mb: f64,
    pub percentage: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseCpuUsage {
    pub percentage: f64,
}

// Database User models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseUser {
    pub username: String,
    pub password: Option<String>,
    pub encryption: Option<String>,
    pub access_cert: Option<VultrDatabaseAccessCert>,
    pub access_key: Option<VultrDatabaseAccessKey>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseUsersResponse {
    pub users: Vec<VultrDatabaseUser>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateDatabaseUserRequest {
    pub username: String,
    pub password: Option<String>,
    pub encryption: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateDatabaseUserRequest {
    pub password: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseAccessCert {
    pub cert: String,
    pub key: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseAccessKey {
    pub access_key: String,
    pub secret_key: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SetDatabaseUserACLRequest {
    pub redis_acl_categories: Option<Vec<String>>,
    pub redis_acl_commands: Option<Vec<String>>,
    pub redis_acl_keys: Option<Vec<String>>,
    pub redis_acl_channels: Option<Vec<String>>,
}

// Database DB models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseDB {
    pub name: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseDBsResponse {
    pub dbs: Vec<VultrDatabaseDB>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateDatabaseDBRequest {
    pub name: String,
}

// Database Topic models (for Kafka)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseTopic {
    pub name: String,
    pub partitions: u32,
    pub replication: u32,
    pub retention_hours: Option<u32>,
    pub retention_bytes: Option<u64>,
    pub compression_type: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseTopicsResponse {
    pub topics: Vec<VultrDatabaseTopic>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateDatabaseTopicRequest {
    pub name: String,
    pub partitions: u32,
    pub replication: u32,
    pub retention_hours: Option<u32>,
    pub retention_bytes: Option<u64>,
    pub compression_type: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateDatabaseTopicRequest {
    pub retention_hours: Option<u32>,
    pub retention_bytes: Option<u64>,
    pub compression_type: Option<String>,
}

// Database Quota models (for Kafka)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseQuota {
    pub client_id: String,
    pub user: String,
    pub producer_byte_rate: Option<u64>,
    pub consumer_byte_rate: Option<u64>,
    pub request_percentage: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseQuotasResponse {
    pub quotas: Vec<VultrDatabaseQuota>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateDatabaseQuotaRequest {
    pub client_id: String,
    pub user: String,
    pub producer_byte_rate: Option<u64>,
    pub consumer_byte_rate: Option<u64>,
    pub request_percentage: Option<f64>,
}

// Database Connector models (for Kafka)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseConnector {
    pub name: String,
    pub connector_class: String,
    pub config: HashMap<String, serde_json::Value>,
    pub tasks: Vec<VultrDatabaseConnectorTask>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseConnectorsResponse {
    pub connectors: Vec<VultrDatabaseConnector>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseConnectorTask {
    pub connector: String,
    pub task: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateDatabaseConnectorRequest {
    pub name: String,
    pub connector_class: String,
    pub config: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateDatabaseConnectorRequest {
    pub config: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseConnectorStatus {
    pub name: String,
    pub connector: VultrDatabaseConnectorState,
    pub tasks: Vec<VultrDatabaseConnectorTaskState>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseConnectorState {
    pub state: String,
    pub worker_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseConnectorTaskState {
    pub id: u32,
    pub state: String,
    pub worker_id: String,
}

// Database Maintenance and Migration models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseMaintenanceUpdate {
    pub description: String,
    pub status: String,
    pub start_time: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseMaintenanceUpdatesResponse {
    pub available_updates: Vec<VultrDatabaseMaintenanceUpdate>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseServiceAlert {
    pub timestamp: String,
    pub message_type: String,
    pub description: String,
    pub service_name: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseServiceAlertsResponse {
    pub alerts: Vec<VultrDatabaseServiceAlert>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseMigrationStatus {
    pub status: String,
    pub method: String,
    pub error: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StartDatabaseMigrationRequest {
    pub host: String,
    pub port: u32,
    pub username: String,
    pub password: String,
    pub database: String,
    pub ignored_dbs: Option<String>,
    pub ssl: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AddDatabaseReadReplicaRequest {
    pub region: String,
    pub label: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseBackupInfo {
    pub latest_backup: VultrDatabaseBackup,
    pub oldest_backup: VultrDatabaseBackup,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseBackup {
    pub date: String,
    pub time: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RestoreDatabaseFromBackupRequest {
    pub backup_label: String,
    pub type_: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ForkDatabaseRequest {
    pub label: String,
    pub region: String,
    pub plan: String,
    pub vpc_id: Option<String>,
}

// Connection Pool models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseConnectionPool {
    pub name: String,
    pub database: String,
    pub username: String,
    pub mode: String,
    pub size: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseConnectionPoolsResponse {
    pub connection_pools: Vec<VultrDatabaseConnectionPool>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateDatabaseConnectionPoolRequest {
    pub name: String,
    pub database: String,
    pub username: String,
    pub mode: String,
    pub size: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateDatabaseConnectionPoolRequest {
    pub database: Option<String>,
    pub username: Option<String>,
    pub mode: Option<String>,
    pub size: Option<u32>,
}

// Advanced Options models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseAdvancedOptions {
    pub autovacuum_analyze_scale_factor: Option<f64>,
    pub autovacuum_analyze_threshold: Option<u32>,
    pub autovacuum_freeze_max_age: Option<u32>,
    pub autovacuum_max_workers: Option<u32>,
    pub autovacuum_naptime: Option<u32>,
    pub autovacuum_vacuum_cost_delay: Option<u32>,
    pub autovacuum_vacuum_cost_limit: Option<u32>,
    pub autovacuum_vacuum_scale_factor: Option<f64>,
    pub autovacuum_vacuum_threshold: Option<u32>,
    pub bgwriter_delay: Option<u32>,
    pub bgwriter_flush_after: Option<u32>,
    pub bgwriter_lru_maxpages: Option<u32>,
    pub bgwriter_lru_multiplier: Option<f64>,
    pub deadlock_timeout: Option<u32>,
    pub default_toast_compression: Option<String>,
    pub idle_in_transaction_session_timeout: Option<u32>,
    pub jit: Option<bool>,
    pub log_autovacuum_min_duration: Option<i32>,
    pub log_error_verbosity: Option<String>,
    pub log_line_prefix: Option<String>,
    pub log_min_duration_statement: Option<i32>,
    pub max_files_per_process: Option<u32>,
    pub max_locks_per_transaction: Option<u32>,
    pub max_logical_replication_workers: Option<u32>,
    pub max_parallel_workers: Option<u32>,
    pub max_parallel_workers_per_gather: Option<u32>,
    pub max_pred_locks_per_transaction: Option<u32>,
    pub max_prepared_transactions: Option<u32>,
    pub max_replication_slots: Option<u32>,
    pub max_stack_depth: Option<u32>,
    pub max_standby_archive_delay: Option<u32>,
    pub max_standby_streaming_delay: Option<u32>,
    pub max_wal_senders: Option<u32>,
    pub max_worker_processes: Option<u32>,
    pub pg_partman_bgw_interval: Option<u32>,
    pub pg_partman_bgw_role: Option<String>,
    pub pg_stat_statements_track: Option<String>,
    pub shared_buffers_percentage: Option<f64>,
    pub temp_file_limit: Option<i32>,
    pub timezone: Option<String>,
    pub track_activity_query_size: Option<u32>,
    pub track_commit_timestamp: Option<String>,
    pub track_functions: Option<String>,
    pub track_io_timing: Option<String>,
    pub wal_sender_timeout: Option<u32>,
    pub wal_writer_delay: Option<u32>,
    pub work_mem: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDatabaseVersions {
    pub available_versions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StartDatabaseVersionUpgradeRequest {
    pub version: String,
}

// Marketplace models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrMarketplaceAppVariable {
    pub name: String,
    pub description: String,
    pub required: bool,
    pub default: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrMarketplaceAppVariablesResponse {
    pub variables: Vec<VultrMarketplaceAppVariable>,
    pub meta: VultrMeta,
}







#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrMetalPlan {
    pub id: String,
    pub cpu_count: u32,
    pub cpu_model: String,
    pub cpu_threads: u32,
    pub ram: u32,
    pub disk: u32,
    pub bandwidth: u32,
    pub monthly_cost: f64,
    #[serde(rename = "type")]
    pub plan_type: String,
    pub locations: Vec<String>,
    pub disk_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrMetalPlansResponse {
    pub plans_metal: Vec<VultrMetalPlan>,
    pub meta: VultrMeta,
}

// Serverless Inference models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInference {
    pub id: String,
    pub date_created: String,
    pub label: String,
    pub api_key: String,
    pub usage: VultrInferenceUsage,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInferencesResponse {
    pub inferences: Vec<VultrInference>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateInferenceRequest {
    pub label: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateInferenceRequest {
    pub label: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInferenceUsage {
    pub current_tokens: u64,
    pub monthly_allotment: u64,
}

// VPC models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVPCDetailed {
    pub id: String,
    pub date_created: String,
    pub region: String,
    pub description: String,
    pub v4_subnet: String,
    pub v4_subnet_mask: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVPCsResponse {
    pub vpcs: Vec<VultrVPCDetailed>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateVPCRequest {
    pub region: String,
    pub description: String,
    pub v4_subnet: String,
    pub v4_subnet_mask: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateVPCRequest {
    pub description: String,
}

// Reserved IP models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrReservedIPDetailed {
    pub id: String,
    pub region: String,
    pub ip_type: String,
    pub subnet: String,
    pub subnet_size: u32,
    pub label: String,
    pub instance_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrReservedIPsResponse {
    pub reserved_ips: Vec<VultrReservedIPDetailed>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateReservedIPRequest {
    pub region: String,
    pub ip_type: String,
    pub label: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateReservedIPRequest {
    pub label: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttachReservedIPRequest {
    pub instance_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConvertReservedIPRequest {
    pub ip_address: String,
    pub label: Option<String>,
}



// Snapshot models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrSnapshot {
    pub id: String,
    pub date_created: String,
    pub description: String,
    pub size: u64,
    pub compressed_size: u64,
    pub status: String,
    pub os_id: u32,
    pub app_id: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrSnapshotsResponse {
    pub snapshots: Vec<VultrSnapshot>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSnapshotRequest {
    pub instance_id: String,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSnapshotFromUrlRequest {
    pub url: String,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateSnapshotRequest {
    pub description: String,
}

// Subaccount models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrSubaccount {
    pub id: String,
    pub name: String,
    pub email: String,
    pub subid: u32,
    pub date_created: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrSubaccountsResponse {
    pub subaccounts: Vec<VultrSubaccount>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSubaccountRequest {
    pub name: String,
    pub email: String,
    pub password: String,
}



#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSSHKeyRequest {
    pub name: Option<String>,
    pub ssh_key: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateSSHKeyRequest {
    pub name: Option<String>,
    pub ssh_key: Option<String>,
}

// Startup Script models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStartupScript {
    pub id: String,
    pub date_created: String,
    pub date_modified: String,
    pub name: String,
    pub script: String,
    #[serde(rename = "type")]
    pub script_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStartupScriptsResponse {
    pub startup_scripts: Vec<VultrStartupScript>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateStartupScriptRequest {
    pub name: String,
    pub script: String,
    #[serde(rename = "type")]
    pub script_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateStartupScriptRequest {
    pub name: Option<String>,
    pub script: Option<String>,
}

// Storage Gateway models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStorageGateway {
    pub id: String,
    pub date_created: String,
    pub status: String,
    #[serde(rename = "type")]
    pub gateway_type: String,
    pub label: String,
    pub pending_charges: f64,
    pub tags: Vec<String>,
    pub health: String,
    pub network_config: VultrStorageGatewayNetworkConfig,
    pub export_config: Option<VultrStorageGatewayExportConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStorageGatewayNetworkConfig {
    pub primary: VultrStorageGatewayPrimaryNetwork,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStorageGatewayPrimaryNetwork {
    pub ipv4_public_enabled: bool,
    pub ipv6_public_enabled: bool,
    pub vpc: Option<VultrStorageGatewayVPC>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStorageGatewayVPC {
    pub vpc_uuid: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStorageGatewayExportConfig {
    pub label: String,
    pub vfs_uuid: String,
    pub pseudo_root_path: String,
    pub allowed_ips: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStorageGatewaysResponse {
    pub storage_gateways: Vec<VultrStorageGateway>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateStorageGatewayRequest {
    pub label: String,
    pub region: String,
    #[serde(rename = "type")]
    pub gateway_type: String,
    pub tags: Option<Vec<String>>,
    pub network_config: VultrStorageGatewayNetworkConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateStorageGatewayRequest {
    pub label: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateStorageGatewayExportRequest {
    pub label: String,
    pub vfs_uuid: String,
    pub pseudo_root_path: String,
    pub allowed_ips: Vec<String>,
}

// User models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrUser {
    pub id: String,
    pub name: String,
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub api_enabled: bool,
    pub acls: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrUsersResponse {
    pub users: Vec<VultrUser>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateUserRequest {
    pub email: String,
    pub first_name: String,
    pub last_name: String,
    pub password: String,
    pub api_enabled: Option<bool>,
    pub acls: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateUserRequest {
    pub email: Option<String>,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub password: Option<String>,
    pub api_enabled: Option<bool>,
    pub acls: Option<Vec<String>>,
}

// VFS (Vultr File System) models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVFSRegion {
    pub id: String,
    pub country: String,
    pub continent: String,
    pub description: String,
    pub price_per_gb: VultrVFSPricing,
    pub min_size_gb: VultrVFSMinSize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVFSPricing {
    pub nvme: f64,
    pub hdd: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVFSMinSize {
    pub nvme: u32,
    pub hdd: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVFSRegionsResponse {
    pub regions: Vec<VultrVFSRegion>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVFS {
    pub id: String,
    pub region: String,
    pub date_created: String,
    pub status: String,
    pub label: String,
    pub tags: Vec<String>,
    pub disk_type: String,
    pub storage_size: VultrVFSStorageSize,
    pub storage_used: VultrVFSStorageSize,
    pub billing: VultrVFSBilling,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVFSStorageSize {
    pub bytes: u64,
    pub gb: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVFSBilling {
    pub charges: f64,
    pub monthly: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVFSResponse {
    pub vfs: Vec<VultrVFS>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateVFSRequest {
    pub region: String,
    pub label: String,
    pub storage_size: VultrVFSStorageSize,
    pub disk_type: Option<String>,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateVFSRequest {
    pub label: Option<String>,
    pub storage_size: Option<VultrVFSStorageSize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVFSAttachment {
    pub state: String,
    pub vfs_id: String,
    pub target_id: String,
    pub mount_tag: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVFSAttachmentsResponse {
    pub attachments: Vec<VultrVFSAttachment>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVPC2 {
    pub id: String,
    pub date_created: String,
    pub region: String,
    pub description: String,
    pub ip_block: String,
    pub prefix_length: u32,
}

// Reserved IP models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrReservedIP {
    pub id: String,
    pub region: String,
    pub ip_type: String,
    pub subnet: String,
    pub subnet_size: u32,
    pub label: String,
    pub instance_id: Option<String>,
}



// Sub-Account models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrSubAccount {
    pub id: String,
    pub name: String,
    pub email: String,
    pub activated: bool,
    pub balance: f64,
    pub pending_charges: f64,
}





// Application models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrApplication {
    pub id: u32,
    pub name: String,
    pub short_name: String,
    pub deploy_name: String,
    pub surcharge: f64,
    pub vendor: String,
    pub image_id: String,
}

// Billing models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBillingHistory {
    pub id: u32,
    pub date: String,
    pub type_: String,
    pub description: String,
    pub amount: f64,
    pub balance: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInvoice {
    pub id: u32,
    pub date: String,
    pub description: String,
    pub amount: f64,
    pub balance: f64,
}

// Marketplace models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrMarketplaceApp {
    pub id: u32,
    pub name: String,
    pub short_name: String,
    pub deploy_name: String,
    pub surcharge: f64,
    pub vendor: String,
    pub image_id: String,
    pub type_: String,
}





#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrErrorResponse {
    pub error: VultrError,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalIpv4Response {
    pub ipv4s: Vec<BareMetalIpv4Info>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalIpv6Response {
    pub ipv6s: Vec<BareMetalIpv6Info>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalBandwidthResponse {
    pub bandwidth: BareMetalBandwidth,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalUserDataResponse {
    pub user_data: BareMetalUserData,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalUpgradesResponse {
    pub upgrades: BareMetalUpgrades,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalVncResponse {
    pub vnc: BareMetalVncInfo,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalVpcsResponse {
    pub vpcs: Vec<BareMetalVpcInfo>,
    pub meta: VultrMeta,
}

// Bare Metal Operation Request models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalActionRequest {
    pub baremetal_ids: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttachBareMetalVPCRequest {
    pub vpc_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetachBareMetalVPCRequest {
    pub vpc_id: String,
}

// Orchestration models (placeholder for future implementation)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrOrchestrationTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub template: String,
    pub date_created: String,
    pub date_modified: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrOrchestrationTemplatesResponse {
    pub templates: Vec<VultrOrchestrationTemplate>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateOrchestrationTemplateRequest {
    pub name: String,
    pub description: String,
    pub template: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateOrchestrationTemplateRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub template: Option<String>,
}
