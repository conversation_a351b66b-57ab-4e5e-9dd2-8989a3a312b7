use crate::{
    config::Config,
    database::Database,
    infrastructure::{
        CircuitBreakerService, CircuitBreakerAware, DeploymentStateManager, DeploymentState,
        DeploymentEvent, StateMachineManager, ChunkProcessor, ChunkProcessorConfig,
        MetricsService, RateLimiterService
    },
    models::{
        Application, Deployment, DeploymentStatus, DeploymentTrigger, ApplicationStatus,
        CreateApplicationRequest, ApplicationResponse, DeploymentResponse, TriggerDeploymentRequest,
        Environment, EnvironmentResponse, RuntimeConfig, ServiceType, AutoScalingConfig, Pagination
    },
    services::{ServiceError, ServiceResult, BuildService, GitService},
    vultr::VultrClient,
    with_circuit_breaker,
};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use futures::TryStreamExt;
use mongodb::Collection;
use std::collections::HashMap;
use tracing::{error, info, instrument, warn};
use uuid::Uuid;

pub struct DeploymentService<'a> {
    applications: Collection<Application>,
    deployments: Collection<Deployment>,
    vultr_client: &'a VultrClient,
    build_service: BuildService<'a>,
    git_service: GitService<'a>,
    config: &'a Config,
    circuit_breaker: CircuitBreakerService,
    metrics: MetricsService,
    rate_limiter: RateLimiterService,
    chunk_processor: ChunkProcessor,
}

impl<'a> DeploymentService<'a> {
    pub fn new(
        database: &Database,
        vultr_client: &'a VultrClient,
        config: &'a Config,
    ) -> Self {
        let build_service = BuildService::new(database, vultr_client, config);
        let git_service = GitService::new(database, config);
        let circuit_breaker = CircuitBreakerService::new();
        let metrics = MetricsService::new();
        let rate_limiter = RateLimiterService::new();
        let chunk_processor = ChunkProcessor::new(ChunkProcessorConfig {
            chunk_size: 50,
            delay_between_chunks: std::time::Duration::from_millis(100),
            max_retries: 3,
            retry_delay: std::time::Duration::from_secs(1),
            max_concurrent_chunks: 3,
        });

        Self {
            applications: database.collection("applications"),
            deployments: database.collection("deployments"),
            vultr_client,
            build_service,
            git_service,
            config,
            circuit_breaker,
            metrics,
            rate_limiter,
            chunk_processor,
        }
    }

    #[instrument(skip(self, user_id, request))]
    pub async fn create_application(
        &self,
        user_id: &str,
        request: CreateApplicationRequest,
    ) -> ServiceResult<ApplicationResponse> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Validate repository access
        let repo_info = self.git_service.validate_repository(&request.repository).await?;
        info!("Repository validated: {}", repo_info.full_name);

        // Generate webhook secret
        let webhook_secret = Uuid::new_v4().to_string();

        let application = Application {
            id: None,
            user_id: user_object_id,
            name: request.name.clone(),
            description: request.description.clone(),
            repository: crate::models::Repository {
                provider: request.repository.provider,
                url: request.repository.url,
                branch: request.repository.branch,
                auto_deploy: request.repository.auto_deploy,
                webhook_secret: Some(webhook_secret.clone()),
                access_token: request.repository.access_token,
            },
            environment: crate::models::Environment {
                name: request.environment.name,
                variables: request.environment.variables,
                secrets: request.environment.secrets,
                build_command: request.environment.build_command,
                start_command: request.environment.start_command,
                dockerfile_path: request.environment.dockerfile_path,
                root_directory: request.environment.root_directory,
            },
            runtime_config: crate::models::RuntimeConfig {
                service_type: request.runtime_config.service_type,
                instance_type: request.runtime_config.instance_type,
                region: request.runtime_config.region,
                auto_scaling: request.runtime_config.auto_scaling,
                health_check: request.runtime_config.health_check,
                networking: request.runtime_config.networking,
            },
            status: ApplicationStatus::Creating,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            last_deployed_at: None,
        };

        let result = self.applications
            .insert_one(&application, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        let app_id = result.inserted_id.as_object_id()
            .ok_or_else(|| ServiceError::Internal("Failed to get inserted application ID".to_string()))?;

        // Setup webhook if auto-deploy is enabled
        if application.repository.auto_deploy {
            match self.git_service.setup_webhook(&app_id, &application.repository, &webhook_secret).await {
                Ok(webhook_id) => {
                    info!("Webhook setup successful for application {}: {}", app_id, webhook_id);
                }
                Err(e) => {
                    warn!("Failed to setup webhook for application {}: {}", app_id, e);
                    // Continue without webhook - user can set it up manually
                }
            }
        }

        // Create initial infrastructure
        self.provision_infrastructure(&app_id, &application.runtime_config).await?;

        // Update application status
        self.applications
            .update_one(
                doc! { "_id": app_id },
                doc! { "$set": { "status": "Running" } },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Application created successfully: {}", app_id);

        Ok(ApplicationResponse {
            id: app_id.to_hex(),
            name: application.name,
            description: application.description,
            repository: application.repository,
            environment: EnvironmentResponse {
                name: application.environment.name,
                variables: application.environment.variables.keys().map(|k| (k.clone(), "***".to_string())).collect(),
                secrets: application.environment.secrets.keys().cloned().collect(),
                build_command: application.environment.build_command,
                start_command: application.environment.start_command,
                dockerfile_path: application.environment.dockerfile_path,
                root_directory: application.environment.root_directory,
            },
            runtime_config: application.runtime_config,
            status: ApplicationStatus::Running,
            url: self.generate_application_url(&app_id, &application.runtime_config).await,
            created_at: application.created_at,
            updated_at: application.updated_at,
            last_deployed_at: None,
        })
    }

    #[instrument(skip(self, user_id, app_id))]
    pub async fn get_application(&self, user_id: &str, app_id: &str) -> ServiceResult<ApplicationResponse> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        
        let app_object_id = ObjectId::parse_str(app_id)
            .map_err(|_| ServiceError::Validation("Invalid application ID".to_string()))?;

        let application = self.applications
            .find_one(doc! { "_id": app_object_id, "user_id": user_object_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .ok_or_else(|| ServiceError::NotFound("Application not found".to_string()))?;

        Ok(ApplicationResponse {
            id: application.id.unwrap().to_hex(),
            name: application.name,
            description: application.description,
            repository: application.repository,
            environment: EnvironmentResponse {
                name: application.environment.name,
                variables: application.environment.variables.keys().map(|k| (k.clone(), "***".to_string())).collect(),
                secrets: application.environment.secrets.keys().cloned().collect(),
                build_command: application.environment.build_command,
                start_command: application.environment.start_command,
                dockerfile_path: application.environment.dockerfile_path,
                root_directory: application.environment.root_directory,
            },
            runtime_config: application.runtime_config.clone(),
            status: application.status,
            url: self.generate_application_url(&application.id.unwrap(), &application.runtime_config).await,
            created_at: application.created_at,
            updated_at: application.updated_at,
            last_deployed_at: application.last_deployed_at,
        })
    }

    #[instrument(skip(self, user_id, app_id, request))]
    pub async fn trigger_deployment(
        &self,
        user_id: &str,
        app_id: &str,
        request: TriggerDeploymentRequest,
    ) -> ServiceResult<DeploymentResponse> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        
        let app_object_id = ObjectId::parse_str(app_id)
            .map_err(|_| ServiceError::Validation("Invalid application ID".to_string()))?;

        let application = self.applications
            .find_one(doc! { "_id": app_object_id, "user_id": user_object_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .ok_or_else(|| ServiceError::NotFound("Application not found".to_string()))?;

        // Create deployment record
        let deployment = Deployment {
            id: None,
            application_id: app_object_id,
            version: format!("v{}", Utc::now().timestamp()),
            commit_sha: request.commit_sha.unwrap_or_else(|| "manual".to_string()),
            commit_message: None,
            branch: request.branch.unwrap_or_else(|| application.repository.branch.clone()),
            status: DeploymentStatus::Queued,
            trigger: DeploymentTrigger::Manual { user_id: user_object_id },
            build_logs: Vec::new(),
            runtime_logs: Vec::new(),
            metrics: crate::models::DeploymentMetrics {
                build_duration: None,
                deploy_duration: None,
                instances_count: 1,
                cpu_usage: 0.0,
                memory_usage: 0.0,
                request_count: 0,
                error_rate: 0.0,
                response_time_p95: 0.0,
            },
            created_at: Utc::now(),
            started_at: None,
            completed_at: None,
            failed_at: None,
        };

        let result = self.deployments
            .insert_one(&deployment, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        let deployment_id = result.inserted_id.as_object_id()
            .ok_or_else(|| ServiceError::Internal("Failed to get inserted deployment ID".to_string()))?;

        // Start deployment process
        self.execute_deployment(deployment_id, &application).await?;

        Ok(DeploymentResponse {
            id: deployment_id.to_hex(),
            application_id: app_id.to_string(),
            version: deployment.version,
            commit_sha: deployment.commit_sha,
            commit_message: deployment.commit_message,
            branch: deployment.branch,
            status: deployment.status,
            trigger: deployment.trigger,
            metrics: deployment.metrics,
            created_at: deployment.created_at,
            started_at: deployment.started_at,
            completed_at: deployment.completed_at,
            failed_at: deployment.failed_at,
        })
    }

    #[instrument(skip(self, deployment_id, application))]
    async fn execute_deployment(&self, deployment_id: ObjectId, application: &Application) -> ServiceResult<()> {
        let start_time = std::time::Instant::now();

        // Initialize deployment state machine
        let mut state_manager = DeploymentStateManager::new();

        // Transition to queued state
        state_manager.transition(DeploymentEvent::Queue)
            .map_err(|e| ServiceError::Internal(format!("State transition failed: {}", e)))?;

        // Rate limit deployments
        let user_id = application.user_id.to_hex();
        self.rate_limiter.check_deployment_rate_limit(&user_id).await?;

        // Update deployment status with state machine
        let update_result = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.deployments
                    .update_one(
                        doc! { "_id": deployment_id },
                        doc! {
                            "$set": {
                                "status": "Building",
                                "started_at": Utc::now()
                            }
                        },
                        None,
                    )
                    .await
                    .map_err(|e| ServiceError::Database(e))
            }
        )?;

        // Transition to building state
        state_manager.transition(DeploymentEvent::StartBuild)
            .map_err(|e| ServiceError::Internal(format!("State transition failed: {}", e)))?;

        // Create build job with proper error handling
        let build_request = crate::models::CreateBuildJobRequest {
            application_id: application.id.unwrap().to_hex(),
            deployment_id: deployment_id.to_hex(),
            build_config: crate::models::BuildConfig {
                runtime: self.detect_runtime(application).await?,
                build_command: application.environment.build_command.clone(),
                install_command: self.detect_install_command(application).await,
                dockerfile_path: application.environment.dockerfile_path.clone(),
                root_directory: application.environment.root_directory.clone().unwrap_or_else(|| ".".to_string()),
                environment_variables: application.environment.variables.clone(),
                cache_enabled: true,
                timeout: self.calculate_build_timeout(application).await,
            },
            source: crate::models::BuildSource {
                repository_url: application.repository.url.clone(),
                branch: application.repository.branch.clone(),
                commit_sha: self.get_latest_commit_sha(application).await.unwrap_or_else(|| "HEAD".to_string()),
                commit_message: "Deployment build".to_string(),
                author: "System".to_string(),
                tarball_url: None,
            },
            priority: self.determine_build_priority(application).await,
        };

        let build_job = with_circuit_breaker!(
            self.circuit_breaker,
            "build_system",
            {
                self.build_service.create_build_job(build_request).await
            }
        )?;

        info!("Build job created for deployment {}: {}", deployment_id, build_job.id);

        // Record metrics
        let duration = start_time.elapsed();
        self.metrics.increment_deployments_started();
        self.metrics.record_database_operation("execute_deployment", duration, true);

        // Start monitoring the deployment
        self.monitor_deployment_progress(deployment_id, build_job.id, state_manager).await?;

        Ok(())
    }

    async fn monitor_deployment_progress(
        &self,
        deployment_id: ObjectId,
        build_job_id: String,
        mut state_manager: DeploymentStateManager,
    ) -> ServiceResult<()> {
        // This would typically be handled by a background worker
        // For now, we'll just set up the monitoring structure

        tokio::spawn(async move {
            // Monitor build job status and update deployment accordingly
            // This is a simplified version - production would have more sophisticated monitoring

            loop {
                tokio::time::sleep(std::time::Duration::from_secs(30)).await;

                // Check build job status
                // Update deployment state based on build progress
                // Handle state transitions

                // Break when deployment is complete or failed
                break;
            }
        });

        Ok(())
    }

    async fn detect_runtime(&self, application: &Application) -> ServiceResult<crate::models::BuildRuntime> {
        // Detect runtime based on repository contents and configuration
        if let Some(dockerfile_path) = &application.environment.dockerfile_path {
            return Ok(crate::models::BuildRuntime::Docker {
                dockerfile: dockerfile_path.clone()
            });
        }

        // Default detection logic based on common patterns
        // In production, this would analyze the repository contents
        match application.environment.start_command.as_str() {
            cmd if cmd.contains("node") || cmd.contains("npm") => {
                Ok(crate::models::BuildRuntime::Node { version: "18".to_string() })
            }
            cmd if cmd.contains("python") || cmd.contains("gunicorn") => {
                Ok(crate::models::BuildRuntime::Python { version: "3.11".to_string() })
            }
            cmd if cmd.contains("cargo") => {
                Ok(crate::models::BuildRuntime::Rust { version: "1.70".to_string() })
            }
            cmd if cmd.contains("go") => {
                Ok(crate::models::BuildRuntime::Go { version: "1.21".to_string() })
            }
            _ => {
                // Default to static if no specific runtime detected
                Ok(crate::models::BuildRuntime::Static)
            }
        }
    }

    async fn detect_install_command(&self, application: &Application) -> Option<String> {
        // Detect appropriate install command based on runtime
        match self.detect_runtime(application).await {
            Ok(crate::models::BuildRuntime::Node { .. }) => Some("npm ci".to_string()),
            Ok(crate::models::BuildRuntime::Python { .. }) => Some("pip install -r requirements.txt".to_string()),
            Ok(crate::models::BuildRuntime::Rust { .. }) => Some("cargo fetch".to_string()),
            Ok(crate::models::BuildRuntime::Go { .. }) => Some("go mod download".to_string()),
            _ => None,
        }
    }

    async fn calculate_build_timeout(&self, application: &Application) -> u32 {
        // Calculate timeout based on application complexity and historical data
        let base_timeout = match self.detect_runtime(application).await {
            Ok(crate::models::BuildRuntime::Node { .. }) => 600,      // 10 minutes
            Ok(crate::models::BuildRuntime::Python { .. }) => 480,    // 8 minutes
            Ok(crate::models::BuildRuntime::Rust { .. }) => 1800,     // 30 minutes
            Ok(crate::models::BuildRuntime::Go { .. }) => 900,        // 15 minutes
            Ok(crate::models::BuildRuntime::Docker { .. }) => 1200,   // 20 minutes
            _ => 300,                                                  // 5 minutes default
        };

        // Add buffer for first-time builds or complex projects
        base_timeout + 300 // 5 minute buffer
    }

    async fn determine_build_priority(&self, application: &Application) -> crate::models::BuildPriority {
        // Determine priority based on application tier, user plan, etc.
        // For now, use normal priority for all builds
        crate::models::BuildPriority::Normal
    }

    async fn get_latest_commit_sha(&self, application: &Application) -> Option<String> {
        // Fetch the latest commit SHA from the git provider
        match self.fetch_latest_commit_from_provider(application).await {
            Ok(commit_sha) => Some(commit_sha),
            Err(e) => {
                warn!("Failed to fetch latest commit SHA for {}: {}", application.repository.url, e);
                None
            }
        }
    }

    async fn fetch_latest_commit_from_provider(&self, application: &Application) -> ServiceResult<String> {
        let repo_url = &application.repository.url;
        let branch = &application.repository.branch;

        // Parse repository URL to determine provider
        if repo_url.contains("github.com") {
            self.fetch_github_latest_commit(repo_url, branch).await
        } else if repo_url.contains("gitlab.com") {
            self.fetch_gitlab_latest_commit(repo_url, branch).await
        } else if repo_url.contains("bitbucket.org") {
            self.fetch_bitbucket_latest_commit(repo_url, branch).await
        } else {
            Err(ServiceError::ExternalApi("Unsupported git provider".to_string()))
        }
    }

    async fn fetch_github_latest_commit(&self, repo_url: &str, branch: &str) -> ServiceResult<String> {
        // Extract owner/repo from URL
        let (owner, repo) = self.parse_github_url(repo_url)?;

        let url = format!(
            "https://api.github.com/repos/{}/{}/commits/{}",
            owner, repo, branch
        );

        let client = reqwest::Client::new();
        let response = with_circuit_breaker!(
            self.circuit_breaker,
            "git_api",
            {
                client
                    .get(&url)
                    .header("User-Agent", "Achidas-Platform/1.0")
                    .header("Accept", "application/vnd.github.v3+json")
                    .send()
                    .await
                    .map_err(|e| ServiceError::ExternalApi(format!("GitHub API error: {}", e)))
            }
        )?;

        if !response.status().is_success() {
            return Err(ServiceError::ExternalApi(format!(
                "GitHub API returned status: {}",
                response.status()
            )));
        }

        let commit_data: serde_json::Value = response
            .json()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to parse GitHub response: {}", e)))?;

        let commit_sha = commit_data["sha"]
            .as_str()
            .ok_or_else(|| ServiceError::ExternalApi("No SHA in GitHub response".to_string()))?;

        Ok(commit_sha.to_string())
    }

    async fn fetch_gitlab_latest_commit(&self, repo_url: &str, branch: &str) -> ServiceResult<String> {
        // Extract owner/repo from URL
        let (owner, repo) = self.parse_gitlab_url(repo_url)?;

        let url = format!(
            "https://gitlab.com/api/v4/projects/{}%2F{}/repository/commits/{}",
            owner, repo, branch
        );

        let client = reqwest::Client::new();
        let response = with_circuit_breaker!(
            self.circuit_breaker,
            "git_api",
            {
                client
                    .get(&url)
                    .header("User-Agent", "Achidas-Platform/1.0")
                    .send()
                    .await
                    .map_err(|e| ServiceError::ExternalApi(format!("GitLab API error: {}", e)))
            }
        )?;

        if !response.status().is_success() {
            return Err(ServiceError::ExternalApi(format!(
                "GitLab API returned status: {}",
                response.status()
            )));
        }

        let commit_data: serde_json::Value = response
            .json()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to parse GitLab response: {}", e)))?;

        let commit_sha = commit_data["id"]
            .as_str()
            .ok_or_else(|| ServiceError::ExternalApi("No ID in GitLab response".to_string()))?;

        Ok(commit_sha.to_string())
    }

    async fn fetch_bitbucket_latest_commit(&self, repo_url: &str, branch: &str) -> ServiceResult<String> {
        // Extract owner/repo from URL
        let (owner, repo) = self.parse_bitbucket_url(repo_url)?;

        let url = format!(
            "https://api.bitbucket.org/2.0/repositories/{}/{}/commits/{}",
            owner, repo, branch
        );

        let client = reqwest::Client::new();
        let response = with_circuit_breaker!(
            self.circuit_breaker,
            "git_api",
            {
                client
                    .get(&url)
                    .header("User-Agent", "Achidas-Platform/1.0")
                    .send()
                    .await
                    .map_err(|e| ServiceError::ExternalApi(format!("Bitbucket API error: {}", e)))
            }
        )?;

        if !response.status().is_success() {
            return Err(ServiceError::ExternalApi(format!(
                "Bitbucket API returned status: {}",
                response.status()
            )));
        }

        let response_data: serde_json::Value = response
            .json()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to parse Bitbucket response: {}", e)))?;

        // Bitbucket returns an array of commits, get the first one
        let commits = response_data["values"]
            .as_array()
            .ok_or_else(|| ServiceError::ExternalApi("No commits in Bitbucket response".to_string()))?;

        if commits.is_empty() {
            return Err(ServiceError::ExternalApi("No commits found in branch".to_string()));
        }

        let commit_sha = commits[0]["hash"]
            .as_str()
            .ok_or_else(|| ServiceError::ExternalApi("No hash in Bitbucket commit".to_string()))?;

        Ok(commit_sha.to_string())
    }

    fn parse_github_url(&self, url: &str) -> ServiceResult<(String, String)> {
        let regex = regex::Regex::new(r"https://github\.com/([^/]+)/([^/]+)")
            .map_err(|e| ServiceError::Internal(format!("Regex error: {}", e)))?;

        let captures = regex.captures(url)
            .ok_or_else(|| ServiceError::Validation("Invalid GitHub URL format".to_string()))?;

        let owner = captures.get(1).unwrap().as_str().to_string();
        let repo = captures.get(2).unwrap().as_str().trim_end_matches(".git").to_string();

        Ok((owner, repo))
    }

    fn parse_gitlab_url(&self, url: &str) -> ServiceResult<(String, String)> {
        let regex = regex::Regex::new(r"https://gitlab\.com/([^/]+)/([^/]+)")
            .map_err(|e| ServiceError::Internal(format!("Regex error: {}", e)))?;

        let captures = regex.captures(url)
            .ok_or_else(|| ServiceError::Validation("Invalid GitLab URL format".to_string()))?;

        let owner = captures.get(1).unwrap().as_str().to_string();
        let repo = captures.get(2).unwrap().as_str().trim_end_matches(".git").to_string();

        Ok((owner, repo))
    }

    fn parse_bitbucket_url(&self, url: &str) -> ServiceResult<(String, String)> {
        let regex = regex::Regex::new(r"https://bitbucket\.org/([^/]+)/([^/]+)")
            .map_err(|e| ServiceError::Internal(format!("Regex error: {}", e)))?;

        let captures = regex.captures(url)
            .ok_or_else(|| ServiceError::Validation("Invalid Bitbucket URL format".to_string()))?;

        let owner = captures.get(1).unwrap().as_str().to_string();
        let repo = captures.get(2).unwrap().as_str().trim_end_matches(".git").to_string();

        Ok((owner, repo))
    }

    #[instrument(skip(self, app_id, runtime_config))]
    async fn provision_infrastructure(&self, app_id: &ObjectId, runtime_config: &RuntimeConfig) -> ServiceResult<()> {
        match runtime_config.service_type {
            ServiceType::WebService => {
                // Create Vultr instance for web service
                self.create_web_service_instance(app_id, runtime_config).await?;
            }
            ServiceType::BackgroundWorker => {
                // Create instance for background worker
                self.create_worker_instance(app_id, runtime_config).await?;
            }
            ServiceType::CronJob { .. } => {
                // Setup cron job infrastructure
                self.create_cron_job_instance(app_id, runtime_config).await?;
            }
            ServiceType::StaticSite => {
                // Setup static site hosting
                self.create_static_site_hosting(app_id, runtime_config).await?;
            }
        }

        Ok(())
    }

    async fn create_web_service_instance(&self, _app_id: &ObjectId, _runtime_config: &RuntimeConfig) -> ServiceResult<()> {
        // Implementation for creating web service infrastructure
        // This would create Vultr instances, load balancers, etc.
        Ok(())
    }

    async fn create_worker_instance(&self, _app_id: &ObjectId, _runtime_config: &RuntimeConfig) -> ServiceResult<()> {
        // Implementation for creating background worker infrastructure
        Ok(())
    }

    async fn create_cron_job_instance(&self, _app_id: &ObjectId, _runtime_config: &RuntimeConfig) -> ServiceResult<()> {
        // Implementation for creating cron job infrastructure
        Ok(())
    }

    async fn create_static_site_hosting(&self, _app_id: &ObjectId, _runtime_config: &RuntimeConfig) -> ServiceResult<()> {
        // Implementation for creating static site hosting
        Ok(())
    }

    async fn generate_application_url(&self, _app_id: &ObjectId, runtime_config: &RuntimeConfig) -> Option<String> {
        // Generate application URL based on configuration
        match runtime_config.service_type {
            ServiceType::WebService | ServiceType::StaticSite => {
                Some(format!("https://app-{}.{}", _app_id.to_hex(), "your-domain.com"))
            }
            _ => None,
        }
    }

    async fn detect_runtime(&self, _application: &Application) -> ServiceResult<crate::models::BuildRuntime> {
        // Detect runtime based on repository contents
        // This would analyze package.json, requirements.txt, etc.
        Ok(crate::models::BuildRuntime::Node { version: "18".to_string() })
    }

    /// List applications with pagination and filtering
    pub async fn list_applications(
        &self,
        user_id: &str,
        pagination: Pagination,
    ) -> ServiceResult<Vec<ApplicationResponse>> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let skip = (pagination.page.unwrap_or(1) - 1) * pagination.limit.unwrap_or(20);
        let limit = pagination.limit.unwrap_or(20).min(100) as i64; // Cap at 100

        let filter = doc! { "user_id": user_object_id };

        let applications = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                let mut cursor = self.applications
                    .find(filter, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?
                    .skip(skip as u64)
                    .limit(limit);

                let mut apps = Vec::new();
                while let Some(app) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
                    apps.push(app);
                }
                Ok(apps)
            }
        )?;

        // Convert to response format in chunks to avoid memory issues
        let app_responses = self.chunk_processor.process_chunks(
            applications,
            |chunk| {
                let responses: Vec<ApplicationResponse> = chunk
                    .into_iter()
                    .map(|app| self.convert_to_application_response(app))
                    .collect::<Result<Vec<_>, _>>()?;
                Ok(responses)
            }
        ).await?;

        Ok(app_responses.into_iter().flatten().collect())
    }

    /// Update application with idempotent operations
    pub async fn update_application(
        &self,
        user_id: &str,
        app_id: &str,
        request: CreateApplicationRequest,
    ) -> ServiceResult<ApplicationResponse> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let app_object_id = ObjectId::parse_str(app_id)
            .map_err(|_| ServiceError::Validation("Invalid application ID".to_string()))?;

        // Idempotent update - check if changes are actually needed
        let existing_app = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.applications
                    .find_one(doc! { "_id": app_object_id, "user_id": user_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?
                    .ok_or_else(|| ServiceError::NotFound("Application not found".to_string()))
            }
        )?;

        // Check if update is needed (idempotent)
        if self.is_application_update_needed(&existing_app, &request) {
            let update_doc = doc! {
                "$set": {
                    "name": &request.name,
                    "description": &request.description,
                    "updated_at": Utc::now(),
                }
            };

            with_circuit_breaker!(
                self.circuit_breaker,
                "database",
                {
                    self.applications
                        .update_one(
                            doc! { "_id": app_object_id, "user_id": user_object_id },
                            update_doc,
                            None,
                        )
                        .await
                        .map_err(|e| ServiceError::Database(e))?;
                    Ok(())
                }
            )?;

            info!("Application {} updated successfully", app_id);
        } else {
            info!("Application {} update skipped - no changes needed", app_id);
        }

        // Return updated application
        self.get_application(user_id, app_id).await
    }

    /// Delete application with proper cleanup
    pub async fn delete_application(&self, user_id: &str, app_id: &str) -> ServiceResult<()> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let app_object_id = ObjectId::parse_str(app_id)
            .map_err(|_| ServiceError::Validation("Invalid application ID".to_string()))?;

        // Get application with state machine
        let application = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.applications
                    .find_one(doc! { "_id": app_object_id, "user_id": user_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?
                    .ok_or_else(|| ServiceError::NotFound("Application not found".to_string()))
            }
        )?;

        // Use state machine for safe deletion
        let mut state_manager = DeploymentStateManager::from_state(
            match application.status {
                ApplicationStatus::Running => DeploymentState::Running,
                ApplicationStatus::Failed => DeploymentState::Failed,
                ApplicationStatus::Stopped => DeploymentState::Cancelled,
                _ => DeploymentState::Created,
            }
        );

        // Transition to cancelled state before deletion
        if state_manager.can_transition(&DeploymentEvent::Cancel) {
            state_manager.transition(DeploymentEvent::Cancel)
                .map_err(|e| ServiceError::Internal(format!("State transition failed: {}", e)))?;
        }

        // Cleanup infrastructure first
        self.cleanup_application_infrastructure(&app_object_id).await?;

        // Delete application record
        with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.applications
                    .delete_one(doc! { "_id": app_object_id, "user_id": user_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?;
                Ok(())
            }
        )?;

        info!("Application {} deleted successfully", app_id);
        Ok(())
    }

    fn convert_to_application_response(&self, app: Application) -> ServiceResult<ApplicationResponse> {
        Ok(ApplicationResponse {
            id: app.id.unwrap().to_hex(),
            name: app.name,
            description: app.description,
            repository: app.repository,
            environment: EnvironmentResponse {
                name: app.environment.name,
                variables: app.environment.variables.keys().map(|k| (k.clone(), "***".to_string())).collect(),
                secrets: app.environment.secrets.keys().cloned().collect(),
                build_command: app.environment.build_command,
                start_command: app.environment.start_command,
                dockerfile_path: app.environment.dockerfile_path,
                root_directory: app.environment.root_directory,
            },
            runtime_config: app.runtime_config.clone(),
            status: app.status,
            url: self.generate_application_url(&app.id.unwrap(), &app.runtime_config).await,
            created_at: app.created_at,
            updated_at: app.updated_at,
            last_deployed_at: app.last_deployed_at,
        })
    }

    fn is_application_update_needed(&self, existing: &Application, request: &CreateApplicationRequest) -> bool {
        existing.name != request.name ||
        existing.description != request.description
        // Add more fields as needed
    }

    async fn cleanup_application_infrastructure(&self, app_id: &ObjectId) -> ServiceResult<()> {
        // Cleanup infrastructure resources
        // This would include stopping instances, cleaning up load balancers, etc.
        info!("Cleaning up infrastructure for application: {}", app_id);

        // Use circuit breaker for external API calls
        with_circuit_breaker!(
            self.circuit_breaker,
            "vultr_api",
            {
                // Cleanup logic here
                Ok(())
            }
        )
    }
}

impl<'a> CircuitBreakerAware for DeploymentService<'a> {
    fn circuit_breaker(&self) -> &CircuitBreakerService {
        &self.circuit_breaker
    }
}
