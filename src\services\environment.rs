use crate::{
    config::Config,
    database::Database,
    models::{Application, Environment},
    services::{ServiceError, ServiceResult},
};
use aes_gcm::{
    aead::{Aead, KeyInit, OsRng},
    Aes256Gcm, Nonce,
};
use base64::{engine::general_purpose, Engine as _};
use bson::{doc, oid::ObjectId};
use mongodb::Collection;
use rand::RngCore;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{error, info, instrument};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentVariable {
    pub key: String,
    pub value: String,
    pub encrypted: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Secret {
    pub key: String,
    pub value: String, // Always encrypted
    pub description: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub last_accessed: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentGroup {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub name: String,
    pub description: Option<String>,
    pub variables: HashMap<String, EnvironmentVariable>,
    pub secrets: HashMap<String, Secret>,
    pub linked_applications: Vec<ObjectId>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

pub struct EnvironmentService<'a> {
    applications: Collection<Application>,
    environment_groups: Collection<EnvironmentGroup>,
    encryption_key: Vec<u8>,
    config: &'a Config,
}

impl<'a> EnvironmentService<'a> {
    pub fn new(database: &Database, config: &'a Config) -> Self {
        // In production, this should come from a secure key management service
        let encryption_key = Self::derive_encryption_key(&config.jwt_secret);

        Self {
            applications: database.collection("applications"),
            environment_groups: database.collection("environment_groups"),
            encryption_key,
            config,
        }
    }

    fn derive_encryption_key(secret: &str) -> Vec<u8> {
        use sha2::{Digest, Sha256};
        let mut hasher = Sha256::new();
        hasher.update(secret.as_bytes());
        hasher.update(b"environment_encryption");
        hasher.finalize().to_vec()
    }

    #[instrument(skip(self, value))]
    pub fn encrypt_value(&self, value: &str) -> ServiceResult<String> {
        let cipher = Aes256Gcm::new_from_slice(&self.encryption_key)
            .map_err(|e| ServiceError::Internal(format!("Encryption setup failed: {}", e)))?;

        let mut nonce_bytes = [0u8; 12];
        OsRng.fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        let ciphertext = cipher
            .encrypt(nonce, value.as_bytes())
            .map_err(|e| ServiceError::Internal(format!("Encryption failed: {}", e)))?;

        // Combine nonce and ciphertext
        let mut encrypted_data = nonce_bytes.to_vec();
        encrypted_data.extend_from_slice(&ciphertext);

        Ok(general_purpose::STANDARD.encode(encrypted_data))
    }

    #[instrument(skip(self, encrypted_value))]
    pub fn decrypt_value(&self, encrypted_value: &str) -> ServiceResult<String> {
        let encrypted_data = general_purpose::STANDARD
            .decode(encrypted_value)
            .map_err(|e| ServiceError::Internal(format!("Base64 decode failed: {}", e)))?;

        if encrypted_data.len() < 12 {
            return Err(ServiceError::Internal("Invalid encrypted data".to_string()));
        }

        let (nonce_bytes, ciphertext) = encrypted_data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);

        let cipher = Aes256Gcm::new_from_slice(&self.encryption_key)
            .map_err(|e| ServiceError::Internal(format!("Decryption setup failed: {}", e)))?;

        let plaintext = cipher
            .decrypt(nonce, ciphertext)
            .map_err(|e| ServiceError::Internal(format!("Decryption failed: {}", e)))?;

        String::from_utf8(plaintext)
            .map_err(|e| ServiceError::Internal(format!("UTF-8 decode failed: {}", e)))
    }

    #[instrument(skip(self, user_id, name, variables, secrets))]
    pub async fn create_environment_group(
        &self,
        user_id: &str,
        name: String,
        description: Option<String>,
        variables: HashMap<String, String>,
        secrets: HashMap<String, String>,
    ) -> ServiceResult<EnvironmentGroup> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let now = chrono::Utc::now();

        // Encrypt secrets
        let mut encrypted_secrets = HashMap::new();
        for (key, value) in secrets {
            let encrypted_value = self.encrypt_value(&value)?;
            encrypted_secrets.insert(
                key.clone(),
                Secret {
                    key: key.clone(),
                    value: encrypted_value,
                    description: None,
                    created_at: now,
                    updated_at: now,
                    last_accessed: None,
                },
            );
        }

        // Create environment variables
        let mut env_variables = HashMap::new();
        for (key, value) in variables {
            env_variables.insert(
                key.clone(),
                EnvironmentVariable {
                    key: key.clone(),
                    value,
                    encrypted: false,
                    created_at: now,
                    updated_at: now,
                },
            );
        }

        let environment_group = EnvironmentGroup {
            id: None,
            user_id: user_object_id,
            name,
            description,
            variables: env_variables,
            secrets: encrypted_secrets,
            linked_applications: Vec::new(),
            created_at: now,
            updated_at: now,
        };

        let result = self.environment_groups
            .insert_one(&environment_group, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        let mut created_group = environment_group;
        created_group.id = result.inserted_id.as_object_id();

        info!("Environment group created: {}", created_group.id.unwrap());
        Ok(created_group)
    }

    #[instrument(skip(self, user_id, group_id))]
    pub async fn get_environment_group(&self, user_id: &str, group_id: &str) -> ServiceResult<EnvironmentGroup> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        
        let group_object_id = ObjectId::parse_str(group_id)
            .map_err(|_| ServiceError::Validation("Invalid group ID".to_string()))?;

        let group = self.environment_groups
            .find_one(doc! { "_id": group_object_id, "user_id": user_object_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .ok_or_else(|| ServiceError::NotFound("Environment group not found".to_string()))?;

        Ok(group)
    }

    #[instrument(skip(self, user_id, group_id, key, value))]
    pub async fn set_environment_variable(
        &self,
        user_id: &str,
        group_id: &str,
        key: String,
        value: String,
    ) -> ServiceResult<()> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        
        let group_object_id = ObjectId::parse_str(group_id)
            .map_err(|_| ServiceError::Validation("Invalid group ID".to_string()))?;

        let now = chrono::Utc::now();
        let env_var = EnvironmentVariable {
            key: key.clone(),
            value,
            encrypted: false,
            created_at: now,
            updated_at: now,
        };

        self.environment_groups
            .update_one(
                doc! { "_id": group_object_id, "user_id": user_object_id },
                doc! { 
                    "$set": { 
                        format!("variables.{}", key): &env_var,
                        "updated_at": now
                    }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Environment variable set: {} in group {}", key, group_id);
        Ok(())
    }

    #[instrument(skip(self, user_id, group_id, key, value))]
    pub async fn set_secret(
        &self,
        user_id: &str,
        group_id: &str,
        key: String,
        value: String,
        description: Option<String>,
    ) -> ServiceResult<()> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        
        let group_object_id = ObjectId::parse_str(group_id)
            .map_err(|_| ServiceError::Validation("Invalid group ID".to_string()))?;

        let encrypted_value = self.encrypt_value(&value)?;
        let now = chrono::Utc::now();

        let secret = Secret {
            key: key.clone(),
            value: encrypted_value,
            description,
            created_at: now,
            updated_at: now,
            last_accessed: None,
        };

        self.environment_groups
            .update_one(
                doc! { "_id": group_object_id, "user_id": user_object_id },
                doc! { 
                    "$set": { 
                        format!("secrets.{}", key): &secret,
                        "updated_at": now
                    }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Secret set: {} in group {}", key, group_id);
        Ok(())
    }

    #[instrument(skip(self, user_id, group_id, key))]
    pub async fn get_secret_value(&self, user_id: &str, group_id: &str, key: &str) -> ServiceResult<String> {
        let group = self.get_environment_group(user_id, group_id).await?;
        
        let secret = group.secrets.get(key)
            .ok_or_else(|| ServiceError::NotFound("Secret not found".to_string()))?;

        let decrypted_value = self.decrypt_value(&secret.value)?;

        // Update last accessed time
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        
        let group_object_id = ObjectId::parse_str(group_id)
            .map_err(|_| ServiceError::Validation("Invalid group ID".to_string()))?;

        let _ = self.environment_groups
            .update_one(
                doc! { "_id": group_object_id, "user_id": user_object_id },
                doc! { 
                    "$set": { 
                        format!("secrets.{}.last_accessed", key): chrono::Utc::now()
                    }
                },
                None,
            )
            .await;

        Ok(decrypted_value)
    }

    #[instrument(skip(self, user_id, group_id, key))]
    pub async fn delete_environment_variable(&self, user_id: &str, group_id: &str, key: &str) -> ServiceResult<()> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        
        let group_object_id = ObjectId::parse_str(group_id)
            .map_err(|_| ServiceError::Validation("Invalid group ID".to_string()))?;

        self.environment_groups
            .update_one(
                doc! { "_id": group_object_id, "user_id": user_object_id },
                doc! { 
                    "$unset": { format!("variables.{}", key): "" },
                    "$set": { "updated_at": chrono::Utc::now() }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Environment variable deleted: {} from group {}", key, group_id);
        Ok(())
    }

    #[instrument(skip(self, user_id, group_id, key))]
    pub async fn delete_secret(&self, user_id: &str, group_id: &str, key: &str) -> ServiceResult<()> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        
        let group_object_id = ObjectId::parse_str(group_id)
            .map_err(|_| ServiceError::Validation("Invalid group ID".to_string()))?;

        self.environment_groups
            .update_one(
                doc! { "_id": group_object_id, "user_id": user_object_id },
                doc! { 
                    "$unset": { format!("secrets.{}", key): "" },
                    "$set": { "updated_at": chrono::Utc::now() }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Secret deleted: {} from group {}", key, group_id);
        Ok(())
    }

    #[instrument(skip(self, user_id, group_id, app_id))]
    pub async fn link_application(&self, user_id: &str, group_id: &str, app_id: &str) -> ServiceResult<()> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        
        let group_object_id = ObjectId::parse_str(group_id)
            .map_err(|_| ServiceError::Validation("Invalid group ID".to_string()))?;
        
        let app_object_id = ObjectId::parse_str(app_id)
            .map_err(|_| ServiceError::Validation("Invalid application ID".to_string()))?;

        // Verify application belongs to user
        let app_exists = self.applications
            .find_one(doc! { "_id": app_object_id, "user_id": user_object_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .is_some();

        if !app_exists {
            return Err(ServiceError::NotFound("Application not found".to_string()));
        }

        self.environment_groups
            .update_one(
                doc! { "_id": group_object_id, "user_id": user_object_id },
                doc! { 
                    "$addToSet": { "linked_applications": app_object_id },
                    "$set": { "updated_at": chrono::Utc::now() }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Application {} linked to environment group {}", app_id, group_id);
        Ok(())
    }

    #[instrument(skip(self, app_id))]
    pub async fn get_application_environment(&self, app_id: &str) -> ServiceResult<HashMap<String, String>> {
        let app_object_id = ObjectId::parse_str(app_id)
            .map_err(|_| ServiceError::Validation("Invalid application ID".to_string()))?;

        // Find environment groups linked to this application
        let mut cursor = self.environment_groups
            .find(doc! { "linked_applications": app_object_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        let mut combined_env = HashMap::new();

        while let Some(group) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
            // Add environment variables
            for (key, var) in group.variables {
                combined_env.insert(key, var.value);
            }

            // Add decrypted secrets
            for (key, secret) in group.secrets {
                let decrypted_value = self.decrypt_value(&secret.value)?;
                combined_env.insert(key, decrypted_value);
            }
        }

        Ok(combined_env)
    }
}
