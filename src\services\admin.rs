use crate::{
    database::Database,
    models::{
        Instance, InstanceResponse, PaginatedResponse, PaginationQuery, User, UserProfile,

    },
    services::{ServiceError, ServiceResult},
    utils::Pagination,
};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use mongodb::Collection;
use tracing::instrument;
use futures::TryStreamExt;

pub struct AdminService {
    users: Collection<User>,
    instances: Collection<Instance>,
}

impl AdminService {
    pub fn new(database: &Database) -> Self {
        Self {
            users: database.collection("users"),
            instances: database.collection("instances"),
        }
    }

    #[instrument(skip(self))]
    pub async fn list_all_users(
        &self,
        pagination: PaginationQuery,
    ) -> ServiceResult<PaginatedResponse<UserProfile>> {
        let page = pagination.page.unwrap_or(1);
        let per_page = pagination.per_page.unwrap_or(20);

        // Get total count
        let total = self.users.count_documents(doc! {}, None).await?;

        // Get paginated results
        let pagination_info = Pagination::new(page, per_page, total);
        let mut find_options = mongodb::options::FindOptions::default();
        find_options.sort = Some(doc! { "created_at": -1 });
        find_options.skip = Some(pagination_info.offset());
        find_options.limit = Some(pagination_info.limit() as i64);

        let users: Vec<User> = self
            .users
            .find(doc! {}, find_options)
            .await?
            .try_collect()
            .await?;

        let user_profiles: Vec<UserProfile> = users
            .into_iter()
            .map(|user| user.into())
            .collect();

        Ok(PaginatedResponse {
            data: user_profiles,
            meta: crate::models::PaginationMeta {
                page: pagination_info.page,
                per_page: pagination_info.per_page,
                total: pagination_info.total,
                total_pages: pagination_info.total_pages,
            },
        })
    }

    #[instrument(skip(self))]
    pub async fn get_user_by_id(&self, user_id: &str) -> ServiceResult<UserProfile> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let user = self
            .users
            .find_one(doc! { "_id": user_object_id }, None)
            .await?
            .ok_or_else(|| ServiceError::NotFound("User not found".to_string()))?;

        Ok(user.into())
    }

    #[instrument(skip(self))]
    pub async fn suspend_user(&self, user_id: &str) -> ServiceResult<()> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        self.users
            .update_one(
                doc! { "_id": user_object_id },
                doc! { 
                    "$set": { 
                        "status": "Suspended",
                        "updated_at": Utc::now()
                    }
                },
                None,
            )
            .await?;

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn activate_user(&self, user_id: &str) -> ServiceResult<()> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        self.users
            .update_one(
                doc! { "_id": user_object_id },
                doc! { 
                    "$set": { 
                        "status": "Active",
                        "updated_at": Utc::now()
                    }
                },
                None,
            )
            .await?;

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_all_instances(
        &self,
        pagination: PaginationQuery,
    ) -> ServiceResult<PaginatedResponse<InstanceResponse>> {
        let page = pagination.page.unwrap_or(1);
        let per_page = pagination.per_page.unwrap_or(20);

        // Get total count
        let total = self.instances.count_documents(doc! {}, None).await?;

        // Get paginated results
        let pagination_info = Pagination::new(page, per_page, total);
        let mut find_options = mongodb::options::FindOptions::default();
        find_options.sort = Some(doc! { "created_at": -1 });
        find_options.skip = Some(pagination_info.offset());
        find_options.limit = Some(pagination_info.limit() as i64);

        let instances: Vec<Instance> = self
            .instances
            .find(doc! {}, find_options)
            .await?
            .try_collect()
            .await?;

        let instance_responses: Vec<InstanceResponse> = instances
            .into_iter()
            .map(|instance| instance.into())
            .collect();

        Ok(PaginatedResponse {
            data: instance_responses,
            meta: crate::models::PaginationMeta {
                page: pagination_info.page,
                per_page: pagination_info.per_page,
                total: pagination_info.total,
                total_pages: pagination_info.total_pages,
            },
        })
    }

    #[instrument(skip(self))]
    pub async fn get_billing_overview(&self) -> ServiceResult<serde_json::Value> {
        // Get total users
        let total_users = self.users.count_documents(doc! {}, None).await?;

        // Get active users
        let active_users = self
            .users
            .count_documents(doc! { "status": "Active" }, None)
            .await?;

        // Get total instances
        let total_instances = self.instances.count_documents(doc! {}, None).await?;

        // Get running instances
        let running_instances = self
            .instances
            .count_documents(doc! { "status": "Running" }, None)
            .await?;

        // Calculate total monthly revenue (simplified)
        let pipeline = vec![
            doc! {
                "$group": {
                    "_id": null,
                    "total_monthly_cost": { "$sum": "$monthly_cost" }
                }
            }
        ];

        let mut cursor = self.instances.aggregate(pipeline, None).await?;
        let total_monthly_revenue = if let Some(result) = cursor.try_next().await? {
            result.get_f64("total_monthly_cost").unwrap_or(0.0)
        } else {
            0.0
        };

        Ok(serde_json::json!({
            "users": {
                "total": total_users,
                "active": active_users,
                "suspended": total_users - active_users
            },
            "instances": {
                "total": total_instances,
                "running": running_instances,
                "stopped": total_instances - running_instances
            },
            "revenue": {
                "monthly_recurring": total_monthly_revenue,
                "currency": "USD"
            },
            "generated_at": Utc::now()
        }))
    }

    #[instrument(skip(self))]
    pub async fn get_system_metrics(&self) -> ServiceResult<serde_json::Value> {
        // This would integrate with your metrics system
        // For now, return basic database metrics
        
        let total_users = self.users.count_documents(doc! {}, None).await?;
        let total_instances = self.instances.count_documents(doc! {}, None).await?;

        // Get users created in the last 24 hours
        let yesterday = Utc::now() - chrono::Duration::hours(24);
        let new_users_24h = self
            .users
            .count_documents(doc! { "created_at": { "$gte": yesterday } }, None)
            .await?;

        // Get instances created in the last 24 hours
        let new_instances_24h = self
            .instances
            .count_documents(doc! { "created_at": { "$gte": yesterday } }, None)
            .await?;

        // Get running vs stopped instances
        let running_instances = self.instances
            .count_documents(doc! { "status": "running" }, None)
            .await?;

        let stopped_instances = self.instances
            .count_documents(doc! { "status": "stopped" }, None)
            .await?;

        // Get application metrics
        let applications_collection: mongodb::Collection<crate::models::Application> =
            self.database.collection("applications");

        let total_applications = applications_collection
            .count_documents(doc! {}, None)
            .await?;

        let active_applications = applications_collection
            .count_documents(doc! { "status": "running" }, None)
            .await?;

        // Get deployment success rate
        let deployments_collection: mongodb::Collection<crate::models::Deployment> =
            self.database.collection("deployments");

        let total_deployments = deployments_collection
            .count_documents(doc! {}, None)
            .await?;

        let successful_deployments = deployments_collection
            .count_documents(doc! { "status": "success" }, None)
            .await?;

        let deployment_success_rate = if total_deployments > 0 {
            (successful_deployments as f64 / total_deployments as f64) * 100.0
        } else {
            0.0
        };

        // Calculate system uptime (simplified)
        let app_start_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() - 3600; // Assume app started 1 hour ago for demo

        let uptime_seconds = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() - app_start_time;

        Ok(serde_json::json!({
            "users": {
                "total": total_users,
                "new_24h": new_users_24h,
                "active_last_30_days": total_users // Simplified - would calculate actual active users
            },
            "applications": {
                "total": total_applications,
                "active": active_applications,
                "inactive": total_applications - active_applications
            },
            "instances": {
                "total": total_instances,
                "running": running_instances,
                "stopped": stopped_instances,
                "new_24h": new_instances_24h
            },
            "deployments": {
                "total": total_deployments,
                "successful": successful_deployments,
                "success_rate": deployment_success_rate
            },
            "system": {
                "uptime_seconds": uptime_seconds,
                "uptime_percentage": 99.9, // Would calculate from monitoring data
                "version": env!("CARGO_PKG_VERSION"),
                "environment": self.config.environment.clone().unwrap_or_else(|| "production".to_string())
            },
            "generated_at": Utc::now()
        }))
    }
}
