use metrics::{counter, gauge, histogram, describe_counter, describe_gauge, describe_histogram};
use metrics_exporter_prometheus::PrometheusBuilder;
use std::time::{Duration, Instant};
use tracing::{info, error};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use dashmap::DashMap;

#[derive(Clone)]
pub struct MetricsService {
    custom_metrics: Arc<DashMap<String, CustomMetric>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomMetric {
    pub name: String,
    pub value: f64,
    pub labels: Vec<(String, String)>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl MetricsService {
    pub fn new() -> Self {
        Self {
            custom_metrics: Arc::new(DashMap::new()),
        }
    }

    pub fn init_prometheus_exporter(port: u16) -> Result<(), Box<dyn std::error::Error>> {
        let builder = PrometheusBuilder::new()
            .with_http_listener(([0, 0, 0, 0], port));

        builder.install()?;

        // Describe all metrics
        Self::describe_metrics();

        info!("Prometheus metrics exporter started on port {}", port);
        Ok(())
    }

    fn describe_metrics() {
        // Application metrics
        describe_counter!("applications_total", "Total number of applications");
        describe_counter!("applications_created_total", "Total number of applications created");
        describe_counter!("applications_deleted_total", "Total number of applications deleted");
        describe_gauge!("applications_active", "Number of active applications");

        // Deployment metrics
        describe_counter!("deployments_total", "Total number of deployments");
        describe_counter!("deployments_successful_total", "Total number of successful deployments");
        describe_counter!("deployments_failed_total", "Total number of failed deployments");
        describe_histogram!("deployment_duration_seconds", "Deployment duration in seconds");
        describe_gauge!("deployments_in_progress", "Number of deployments in progress");

        // Build metrics
        describe_counter!("builds_total", "Total number of builds");
        describe_counter!("builds_successful_total", "Total number of successful builds");
        describe_counter!("builds_failed_total", "Total number of failed builds");
        describe_histogram!("build_duration_seconds", "Build duration in seconds");
        describe_gauge!("builds_queued", "Number of builds in queue");

        // API metrics
        describe_counter!("api_requests_total", "Total number of API requests");
        describe_histogram!("api_request_duration_seconds", "API request duration in seconds");
        describe_counter!("api_errors_total", "Total number of API errors");

        // Infrastructure metrics
        describe_gauge!("circuit_breaker_open", "Circuit breaker open status");
        describe_counter!("circuit_breaker_trips_total", "Total number of circuit breaker trips");
        describe_gauge!("rate_limit_remaining", "Remaining rate limit capacity");

        // Database metrics
        describe_counter!("database_operations_total", "Total number of database operations");
        describe_histogram!("database_operation_duration_seconds", "Database operation duration");
        describe_counter!("database_errors_total", "Total number of database errors");

        // External API metrics
        describe_counter!("external_api_calls_total", "Total number of external API calls");
        describe_histogram!("external_api_duration_seconds", "External API call duration");
        describe_counter!("external_api_errors_total", "Total number of external API errors");
    }

    // Application metrics
    pub fn increment_applications_created(&self) {
        counter!("applications_created_total").increment(1);
        counter!("applications_total").increment(1);
    }

    pub fn increment_applications_deleted(&self) {
        counter!("applications_deleted_total").increment(1);
        counter!("applications_total").increment(-1);
    }

    pub fn set_active_applications(&self, count: f64) {
        gauge!("applications_active").set(count);
    }

    // Deployment metrics
    pub fn increment_deployments_started(&self) {
        counter!("deployments_total").increment(1);
        gauge!("deployments_in_progress").increment(1.0);
    }

    pub fn increment_deployments_successful(&self, duration: Duration) {
        counter!("deployments_successful_total").increment(1);
        histogram!("deployment_duration_seconds").record(duration.as_secs_f64());
        gauge!("deployments_in_progress").decrement(1.0);
    }

    pub fn increment_deployments_failed(&self, duration: Duration) {
        counter!("deployments_failed_total").increment(1);
        histogram!("deployment_duration_seconds").record(duration.as_secs_f64());
        gauge!("deployments_in_progress").decrement(1.0);
    }

    // Build metrics
    pub fn increment_builds_started(&self) {
        counter!("builds_total").increment(1);
        gauge!("builds_queued").increment(1.0);
    }

    pub fn increment_builds_successful(&self, duration: Duration) {
        counter!("builds_successful_total").increment(1);
        histogram!("build_duration_seconds").record(duration.as_secs_f64());
        gauge!("builds_queued").decrement(1.0);
    }

    pub fn increment_builds_failed(&self, duration: Duration) {
        counter!("builds_failed_total").increment(1);
        histogram!("build_duration_seconds").record(duration.as_secs_f64());
        gauge!("builds_queued").decrement(1.0);
    }

    // API metrics
    pub fn record_api_request(&self, method: &str, path: &str, status_code: u16, duration: Duration) {
        let labels = [
            ("method", method),
            ("path", path),
            ("status_code", &status_code.to_string()),
        ];

        counter!("api_requests_total", &labels).increment(1);
        histogram!("api_request_duration_seconds", &labels).record(duration.as_secs_f64());

        if status_code >= 400 {
            counter!("api_errors_total", &labels).increment(1);
        }
    }

    // Circuit breaker metrics
    pub fn set_circuit_breaker_status(&self, service: &str, is_open: bool) {
        let labels = [("service", service)];
        gauge!("circuit_breaker_open", &labels).set(if is_open { 1.0 } else { 0.0 });
    }

    pub fn increment_circuit_breaker_trips(&self, service: &str) {
        let labels = [("service", service)];
        counter!("circuit_breaker_trips_total", &labels).increment(1);
    }

    // Rate limiting metrics
    pub fn set_rate_limit_remaining(&self, key: &str, remaining: u32) {
        let labels = [("key", key)];
        gauge!("rate_limit_remaining", &labels).set(remaining as f64);
    }

    // Database metrics
    pub fn record_database_operation(&self, operation: &str, duration: Duration, success: bool) {
        let labels = [("operation", operation)];
        
        counter!("database_operations_total", &labels).increment(1);
        histogram!("database_operation_duration_seconds", &labels).record(duration.as_secs_f64());

        if !success {
            counter!("database_errors_total", &labels).increment(1);
        }
    }

    // External API metrics
    pub fn record_external_api_call(&self, service: &str, operation: &str, duration: Duration, success: bool) {
        let labels = [("service", service), ("operation", operation)];
        
        counter!("external_api_calls_total", &labels).increment(1);
        histogram!("external_api_duration_seconds", &labels).record(duration.as_secs_f64());

        if !success {
            counter!("external_api_errors_total", &labels).increment(1);
        }
    }

    // Custom metrics
    pub fn record_custom_metric(&self, name: &str, value: f64, labels: Vec<(String, String)>) {
        let metric = CustomMetric {
            name: name.to_string(),
            value,
            labels,
            timestamp: chrono::Utc::now(),
        };

        self.custom_metrics.insert(name.to_string(), metric);
    }

    pub fn get_custom_metrics(&self) -> Vec<CustomMetric> {
        self.custom_metrics.iter().map(|entry| entry.value().clone()).collect()
    }

    // Health metrics
    pub fn record_health_check(&self, component: &str, healthy: bool, response_time: Duration) {
        let labels = [("component", component)];
        
        gauge!("health_status", &labels).set(if healthy { 1.0 } else { 0.0 });
        histogram!("health_check_duration_seconds", &labels).record(response_time.as_secs_f64());
    }
}

impl Default for MetricsService {
    fn default() -> Self {
        Self::new()
    }
}

/// Timer helper for measuring operation duration
pub struct MetricsTimer {
    start: Instant,
    service: Arc<MetricsService>,
    operation: String,
    labels: Vec<(String, String)>,
}

impl MetricsTimer {
    pub fn new(service: Arc<MetricsService>, operation: String, labels: Vec<(String, String)>) -> Self {
        Self {
            start: Instant::now(),
            service,
            operation,
            labels,
        }
    }

    pub fn finish(self, success: bool) {
        let duration = self.start.elapsed();
        
        // Record based on operation type
        if self.operation.starts_with("db_") {
            self.service.record_database_operation(&self.operation, duration, success);
        } else if self.operation.starts_with("api_") {
            if let Some(service_label) = self.labels.iter().find(|(k, _)| k == "service") {
                self.service.record_external_api_call(&service_label.1, &self.operation, duration, success);
            }
        }
    }
}

/// Macro for easy metrics timing
#[macro_export]
macro_rules! time_operation {
    ($metrics:expr, $operation:expr, $labels:expr, $code:block) => {{
        let timer = MetricsTimer::new($metrics.clone(), $operation.to_string(), $labels);
        let result = $code;
        let success = result.is_ok();
        timer.finish(success);
        result
    }};
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_metrics_service_creation() {
        let service = MetricsService::new();
        assert!(service.custom_metrics.is_empty());
    }

    #[test]
    fn test_custom_metrics() {
        let service = MetricsService::new();
        
        service.record_custom_metric(
            "test_metric",
            42.0,
            vec![("label1".to_string(), "value1".to_string())],
        );

        let metrics = service.get_custom_metrics();
        assert_eq!(metrics.len(), 1);
        assert_eq!(metrics[0].name, "test_metric");
        assert_eq!(metrics[0].value, 42.0);
    }

    #[tokio::test]
    async fn test_metrics_timer() {
        let service = Arc::new(MetricsService::new());
        let timer = MetricsTimer::new(
            service.clone(),
            "test_operation".to_string(),
            vec![],
        );

        // Simulate some work
        tokio::time::sleep(Duration::from_millis(10)).await;
        
        timer.finish(true);
        // Timer should have recorded the duration
    }
}
