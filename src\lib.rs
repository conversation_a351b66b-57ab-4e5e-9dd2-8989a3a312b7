pub mod config;
pub mod controllers;
pub mod database;
pub mod infrastructure;
pub mod middleware;
pub mod models;
pub mod observability;
pub mod routes;
pub mod services;
pub mod utils;
pub mod vultr;

pub use config::Config;
pub use database::Database;
pub use vultr::VultrClient;

// Application state shared across the application
#[derive(Clone)]
pub struct AppState {
    pub config: Config,
    pub database: Database,
    pub vultr_client: VultrClient,
}
