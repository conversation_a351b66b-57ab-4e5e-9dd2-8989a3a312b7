use crate::{
    controllers::{success_response, ControllerResult},
    middleware::auth::get_current_user,
    models::{BillingResponse, InvoiceResponse, PaginatedResponse, PaginationQuery},
    services::billing::BillingService,
    AppState,
};
use axum::{
    extract::{Path, Query, Request, State},
    Json,
};
use std::sync::Arc;
use tracing::instrument;
use validator::Validate;

#[instrument(skip(state, req))]
pub async fn get_billing_info(
    State(state): State<Arc<AppState>>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<BillingResponse>>> {
    let claims = get_current_user(&req)?;

    let billing_service = BillingService::new(&state.database);
    let billing_info = billing_service.get_billing_info(&claims.sub).await?;

    Ok(success_response(billing_info))
}

#[instrument(skip(state, req))]
pub async fn list_invoices(
    State(state): State<Arc<AppState>>,
    Query(pagination): Query<PaginationQuery>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<PaginatedResponse<InvoiceResponse>>>> {
    let claims = get_current_user(&req)?;
    
    pagination.validate().map_err(|e| {
        crate::controllers::ControllerError::Validation(format!("pagination parameters are invalid: {}", e))
    })?;

    let billing_service = BillingService::new(&state.database);
    let invoices = billing_service
        .list_user_invoices(&claims.sub, pagination)
        .await?;

    Ok(success_response(invoices))
}

#[instrument(skip(state, req))]
pub async fn get_invoice(
    State(state): State<Arc<AppState>>,
    Path(invoice_id): Path<String>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<InvoiceResponse>>> {
    let claims = get_current_user(&req)?;

    // Inline validation checks
    if invoice_id.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("invoice_id is required".to_string()));
    }

    let billing_service = BillingService::new(&state.database);
    let invoice = billing_service
        .get_user_invoice(&claims.sub, &invoice_id)
        .await?;

    Ok(success_response(invoice))
}

#[instrument(skip(state, req))]
pub async fn get_usage(
    State(state): State<Arc<AppState>>,
    Query(params): Query<serde_json::Value>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<serde_json::Value>>> {
    let claims = get_current_user(&req)?;

    let billing_service = BillingService::new(&state.database);
    let usage = billing_service.get_usage_data(&claims.sub, params).await?;

    Ok(success_response(usage))
}
