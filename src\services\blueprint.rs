use crate::{
    config::Config,
    database::Database,
    infrastructure::{
        CircuitBreakerService, CircuitBreakerAware, MetricsService, RateLimiterService,
        ChunkProcessor, ChunkProcessorConfig
    },
    models::{Blueprint, BlueprintStatus, BlueprintSync, SyncState, BlueprintResource},
    services::{ServiceError, ServiceResult},
    with_circuit_breaker,
};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use futures::TryStreamExt;
use mongodb::Collection;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{error, info, instrument, warn};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateBlueprintRequest {
    pub name: String,
    pub repo: String,
    pub branch: String,
    pub auto_sync: bool,
    pub owner_id: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateBlueprintRequest {
    pub name: Option<String>,
    pub auto_sync: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlueprintResponse {
    pub id: String,
    pub name: String,
    pub status: BlueprintStatus,
    pub auto_sync: bool,
    pub repo: String,
    pub branch: String,
    pub last_sync: Option<chrono::DateTime<chrono::Utc>>,
    pub resources: Vec<BlueprintResourceResponse>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlueprintResourceResponse {
    pub id: String,
    pub name: String,
    pub resource_type: String,
    pub status: String,
}

pub struct BlueprintService<'a> {
    blueprints: Collection<Blueprint>,
    blueprint_syncs: Collection<BlueprintSync>,
    config: &'a Config,
    circuit_breaker: CircuitBreakerService,
    metrics: MetricsService,
    rate_limiter: RateLimiterService,
    chunk_processor: ChunkProcessor,
}

impl<'a> BlueprintService<'a> {
    pub fn new(database: &Database, config: &'a Config) -> Self {
        let circuit_breaker = CircuitBreakerService::new();
        let metrics = MetricsService::new();
        let rate_limiter = RateLimiterService::new();
        let chunk_processor = ChunkProcessor::new(ChunkProcessorConfig::default());

        Self {
            blueprints: database.collection("blueprints"),
            blueprint_syncs: database.collection("blueprint_syncs"),
            config,
            circuit_breaker,
            metrics,
            rate_limiter,
            chunk_processor,
        }
    }

    /// Create a new blueprint from render.yaml
    #[instrument(skip(self))]
    pub async fn create_blueprint(&self, request: CreateBlueprintRequest) -> ServiceResult<BlueprintResponse> {
        // Rate limit blueprint creation
        self.rate_limiter.check_api_rate_limit(&request.owner_id).await?;

        // Validate repository access
        self.validate_repository_access(&request.repo, &request.branch).await?;

        // Parse render.yaml from repository
        let resources = self.parse_render_yaml(&request.repo, &request.branch).await?;

        let blueprint = Blueprint {
            id: None,
            name: request.name.clone(),
            status: BlueprintStatus::Created,
            auto_sync: request.auto_sync,
            repo: request.repo.clone(),
            branch: request.branch.clone(),
            owner_id: ObjectId::parse_str(&request.owner_id)
                .map_err(|_| ServiceError::Validation("Invalid owner ID".to_string()))?,
            resources,
            last_sync: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let result = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.blueprints.insert_one(&blueprint, None).await
                    .map_err(|e| ServiceError::Database(e))
            }
        )?;

        let blueprint_id = result.inserted_id.as_object_id()
            .ok_or_else(|| ServiceError::Internal("Failed to get blueprint ID".to_string()))?;

        // Trigger initial sync if auto_sync is enabled
        if request.auto_sync {
            self.trigger_sync(blueprint_id).await?;
        }

        self.metrics.record_custom_metric("blueprints_created", 1.0, vec![]);

        Ok(BlueprintResponse {
            id: blueprint_id.to_hex(),
            name: blueprint.name,
            status: blueprint.status,
            auto_sync: blueprint.auto_sync,
            repo: blueprint.repo,
            branch: blueprint.branch,
            last_sync: blueprint.last_sync,
            resources: blueprint.resources.into_iter().map(|r| BlueprintResourceResponse {
                id: r.id,
                name: r.name,
                resource_type: r.resource_type,
                status: "pending".to_string(),
            }).collect(),
        })
    }

    /// Update blueprint configuration
    #[instrument(skip(self))]
    pub async fn update_blueprint(
        &self,
        blueprint_id: &str,
        request: UpdateBlueprintRequest,
    ) -> ServiceResult<BlueprintResponse> {
        let blueprint_object_id = ObjectId::parse_str(blueprint_id)
            .map_err(|_| ServiceError::Validation("Invalid blueprint ID".to_string()))?;

        let mut update_doc = doc! { "$set": { "updated_at": Utc::now() } };

        if let Some(name) = request.name {
            update_doc.get_document_mut("$set").unwrap().insert("name", name);
        }

        if let Some(auto_sync) = request.auto_sync {
            update_doc.get_document_mut("$set").unwrap().insert("auto_sync", auto_sync);
        }

        with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.blueprints
                    .update_one(doc! { "_id": blueprint_object_id }, update_doc, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?;
                Ok(())
            }
        )?;

        self.get_blueprint(blueprint_id).await
    }

    /// Get blueprint by ID
    #[instrument(skip(self))]
    pub async fn get_blueprint(&self, blueprint_id: &str) -> ServiceResult<BlueprintResponse> {
        let blueprint_object_id = ObjectId::parse_str(blueprint_id)
            .map_err(|_| ServiceError::Validation("Invalid blueprint ID".to_string()))?;

        let blueprint = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.blueprints
                    .find_one(doc! { "_id": blueprint_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?
                    .ok_or_else(|| ServiceError::NotFound("Blueprint not found".to_string()))
            }
        )?;

        Ok(BlueprintResponse {
            id: blueprint.id.unwrap().to_hex(),
            name: blueprint.name,
            status: blueprint.status,
            auto_sync: blueprint.auto_sync,
            repo: blueprint.repo,
            branch: blueprint.branch,
            last_sync: blueprint.last_sync,
            resources: blueprint.resources.into_iter().map(|r| BlueprintResourceResponse {
                id: r.id,
                name: r.name,
                resource_type: r.resource_type,
                status: "active".to_string(), // Would check actual resource status
            }).collect(),
        })
    }

    /// Trigger blueprint sync
    #[instrument(skip(self))]
    pub async fn trigger_sync(&self, blueprint_id: ObjectId) -> ServiceResult<String> {
        let sync = BlueprintSync {
            id: None,
            blueprint_id,
            commit_id: "latest".to_string(), // Would get actual commit
            state: SyncState::Pending,
            started_at: Some(Utc::now()),
            completed_at: None,
            error_message: None,
            resources_synced: 0,
            resources_total: 0,
        };

        let result = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.blueprint_syncs.insert_one(&sync, None).await
                    .map_err(|e| ServiceError::Database(e))
            }
        )?;

        let sync_id = result.inserted_id.as_object_id()
            .ok_or_else(|| ServiceError::Internal("Failed to get sync ID".to_string()))?;

        // Start async sync process
        self.execute_sync(sync_id, blueprint_id).await?;

        Ok(sync_id.to_hex())
    }

    /// Execute blueprint sync
    async fn execute_sync(&self, sync_id: ObjectId, blueprint_id: ObjectId) -> ServiceResult<()> {
        // Update sync state to running
        with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.blueprint_syncs
                    .update_one(
                        doc! { "_id": sync_id },
                        doc! { "$set": { "state": "running" } },
                        None,
                    )
                    .await
                    .map_err(|e| ServiceError::Database(e))?;
                Ok(())
            }
        )?;

        // Get blueprint
        let blueprint = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.blueprints
                    .find_one(doc! { "_id": blueprint_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?
                    .ok_or_else(|| ServiceError::NotFound("Blueprint not found".to_string()))
            }
        )?;

        // Parse latest render.yaml
        let resources = match self.parse_render_yaml(&blueprint.repo, &blueprint.branch).await {
            Ok(resources) => resources,
            Err(e) => {
                // Mark sync as failed
                self.mark_sync_failed(sync_id, &e.to_string()).await?;
                return Err(e);
            }
        };

        // Sync resources
        let sync_result = self.sync_resources(blueprint_id, resources).await;

        match sync_result {
            Ok(synced_count) => {
                // Mark sync as successful
                with_circuit_breaker!(
                    self.circuit_breaker,
                    "database",
                    {
                        self.blueprint_syncs
                            .update_one(
                                doc! { "_id": sync_id },
                                doc! { 
                                    "$set": { 
                                        "state": "success",
                                        "completed_at": Utc::now(),
                                        "resources_synced": synced_count as i32
                                    }
                                },
                                None,
                            )
                            .await
                            .map_err(|e| ServiceError::Database(e))?;
                        Ok(())
                    }
                )?;

                // Update blueprint last_sync
                with_circuit_breaker!(
                    self.circuit_breaker,
                    "database",
                    {
                        self.blueprints
                            .update_one(
                                doc! { "_id": blueprint_id },
                                doc! { "$set": { "last_sync": Utc::now(), "status": "in_sync" } },
                                None,
                            )
                            .await
                            .map_err(|e| ServiceError::Database(e))?;
                        Ok(())
                    }
                )?;

                info!("Blueprint sync completed successfully: {} resources synced", synced_count);
            }
            Err(e) => {
                self.mark_sync_failed(sync_id, &e.to_string()).await?;
                return Err(e);
            }
        }

        Ok(())
    }

    async fn mark_sync_failed(&self, sync_id: ObjectId, error_message: &str) -> ServiceResult<()> {
        with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.blueprint_syncs
                    .update_one(
                        doc! { "_id": sync_id },
                        doc! { 
                            "$set": { 
                                "state": "error",
                                "completed_at": Utc::now(),
                                "error_message": error_message
                            }
                        },
                        None,
                    )
                    .await
                    .map_err(|e| ServiceError::Database(e))?;
                Ok(())
            }
        )
    }

    async fn validate_repository_access(&self, repo: &str, branch: &str) -> ServiceResult<()> {
        // Validate repository URL format
        if !repo.starts_with("https://github.com/") && !repo.starts_with("https://gitlab.com/") {
            return Err(ServiceError::Validation("Invalid repository URL".to_string()));
        }

        // In production, would verify access to the repository
        info!("Validating access to repository: {} branch: {}", repo, branch);
        Ok(())
    }

    async fn parse_render_yaml(&self, repo: &str, branch: &str) -> ServiceResult<Vec<BlueprintResource>> {
        // In production, would fetch and parse render.yaml from the repository
        info!("Parsing render.yaml from repository: {} branch: {}", repo, branch);
        
        // Mock resources for now - in production would parse actual YAML
        Ok(vec![
            BlueprintResource {
                id: Uuid::new_v4().to_string(),
                name: "web-service".to_string(),
                resource_type: "web_service".to_string(),
                config: HashMap::new(),
            },
            BlueprintResource {
                id: Uuid::new_v4().to_string(),
                name: "database".to_string(),
                resource_type: "postgres".to_string(),
                config: HashMap::new(),
            },
        ])
    }

    async fn sync_resources(&self, blueprint_id: ObjectId, resources: Vec<BlueprintResource>) -> ServiceResult<usize> {
        info!("Syncing {} resources for blueprint: {}", resources.len(), blueprint_id);
        
        // Process resources in chunks to avoid overwhelming the system
        let synced_resources = self.chunk_processor.process_with_queue_monitoring(
            "blueprint_sync",
            resources,
            |resource_chunk| {
                // In production, would create/update actual resources
                for resource in &resource_chunk {
                    info!("Syncing resource: {} ({})", resource.name, resource.resource_type);
                }
                Ok(resource_chunk)
            }
        ).await?;

        Ok(synced_resources.len())
    }
}

impl<'a> CircuitBreakerAware for BlueprintService<'a> {
    fn circuit_breaker(&self) -> &CircuitBreakerService {
        &self.circuit_breaker
    }
}
