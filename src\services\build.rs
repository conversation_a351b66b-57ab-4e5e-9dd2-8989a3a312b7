use crate::{
    config::Config,
    database::Database,
    infrastructure::{
        CircuitBreakerService, CircuitBreakerAware, BuildStateManager, BuildState,
        BuildEvent, StateMachineManager, ChunkProcessor, MetricsService, RateLimiterService
    },
    models::{
        BuildJob, BuildStatus, BuildConfig, BuildSource, BuildArtifact, BuildLogEntry,
        BuildMetrics, BuildWorker, WorkerStatus, BuildQueue, BuildPriority,
        CreateBuildJobRequest, BuildJobResponse, BuildLogsResponse, BuildStep,
        LogLevel, LogStream, ArtifactType, BuildRuntime
    },
    services::{ServiceError, ServiceResult},
    vultr::VultrClient,
    with_circuit_breaker,
};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use futures::TryStreamExt;
use mongodb::Collection;
use std::collections::HashMap;
use std::process::Stdio;
use tokio::process::Command;
use tokio::io::{AsyncBufReadExt, BufReader};
use tracing::{error, info, instrument, warn};
use uuid::Uuid;

pub struct BuildService<'a> {
    build_jobs: Collection<BuildJob>,
    build_workers: Collection<BuildWorker>,
    build_queue: Collection<BuildQueue>,
    vultr_client: &'a VultrClient,
    config: &'a Config,
    circuit_breaker: CircuitBreakerService,
    metrics: MetricsService,
    rate_limiter: RateLimiterService,
    chunk_processor: ChunkProcessor,
}

impl<'a> BuildService<'a> {
    pub fn new(database: &Database, vultr_client: &'a VultrClient, config: &'a Config) -> Self {
        let circuit_breaker = CircuitBreakerService::new();
        let metrics = MetricsService::new();
        let rate_limiter = RateLimiterService::new();
        let chunk_processor = ChunkProcessor::new(ChunkProcessorConfig {
            chunk_size: 20,
            delay_between_chunks: std::time::Duration::from_millis(50),
            max_retries: 3,
            retry_delay: std::time::Duration::from_secs(1),
            max_concurrent_chunks: 2,
        });

        Self {
            build_jobs: database.collection("build_jobs"),
            build_workers: database.collection("build_workers"),
            build_queue: database.collection("build_queue"),
            vultr_client,
            config,
            circuit_breaker,
            metrics,
            rate_limiter,
            chunk_processor,
        }
    }

    #[instrument(skip(self, request))]
    pub async fn create_build_job(&self, request: CreateBuildJobRequest) -> ServiceResult<BuildJobResponse> {
        let application_id = ObjectId::parse_str(&request.application_id)
            .map_err(|_| ServiceError::Validation("Invalid application ID".to_string()))?;
        
        let deployment_id = ObjectId::parse_str(&request.deployment_id)
            .map_err(|_| ServiceError::Validation("Invalid deployment ID".to_string()))?;

        let build_job = BuildJob {
            id: None,
            application_id,
            deployment_id,
            status: BuildStatus::Queued,
            build_config: request.build_config.clone(),
            source: request.source.clone(),
            artifacts: Vec::new(),
            logs: Vec::new(),
            metrics: BuildMetrics {
                total_duration: None,
                clone_duration: None,
                install_duration: None,
                build_duration: None,
                test_duration: None,
                package_duration: None,
                cache_hit_rate: 0.0,
                artifact_size: 0,
                peak_memory_usage: 0,
                cpu_time: 0,
            },
            created_at: Utc::now(),
            started_at: None,
            completed_at: None,
            failed_at: None,
            worker_id: None,
        };

        let result = self.build_jobs
            .insert_one(&build_job, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        let job_id = result.inserted_id.as_object_id()
            .ok_or_else(|| ServiceError::Internal("Failed to get inserted job ID".to_string()))?;

        // Add to build queue
        let queue_entry = BuildQueue {
            id: None,
            priority: request.priority,
            job_id,
            estimated_duration: self.estimate_build_duration(&request.build_config).await,
            queued_at: Utc::now(),
            assigned_worker: None,
            assigned_at: None,
        };

        self.build_queue
            .insert_one(&queue_entry, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Build job created and queued: {}", job_id);

        // Try to assign to available worker
        self.try_assign_worker(job_id).await?;

        Ok(BuildJobResponse {
            id: job_id.to_hex(),
            application_id: request.application_id,
            deployment_id: request.deployment_id,
            status: BuildStatus::Queued,
            build_config: request.build_config,
            source: request.source,
            artifacts: Vec::new(),
            metrics: build_job.metrics,
            created_at: build_job.created_at,
            started_at: None,
            completed_at: None,
            failed_at: None,
        })
    }

    #[instrument(skip(self, job_id))]
    pub async fn get_build_job(&self, job_id: &str) -> ServiceResult<BuildJobResponse> {
        let object_id = ObjectId::parse_str(job_id)
            .map_err(|_| ServiceError::Validation("Invalid job ID".to_string()))?;

        let job = self.build_jobs
            .find_one(doc! { "_id": object_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .ok_or_else(|| ServiceError::NotFound("Build job not found".to_string()))?;

        Ok(BuildJobResponse {
            id: job.id.unwrap().to_hex(),
            application_id: job.application_id.to_hex(),
            deployment_id: job.deployment_id.to_hex(),
            status: job.status,
            build_config: job.build_config,
            source: job.source,
            artifacts: job.artifacts,
            metrics: job.metrics,
            created_at: job.created_at,
            started_at: job.started_at,
            completed_at: job.completed_at,
            failed_at: job.failed_at,
        })
    }

    #[instrument(skip(self, job_id))]
    pub async fn get_build_logs(&self, job_id: &str, cursor: Option<String>, limit: Option<u32>) -> ServiceResult<BuildLogsResponse> {
        let object_id = ObjectId::parse_str(job_id)
            .map_err(|_| ServiceError::Validation("Invalid job ID".to_string()))?;

        let job = self.build_jobs
            .find_one(doc! { "_id": object_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .ok_or_else(|| ServiceError::NotFound("Build job not found".to_string()))?;

        let limit = limit.unwrap_or(100) as usize;
        let logs = if let Some(_cursor) = cursor {
            // Implement cursor-based pagination
            job.logs.into_iter().take(limit).collect()
        } else {
            job.logs.into_iter().take(limit).collect()
        };

        Ok(BuildLogsResponse {
            logs,
            has_more: false, // Implement proper pagination
            next_cursor: None,
        })
    }

    #[instrument(skip(self, job_id))]
    async fn try_assign_worker(&self, job_id: ObjectId) -> ServiceResult<()> {
        // Find available worker
        let available_worker = self.build_workers
            .find_one(doc! { 
                "status": "Available",
                "current_job": { "$exists": false }
            }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        if let Some(worker) = available_worker {
            // Assign job to worker
            self.build_queue
                .update_one(
                    doc! { "job_id": job_id },
                    doc! { 
                        "$set": { 
                            "assigned_worker": &worker.worker_id,
                            "assigned_at": Utc::now()
                        }
                    },
                    None,
                )
                .await
                .map_err(|e| ServiceError::Database(e))?;

            self.build_workers
                .update_one(
                    doc! { "_id": worker.id },
                    doc! { 
                        "$set": { 
                            "status": "Busy",
                            "current_job": job_id
                        }
                    },
                    None,
                )
                .await
                .map_err(|e| ServiceError::Database(e))?;

            info!("Assigned build job {} to worker {}", job_id, worker.worker_id);

            // Start the build process
            self.start_build_execution(job_id, &worker.worker_id).await?;
        }

        Ok(())
    }

    #[instrument(skip(self, job_id, worker_id))]
    async fn start_build_execution(&self, job_id: ObjectId, worker_id: &str) -> ServiceResult<()> {
        // Update job status to running
        self.build_jobs
            .update_one(
                doc! { "_id": job_id },
                doc! { 
                    "$set": { 
                        "status": "Running",
                        "started_at": Utc::now(),
                        "worker_id": worker_id
                    }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        // Spawn build task
        let build_service = self.clone(); // We'd need to implement Clone or use Arc
        tokio::spawn(async move {
            if let Err(e) = build_service.execute_build(job_id).await {
                error!("Build execution failed for job {}: {}", job_id, e);
                // Update job status to failed
                let _ = build_service.mark_build_failed(job_id, &e.to_string()).await;
            }
        });

        Ok(())
    }

    #[instrument(skip(self, job_id))]
    async fn execute_build(&self, job_id: ObjectId) -> ServiceResult<()> {
        let job = self.build_jobs
            .find_one(doc! { "_id": job_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .ok_or_else(|| ServiceError::NotFound("Build job not found".to_string()))?;

        let build_start = std::time::Instant::now();

        // Step 1: Clone repository
        self.log_build_step(job_id, BuildStep::Clone, LogLevel::Info, "Cloning repository...").await?;
        let clone_start = std::time::Instant::now();
        self.clone_repository(&job).await?;
        let clone_duration = clone_start.elapsed().as_secs();

        // Step 2: Install dependencies
        self.log_build_step(job_id, BuildStep::InstallDependencies, LogLevel::Info, "Installing dependencies...").await?;
        let install_start = std::time::Instant::now();
        self.install_dependencies(&job).await?;
        let install_duration = install_start.elapsed().as_secs();

        // Step 3: Build application
        self.log_build_step(job_id, BuildStep::Build, LogLevel::Info, "Building application...").await?;
        let build_start_time = std::time::Instant::now();
        self.build_application(&job).await?;
        let build_duration = build_start_time.elapsed().as_secs();

        // Step 4: Package artifacts
        self.log_build_step(job_id, BuildStep::Package, LogLevel::Info, "Packaging artifacts...").await?;
        let package_start = std::time::Instant::now();
        let artifacts = self.package_artifacts(&job).await?;
        let package_duration = package_start.elapsed().as_secs();

        let total_duration = build_start.elapsed().as_secs();

        // Update job with completion
        self.build_jobs
            .update_one(
                doc! { "_id": job_id },
                doc! { 
                    "$set": { 
                        "status": "Success",
                        "completed_at": Utc::now(),
                        "artifacts": &artifacts,
                        "metrics.total_duration": total_duration as i64,
                        "metrics.clone_duration": clone_duration as i64,
                        "metrics.install_duration": install_duration as i64,
                        "metrics.build_duration": build_duration as i64,
                        "metrics.package_duration": package_duration as i64,
                    }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        // Release worker
        self.release_worker(&job.worker_id.unwrap_or_default()).await?;

        info!("Build completed successfully for job {}", job_id);
        Ok(())
    }

    #[instrument(skip(self, job_id, error_message))]
    async fn mark_build_failed(&self, job_id: ObjectId, error_message: &str) -> ServiceResult<()> {
        self.build_jobs
            .update_one(
                doc! { "_id": job_id },
                doc! { 
                    "$set": { 
                        "status": "Failed",
                        "failed_at": Utc::now()
                    }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        self.log_build_step(job_id, BuildStep::Build, LogLevel::Error, error_message).await?;

        // Release worker if assigned
        if let Ok(Some(job)) = self.build_jobs.find_one(doc! { "_id": job_id }, None).await {
            if let Some(worker_id) = job.worker_id {
                self.release_worker(&worker_id).await?;
            }
        }

        Ok(())
    }

    #[instrument(skip(self, worker_id))]
    async fn release_worker(&self, worker_id: &str) -> ServiceResult<()> {
        self.build_workers
            .update_one(
                doc! { "worker_id": worker_id },
                doc! { 
                    "$set": { "status": "Available" },
                    "$unset": { "current_job": "" }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        Ok(())
    }

    #[instrument(skip(self, job_id, step, level, message))]
    async fn log_build_step(&self, job_id: ObjectId, step: BuildStep, level: LogLevel, message: &str) -> ServiceResult<()> {
        let log_entry = BuildLogEntry {
            timestamp: Utc::now(),
            level,
            message: message.to_string(),
            step,
            stream: LogStream::System,
        };

        self.build_jobs
            .update_one(
                doc! { "_id": job_id },
                doc! { "$push": { "logs": &log_entry } },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        Ok(())
    }

    async fn clone_repository(&self, job: &BuildJob) -> ServiceResult<()> {
        let start_time = std::time::Instant::now();

        // Rate limit git operations
        self.rate_limiter.check_external_api_rate_limit("git").await?;

        let clone_result = with_circuit_breaker!(
            self.circuit_breaker,
            "git_api",
            {
                self.execute_git_clone(&job.source).await
            }
        );

        let duration = start_time.elapsed();
        self.metrics.record_external_api_call("git", "clone", duration, clone_result.is_ok());

        clone_result
    }

    async fn execute_git_clone(&self, source: &BuildSource) -> ServiceResult<()> {
        use tokio::process::Command;

        let clone_dir = format!("/tmp/builds/{}", uuid::Uuid::new_v4());

        let output = Command::new("git")
            .args(&[
                "clone",
                "--depth", "1",
                "--branch", &source.branch,
                &source.repository_url,
                &clone_dir,
            ])
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("Git clone failed: {}", e)))?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::ExternalApi(format!("Git clone failed: {}", error)));
        }

        info!("Repository cloned successfully to: {}", clone_dir);
        Ok(())
    }

    async fn install_dependencies(&self, job: &BuildJob) -> ServiceResult<()> {
        let start_time = std::time::Instant::now();

        let install_result = match &job.build_config.runtime {
            BuildRuntime::Node { .. } => self.install_node_dependencies(job).await,
            BuildRuntime::Python { .. } => self.install_python_dependencies(job).await,
            BuildRuntime::Rust { .. } => self.install_rust_dependencies(job).await,
            BuildRuntime::Go { .. } => self.install_go_dependencies(job).await,
            BuildRuntime::Docker { .. } => Ok(()), // Dependencies handled in Dockerfile
            BuildRuntime::Static => Ok(()), // No dependencies needed
            _ => Err(ServiceError::Internal("Unsupported runtime".to_string())),
        };

        let duration = start_time.elapsed();
        self.metrics.record_database_operation("install_dependencies", duration, install_result.is_ok());

        install_result
    }

    async fn install_node_dependencies(&self, job: &BuildJob) -> ServiceResult<()> {
        use tokio::process::Command;

        let build_dir = format!("/tmp/builds/{}", job.id.unwrap().to_hex());

        // Check for package.json
        if !tokio::fs::metadata(format!("{}/package.json", build_dir)).await.is_ok() {
            return Ok(()); // No package.json, skip dependency installation
        }

        let output = Command::new("npm")
            .args(&["ci", "--production"])
            .current_dir(&build_dir)
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("npm install failed: {}", e)))?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("npm install failed: {}", error)));
        }

        Ok(())
    }

    async fn install_python_dependencies(&self, job: &BuildJob) -> ServiceResult<()> {
        use tokio::process::Command;

        let build_dir = format!("/tmp/builds/{}", job.id.unwrap().to_hex());

        // Check for requirements.txt
        if !tokio::fs::metadata(format!("{}/requirements.txt", build_dir)).await.is_ok() {
            return Ok(()); // No requirements.txt, skip dependency installation
        }

        let output = Command::new("pip")
            .args(&["install", "-r", "requirements.txt"])
            .current_dir(&build_dir)
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("pip install failed: {}", e)))?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("pip install failed: {}", error)));
        }

        Ok(())
    }

    async fn install_rust_dependencies(&self, job: &BuildJob) -> ServiceResult<()> {
        use tokio::process::Command;

        let build_dir = format!("/tmp/builds/{}", job.id.unwrap().to_hex());

        // Check for Cargo.toml
        if !tokio::fs::metadata(format!("{}/Cargo.toml", build_dir)).await.is_ok() {
            return Ok(()); // No Cargo.toml, skip dependency installation
        }

        let output = Command::new("cargo")
            .args(&["fetch"])
            .current_dir(&build_dir)
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("cargo fetch failed: {}", e)))?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("cargo fetch failed: {}", error)));
        }

        Ok(())
    }

    async fn install_go_dependencies(&self, job: &BuildJob) -> ServiceResult<()> {
        use tokio::process::Command;

        let build_dir = format!("/tmp/builds/{}", job.id.unwrap().to_hex());

        // Check for go.mod
        if !tokio::fs::metadata(format!("{}/go.mod", build_dir)).await.is_ok() {
            return Ok(()); // No go.mod, skip dependency installation
        }

        let output = Command::new("go")
            .args(&["mod", "download"])
            .current_dir(&build_dir)
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("go mod download failed: {}", e)))?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("go mod download failed: {}", error)));
        }

        Ok(())
    }

    async fn build_application(&self, job: &BuildJob) -> ServiceResult<()> {
        let start_time = std::time::Instant::now();

        let build_result = match &job.build_config.runtime {
            BuildRuntime::Node { .. } => self.build_node_application(job).await,
            BuildRuntime::Python { .. } => self.build_python_application(job).await,
            BuildRuntime::Rust { .. } => self.build_rust_application(job).await,
            BuildRuntime::Go { .. } => self.build_go_application(job).await,
            BuildRuntime::Docker { dockerfile } => self.build_docker_application(job, dockerfile).await,
            BuildRuntime::Static => Ok(()), // Static sites don't need building
            _ => Err(ServiceError::Internal("Unsupported runtime".to_string())),
        };

        let duration = start_time.elapsed();
        self.metrics.record_database_operation("build_application", duration, build_result.is_ok());

        build_result
    }

    async fn build_node_application(&self, job: &BuildJob) -> ServiceResult<()> {
        use tokio::process::Command;

        let build_dir = format!("/tmp/builds/{}", job.id.unwrap().to_hex());

        if let Some(build_command) = &job.build_config.build_command {
            let output = Command::new("sh")
                .args(&["-c", build_command])
                .current_dir(&build_dir)
                .output()
                .await
                .map_err(|e| ServiceError::Internal(format!("Build command failed: {}", e)))?;

            if !output.status.success() {
                let error = String::from_utf8_lossy(&output.stderr);
                return Err(ServiceError::Internal(format!("Build failed: {}", error)));
            }
        } else {
            // Default Node.js build
            let output = Command::new("npm")
                .args(&["run", "build"])
                .current_dir(&build_dir)
                .output()
                .await
                .map_err(|e| ServiceError::Internal(format!("npm build failed: {}", e)))?;

            if !output.status.success() {
                let error = String::from_utf8_lossy(&output.stderr);
                return Err(ServiceError::Internal(format!("npm build failed: {}", error)));
            }
        }

        Ok(())
    }

    async fn build_python_application(&self, job: &BuildJob) -> ServiceResult<()> {
        // Python applications typically don't need a build step
        // But we can run tests or other validation here
        if let Some(build_command) = &job.build_config.build_command {
            use tokio::process::Command;

            let build_dir = format!("/tmp/builds/{}", job.id.unwrap().to_hex());

            let output = Command::new("sh")
                .args(&["-c", build_command])
                .current_dir(&build_dir)
                .output()
                .await
                .map_err(|e| ServiceError::Internal(format!("Build command failed: {}", e)))?;

            if !output.status.success() {
                let error = String::from_utf8_lossy(&output.stderr);
                return Err(ServiceError::Internal(format!("Build failed: {}", error)));
            }
        }

        Ok(())
    }

    async fn build_rust_application(&self, job: &BuildJob) -> ServiceResult<()> {
        use tokio::process::Command;

        let build_dir = format!("/tmp/builds/{}", job.id.unwrap().to_hex());

        let output = Command::new("cargo")
            .args(&["build", "--release"])
            .current_dir(&build_dir)
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("cargo build failed: {}", e)))?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("cargo build failed: {}", error)));
        }

        Ok(())
    }

    async fn build_go_application(&self, job: &BuildJob) -> ServiceResult<()> {
        use tokio::process::Command;

        let build_dir = format!("/tmp/builds/{}", job.id.unwrap().to_hex());

        let output = Command::new("go")
            .args(&["build", "-o", "app", "."])
            .current_dir(&build_dir)
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("go build failed: {}", e)))?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("go build failed: {}", error)));
        }

        Ok(())
    }

    async fn build_docker_application(&self, job: &BuildJob, dockerfile: &str) -> ServiceResult<()> {
        use tokio::process::Command;

        let build_dir = format!("/tmp/builds/{}", job.id.unwrap().to_hex());
        let image_tag = format!("app-{}", job.id.unwrap().to_hex());

        let output = Command::new("docker")
            .args(&[
                "build",
                "-f", dockerfile,
                "-t", &image_tag,
                "."
            ])
            .current_dir(&build_dir)
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("Docker build failed: {}", e)))?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("Docker build failed: {}", error)));
        }

        Ok(())
    }

    async fn package_artifacts(&self, job: &BuildJob) -> ServiceResult<Vec<BuildArtifact>> {
        let start_time = std::time::Instant::now();
        let build_dir = format!("/tmp/builds/{}", job.id.unwrap().to_hex());

        let artifacts = match &job.build_config.runtime {
            BuildRuntime::Node { .. } => self.package_node_artifacts(&build_dir).await?,
            BuildRuntime::Python { .. } => self.package_python_artifacts(&build_dir).await?,
            BuildRuntime::Rust { .. } => self.package_rust_artifacts(&build_dir).await?,
            BuildRuntime::Go { .. } => self.package_go_artifacts(&build_dir).await?,
            BuildRuntime::Docker { .. } => self.package_docker_artifacts(&build_dir, job).await?,
            BuildRuntime::Static => self.package_static_artifacts(&build_dir).await?,
            _ => Vec::new(),
        };

        let duration = start_time.elapsed();
        self.metrics.record_database_operation("package_artifacts", duration, true);

        Ok(artifacts)
    }

    async fn package_node_artifacts(&self, build_dir: &str) -> ServiceResult<Vec<BuildArtifact>> {
        let mut artifacts = Vec::new();

        // Check for build output directory
        let dist_dir = format!("{}/dist", build_dir);
        if tokio::fs::metadata(&dist_dir).await.is_ok() {
            let (size, checksum) = self.calculate_directory_stats(&dist_dir).await?;
            artifacts.push(BuildArtifact {
                name: "dist".to_string(),
                path: dist_dir,
                size,
                checksum,
                artifact_type: ArtifactType::StaticFiles,
                created_at: Utc::now(),
            });
        }

        // Check for build directory as fallback
        let build_output_dir = format!("{}/build", build_dir);
        if tokio::fs::metadata(&build_output_dir).await.is_ok() {
            let (size, checksum) = self.calculate_directory_stats(&build_output_dir).await?;
            artifacts.push(BuildArtifact {
                name: "build".to_string(),
                path: build_output_dir,
                size,
                checksum,
                artifact_type: ArtifactType::StaticFiles,
                created_at: Utc::now(),
            });
        }

        Ok(artifacts)
    }

    async fn package_python_artifacts(&self, build_dir: &str) -> ServiceResult<Vec<BuildArtifact>> {
        let mut artifacts = Vec::new();

        // Package Python application
        let (size, checksum) = self.calculate_directory_stats(build_dir).await?;
        artifacts.push(BuildArtifact {
            name: "python_app".to_string(),
            path: build_dir.to_string(),
            size,
            checksum,
            artifact_type: ArtifactType::Archive,
            created_at: Utc::now(),
        });

        Ok(artifacts)
    }

    async fn package_rust_artifacts(&self, build_dir: &str) -> ServiceResult<Vec<BuildArtifact>> {
        let mut artifacts = Vec::new();

        // Check for release binary directory
        let binary_dir = format!("{}/target/release", build_dir);
        if tokio::fs::metadata(&binary_dir).await.is_ok() {
            // Find the actual binary file (not just the directory)
            let mut entries = tokio::fs::read_dir(&binary_dir).await
                .map_err(|e| ServiceError::Internal(format!("Failed to read release directory: {}", e)))?;

            while let Some(entry) = entries.next_entry().await
                .map_err(|e| ServiceError::Internal(format!("Failed to read directory entry: {}", e)))? {

                let path = entry.path();
                if path.is_file() {
                    let metadata = entry.metadata().await
                        .map_err(|e| ServiceError::Internal(format!("Failed to get file metadata: {}", e)))?;

                    // Check if it's an executable (simplified check)
                    if let Some(file_name) = path.file_name() {
                        if let Some(name_str) = file_name.to_str() {
                            if !name_str.contains('.') || name_str.ends_with(".exe") {
                                let (size, checksum) = self.calculate_file_stats(&path).await?;
                                artifacts.push(BuildArtifact {
                                    name: name_str.to_string(),
                                    path: path.to_string_lossy().to_string(),
                                    size,
                                    checksum,
                                    artifact_type: ArtifactType::Binary,
                                    created_at: Utc::now(),
                                });
                            }
                        }
                    }
                }
            }
        }

        Ok(artifacts)
    }

    async fn package_go_artifacts(&self, build_dir: &str) -> ServiceResult<Vec<BuildArtifact>> {
        let mut artifacts = Vec::new();

        // Check for Go binary
        let binary_path = format!("{}/app", build_dir);
        if tokio::fs::metadata(&binary_path).await.is_ok() {
            let (size, checksum) = self.calculate_file_stats(&std::path::Path::new(&binary_path)).await?;
            artifacts.push(BuildArtifact {
                name: "go_binary".to_string(),
                path: binary_path,
                size,
                checksum,
                artifact_type: ArtifactType::Binary,
                created_at: Utc::now(),
            });
        }

        // Also check for other common Go binary names
        let possible_binaries = vec!["main", "server", "api"];
        for binary_name in possible_binaries {
            let binary_path = format!("{}/{}", build_dir, binary_name);
            if tokio::fs::metadata(&binary_path).await.is_ok() {
                let (size, checksum) = self.calculate_file_stats(&std::path::Path::new(&binary_path)).await?;
                artifacts.push(BuildArtifact {
                    name: binary_name.to_string(),
                    path: binary_path,
                    size,
                    checksum,
                    artifact_type: ArtifactType::Binary,
                    created_at: Utc::now(),
                });
            }
        }

        Ok(artifacts)
    }

    async fn package_docker_artifacts(&self, _build_dir: &str, job: &BuildJob) -> ServiceResult<Vec<BuildArtifact>> {
        let mut artifacts = Vec::new();

        let image_tag = format!("app-{}", job.id.unwrap().to_hex());

        // Get Docker image size and digest
        let (size, checksum) = self.get_docker_image_stats(&image_tag).await?;

        artifacts.push(BuildArtifact {
            name: "docker_image".to_string(),
            path: image_tag,
            size,
            checksum,
            artifact_type: ArtifactType::ContainerImage,
            created_at: Utc::now(),
        });

        Ok(artifacts)
    }

    async fn package_static_artifacts(&self, build_dir: &str) -> ServiceResult<Vec<BuildArtifact>> {
        let mut artifacts = Vec::new();

        let (size, checksum) = self.calculate_directory_stats(build_dir).await?;
        artifacts.push(BuildArtifact {
            name: "static_files".to_string(),
            path: build_dir.to_string(),
            size,
            checksum,
            artifact_type: ArtifactType::StaticFiles,
            created_at: Utc::now(),
        });

        Ok(artifacts)
    }

    async fn estimate_build_duration(&self, config: &BuildConfig) -> Option<u32> {
        // Estimate based on runtime and historical data
        let base_duration = match config.runtime {
            BuildRuntime::Node { .. } => 180,      // 3 minutes
            BuildRuntime::Python { .. } => 120,    // 2 minutes
            BuildRuntime::Rust { .. } => 600,      // 10 minutes
            BuildRuntime::Go { .. } => 300,        // 5 minutes
            BuildRuntime::Docker { .. } => 900,    // 15 minutes
            BuildRuntime::Static => 30,            // 30 seconds
            _ => 300,                               // 5 minutes default
        };

        // Add buffer for cache misses and complexity
        Some(base_duration + 60)
    }

    /// Calculate file size and checksum
    async fn calculate_file_stats(&self, file_path: &std::path::Path) -> ServiceResult<(u64, String)> {
        use sha2::{Sha256, Digest};
        use tokio::io::AsyncReadExt;

        let metadata = tokio::fs::metadata(file_path).await
            .map_err(|e| ServiceError::Internal(format!("Failed to get file metadata: {}", e)))?;

        let size = metadata.len();

        // Calculate SHA256 checksum
        let mut file = tokio::fs::File::open(file_path).await
            .map_err(|e| ServiceError::Internal(format!("Failed to open file for checksum: {}", e)))?;

        let mut hasher = Sha256::new();
        let mut buffer = vec![0; 8192]; // 8KB buffer

        loop {
            let bytes_read = file.read(&mut buffer).await
                .map_err(|e| ServiceError::Internal(format!("Failed to read file for checksum: {}", e)))?;

            if bytes_read == 0 {
                break;
            }

            hasher.update(&buffer[..bytes_read]);
        }

        let checksum = format!("{:x}", hasher.finalize());
        Ok((size, checksum))
    }

    /// Calculate directory size and checksum (recursive)
    async fn calculate_directory_stats(&self, dir_path: &str) -> ServiceResult<(u64, String)> {
        use sha2::{Sha256, Digest};
        use std::path::Path;

        let mut total_size = 0u64;
        let mut hasher = Sha256::new();
        let mut file_paths = Vec::new();

        // Collect all files recursively
        self.collect_files_recursive(Path::new(dir_path), &mut file_paths).await?;

        // Sort paths for consistent checksum calculation
        file_paths.sort();

        for file_path in file_paths {
            let metadata = tokio::fs::metadata(&file_path).await
                .map_err(|e| ServiceError::Internal(format!("Failed to get file metadata: {}", e)))?;

            if metadata.is_file() {
                total_size += metadata.len();

                // Add file path to hash for directory integrity
                hasher.update(file_path.to_string_lossy().as_bytes());

                // Add file content to hash
                let mut file = tokio::fs::File::open(&file_path).await
                    .map_err(|e| ServiceError::Internal(format!("Failed to open file: {}", e)))?;

                let mut buffer = vec![0; 8192];
                loop {
                    let bytes_read = file.read(&mut buffer).await
                        .map_err(|e| ServiceError::Internal(format!("Failed to read file: {}", e)))?;

                    if bytes_read == 0 {
                        break;
                    }

                    hasher.update(&buffer[..bytes_read]);
                }
            }
        }

        let checksum = format!("{:x}", hasher.finalize());
        Ok((total_size, checksum))
    }

    /// Recursively collect all file paths in a directory
    async fn collect_files_recursive(&self, dir_path: &Path, file_paths: &mut Vec<std::path::PathBuf>) -> ServiceResult<()> {
        let mut entries = tokio::fs::read_dir(dir_path).await
            .map_err(|e| ServiceError::Internal(format!("Failed to read directory: {}", e)))?;

        while let Some(entry) = entries.next_entry().await
            .map_err(|e| ServiceError::Internal(format!("Failed to read directory entry: {}", e)))? {

            let path = entry.path();

            if path.is_file() {
                file_paths.push(path);
            } else if path.is_dir() {
                // Skip hidden directories and common build artifacts
                if let Some(dir_name) = path.file_name() {
                    if let Some(name_str) = dir_name.to_str() {
                        if !name_str.starts_with('.') &&
                           !name_str.eq("node_modules") &&
                           !name_str.eq("target") &&
                           !name_str.eq("__pycache__") {
                            self.collect_files_recursive(&path, file_paths).await?;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Get Docker image size and digest
    async fn get_docker_image_stats(&self, image_tag: &str) -> ServiceResult<(u64, String)> {
        use tokio::process::Command;

        // Get image size
        let size_output = Command::new("docker")
            .args(&["image", "inspect", image_tag, "--format", "{{.Size}}"])
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("Failed to get Docker image size: {}", e)))?;

        let size = if size_output.status.success() {
            let size_str = String::from_utf8_lossy(&size_output.stdout).trim().to_string();
            size_str.parse::<u64>().unwrap_or(0)
        } else {
            0
        };

        // Get image digest/ID
        let digest_output = Command::new("docker")
            .args(&["image", "inspect", image_tag, "--format", "{{.Id}}"])
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("Failed to get Docker image digest: {}", e)))?;

        let digest = if digest_output.status.success() {
            String::from_utf8_lossy(&digest_output.stdout).trim().to_string()
        } else {
            "unknown".to_string()
        };

        Ok((size, digest))
    }
}

impl<'a> CircuitBreakerAware for BuildService<'a> {
    fn circuit_breaker(&self) -> &CircuitBreakerService {
        &self.circuit_breaker
    }
}
