use crate::{
    config::Config,
    database::Database,
    models::{
        BuildJob, BuildStatus, BuildConfig, BuildSource, BuildArtifact, BuildLogEntry,
        BuildMetrics, BuildWorker, WorkerStatus, BuildQueue, BuildPriority,
        CreateBuildJobRequest, BuildJobResponse, BuildLogsResponse, BuildStep,
        LogLevel, LogStream, ArtifactType, BuildRuntime
    },
    services::{ServiceError, ServiceResult},
    vultr::VultrClient,
};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use futures::TryStreamExt;
use mongodb::Collection;
use std::collections::HashMap;
use std::process::Stdio;
use tokio::process::Command;
use tokio::io::{AsyncBufReadExt, BufReader};
use tracing::{error, info, instrument, warn};
use uuid::Uuid;

pub struct BuildService<'a> {
    build_jobs: Collection<BuildJob>,
    build_workers: Collection<BuildWorker>,
    build_queue: Collection<BuildQueue>,
    vultr_client: &'a VultrClient,
    config: &'a Config,
}

impl<'a> BuildService<'a> {
    pub fn new(database: &Database, vultr_client: &'a VultrClient, config: &'a Config) -> Self {
        Self {
            build_jobs: database.collection("build_jobs"),
            build_workers: database.collection("build_workers"),
            build_queue: database.collection("build_queue"),
            vultr_client,
            config,
        }
    }

    #[instrument(skip(self, request))]
    pub async fn create_build_job(&self, request: CreateBuildJobRequest) -> ServiceResult<BuildJobResponse> {
        let application_id = ObjectId::parse_str(&request.application_id)
            .map_err(|_| ServiceError::Validation("Invalid application ID".to_string()))?;
        
        let deployment_id = ObjectId::parse_str(&request.deployment_id)
            .map_err(|_| ServiceError::Validation("Invalid deployment ID".to_string()))?;

        let build_job = BuildJob {
            id: None,
            application_id,
            deployment_id,
            status: BuildStatus::Queued,
            build_config: request.build_config.clone(),
            source: request.source.clone(),
            artifacts: Vec::new(),
            logs: Vec::new(),
            metrics: BuildMetrics {
                total_duration: None,
                clone_duration: None,
                install_duration: None,
                build_duration: None,
                test_duration: None,
                package_duration: None,
                cache_hit_rate: 0.0,
                artifact_size: 0,
                peak_memory_usage: 0,
                cpu_time: 0,
            },
            created_at: Utc::now(),
            started_at: None,
            completed_at: None,
            failed_at: None,
            worker_id: None,
        };

        let result = self.build_jobs
            .insert_one(&build_job, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        let job_id = result.inserted_id.as_object_id()
            .ok_or_else(|| ServiceError::Internal("Failed to get inserted job ID".to_string()))?;

        // Add to build queue
        let queue_entry = BuildQueue {
            id: None,
            priority: request.priority,
            job_id,
            estimated_duration: self.estimate_build_duration(&request.build_config).await,
            queued_at: Utc::now(),
            assigned_worker: None,
            assigned_at: None,
        };

        self.build_queue
            .insert_one(&queue_entry, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Build job created and queued: {}", job_id);

        // Try to assign to available worker
        self.try_assign_worker(job_id).await?;

        Ok(BuildJobResponse {
            id: job_id.to_hex(),
            application_id: request.application_id,
            deployment_id: request.deployment_id,
            status: BuildStatus::Queued,
            build_config: request.build_config,
            source: request.source,
            artifacts: Vec::new(),
            metrics: build_job.metrics,
            created_at: build_job.created_at,
            started_at: None,
            completed_at: None,
            failed_at: None,
        })
    }

    #[instrument(skip(self, job_id))]
    pub async fn get_build_job(&self, job_id: &str) -> ServiceResult<BuildJobResponse> {
        let object_id = ObjectId::parse_str(job_id)
            .map_err(|_| ServiceError::Validation("Invalid job ID".to_string()))?;

        let job = self.build_jobs
            .find_one(doc! { "_id": object_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .ok_or_else(|| ServiceError::NotFound("Build job not found".to_string()))?;

        Ok(BuildJobResponse {
            id: job.id.unwrap().to_hex(),
            application_id: job.application_id.to_hex(),
            deployment_id: job.deployment_id.to_hex(),
            status: job.status,
            build_config: job.build_config,
            source: job.source,
            artifacts: job.artifacts,
            metrics: job.metrics,
            created_at: job.created_at,
            started_at: job.started_at,
            completed_at: job.completed_at,
            failed_at: job.failed_at,
        })
    }

    #[instrument(skip(self, job_id))]
    pub async fn get_build_logs(&self, job_id: &str, cursor: Option<String>, limit: Option<u32>) -> ServiceResult<BuildLogsResponse> {
        let object_id = ObjectId::parse_str(job_id)
            .map_err(|_| ServiceError::Validation("Invalid job ID".to_string()))?;

        let job = self.build_jobs
            .find_one(doc! { "_id": object_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .ok_or_else(|| ServiceError::NotFound("Build job not found".to_string()))?;

        let limit = limit.unwrap_or(100) as usize;
        let logs = if let Some(_cursor) = cursor {
            // Implement cursor-based pagination
            job.logs.into_iter().take(limit).collect()
        } else {
            job.logs.into_iter().take(limit).collect()
        };

        Ok(BuildLogsResponse {
            logs,
            has_more: false, // Implement proper pagination
            next_cursor: None,
        })
    }

    #[instrument(skip(self, job_id))]
    async fn try_assign_worker(&self, job_id: ObjectId) -> ServiceResult<()> {
        // Find available worker
        let available_worker = self.build_workers
            .find_one(doc! { 
                "status": "Available",
                "current_job": { "$exists": false }
            }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        if let Some(worker) = available_worker {
            // Assign job to worker
            self.build_queue
                .update_one(
                    doc! { "job_id": job_id },
                    doc! { 
                        "$set": { 
                            "assigned_worker": &worker.worker_id,
                            "assigned_at": Utc::now()
                        }
                    },
                    None,
                )
                .await
                .map_err(|e| ServiceError::Database(e))?;

            self.build_workers
                .update_one(
                    doc! { "_id": worker.id },
                    doc! { 
                        "$set": { 
                            "status": "Busy",
                            "current_job": job_id
                        }
                    },
                    None,
                )
                .await
                .map_err(|e| ServiceError::Database(e))?;

            info!("Assigned build job {} to worker {}", job_id, worker.worker_id);

            // Start the build process
            self.start_build_execution(job_id, &worker.worker_id).await?;
        }

        Ok(())
    }

    #[instrument(skip(self, job_id, worker_id))]
    async fn start_build_execution(&self, job_id: ObjectId, worker_id: &str) -> ServiceResult<()> {
        // Update job status to running
        self.build_jobs
            .update_one(
                doc! { "_id": job_id },
                doc! { 
                    "$set": { 
                        "status": "Running",
                        "started_at": Utc::now(),
                        "worker_id": worker_id
                    }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        // Spawn build task
        let build_service = self.clone(); // We'd need to implement Clone or use Arc
        tokio::spawn(async move {
            if let Err(e) = build_service.execute_build(job_id).await {
                error!("Build execution failed for job {}: {}", job_id, e);
                // Update job status to failed
                let _ = build_service.mark_build_failed(job_id, &e.to_string()).await;
            }
        });

        Ok(())
    }

    #[instrument(skip(self, job_id))]
    async fn execute_build(&self, job_id: ObjectId) -> ServiceResult<()> {
        let job = self.build_jobs
            .find_one(doc! { "_id": job_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .ok_or_else(|| ServiceError::NotFound("Build job not found".to_string()))?;

        let build_start = std::time::Instant::now();

        // Step 1: Clone repository
        self.log_build_step(job_id, BuildStep::Clone, LogLevel::Info, "Cloning repository...").await?;
        let clone_start = std::time::Instant::now();
        self.clone_repository(&job).await?;
        let clone_duration = clone_start.elapsed().as_secs();

        // Step 2: Install dependencies
        self.log_build_step(job_id, BuildStep::InstallDependencies, LogLevel::Info, "Installing dependencies...").await?;
        let install_start = std::time::Instant::now();
        self.install_dependencies(&job).await?;
        let install_duration = install_start.elapsed().as_secs();

        // Step 3: Build application
        self.log_build_step(job_id, BuildStep::Build, LogLevel::Info, "Building application...").await?;
        let build_start_time = std::time::Instant::now();
        self.build_application(&job).await?;
        let build_duration = build_start_time.elapsed().as_secs();

        // Step 4: Package artifacts
        self.log_build_step(job_id, BuildStep::Package, LogLevel::Info, "Packaging artifacts...").await?;
        let package_start = std::time::Instant::now();
        let artifacts = self.package_artifacts(&job).await?;
        let package_duration = package_start.elapsed().as_secs();

        let total_duration = build_start.elapsed().as_secs();

        // Update job with completion
        self.build_jobs
            .update_one(
                doc! { "_id": job_id },
                doc! { 
                    "$set": { 
                        "status": "Success",
                        "completed_at": Utc::now(),
                        "artifacts": &artifacts,
                        "metrics.total_duration": total_duration as i64,
                        "metrics.clone_duration": clone_duration as i64,
                        "metrics.install_duration": install_duration as i64,
                        "metrics.build_duration": build_duration as i64,
                        "metrics.package_duration": package_duration as i64,
                    }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        // Release worker
        self.release_worker(&job.worker_id.unwrap_or_default()).await?;

        info!("Build completed successfully for job {}", job_id);
        Ok(())
    }

    #[instrument(skip(self, job_id, error_message))]
    async fn mark_build_failed(&self, job_id: ObjectId, error_message: &str) -> ServiceResult<()> {
        self.build_jobs
            .update_one(
                doc! { "_id": job_id },
                doc! { 
                    "$set": { 
                        "status": "Failed",
                        "failed_at": Utc::now()
                    }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        self.log_build_step(job_id, BuildStep::Build, LogLevel::Error, error_message).await?;

        // Release worker if assigned
        if let Ok(Some(job)) = self.build_jobs.find_one(doc! { "_id": job_id }, None).await {
            if let Some(worker_id) = job.worker_id {
                self.release_worker(&worker_id).await?;
            }
        }

        Ok(())
    }

    #[instrument(skip(self, worker_id))]
    async fn release_worker(&self, worker_id: &str) -> ServiceResult<()> {
        self.build_workers
            .update_one(
                doc! { "worker_id": worker_id },
                doc! { 
                    "$set": { "status": "Available" },
                    "$unset": { "current_job": "" }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        Ok(())
    }

    #[instrument(skip(self, job_id, step, level, message))]
    async fn log_build_step(&self, job_id: ObjectId, step: BuildStep, level: LogLevel, message: &str) -> ServiceResult<()> {
        let log_entry = BuildLogEntry {
            timestamp: Utc::now(),
            level,
            message: message.to_string(),
            step,
            stream: LogStream::System,
        };

        self.build_jobs
            .update_one(
                doc! { "_id": job_id },
                doc! { "$push": { "logs": &log_entry } },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        Ok(())
    }

    async fn clone_repository(&self, _job: &BuildJob) -> ServiceResult<()> {
        // Implementation for cloning repository
        // This would use git commands or git2 library
        Ok(())
    }

    async fn install_dependencies(&self, _job: &BuildJob) -> ServiceResult<()> {
        // Implementation for installing dependencies based on runtime
        // This would detect package.json, requirements.txt, etc.
        Ok(())
    }

    async fn build_application(&self, _job: &BuildJob) -> ServiceResult<()> {
        // Implementation for building application based on runtime
        // This would run npm build, cargo build, etc.
        Ok(())
    }

    async fn package_artifacts(&self, _job: &BuildJob) -> ServiceResult<Vec<BuildArtifact>> {
        // Implementation for packaging build artifacts
        // This would create container images, zip files, etc.
        Ok(Vec::new())
    }

    async fn estimate_build_duration(&self, _config: &BuildConfig) -> Option<u32> {
        // Estimate build duration based on historical data and config
        Some(300) // 5 minutes default
    }
}
