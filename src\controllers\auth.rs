use crate::{
    controllers::{success_response, <PERSON><PERSON><PERSON><PERSON>, ControllerR<PERSON>ult},
    models::{CreateUserRequest, LoginRequest, LoginResponse, UserProfile},
    services::auth::AuthService,
    AppState,
};
use axum::{extract::State, <PERSON>son};
use std::sync::Arc;
use tracing::instrument;
use validator::Validate;

#[instrument(skip(state, request))]
pub async fn register(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateUserRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<UserProfile>>> {
    // Inline validation checks
    let email = request.email.as_ref().ok_or_else(|| {
        ControllerError::Validation("email is required".to_string())
    })?;

    let password = request.password.as_ref().ok_or_else(|| {
        ControllerError::Validation("password is required".to_string())
    })?;

    let first_name = request.first_name.as_ref().ok_or_else(|| {
        ControllerError::Validation("first_name is required".to_string())
    })?;

    let last_name = request.last_name.as_ref().ok_or_else(|| {
        ControllerError::Validation("last_name is required".to_string())
    })?;

    if email.is_empty() {
        return Err(ControllerError::Validation("email is required".to_string()));
    }

    if password.is_empty() {
        return Err(ControllerError::Validation("password is required".to_string()));
    }

    if first_name.is_empty() {
        return Err(ControllerError::Validation("first_name is required".to_string()));
    }

    if last_name.is_empty() {
        return Err(ControllerError::Validation("last_name is required".to_string()));
    }

    if !email.contains('@') {
        return Err(ControllerError::Validation("email must be a valid email address".to_string()));
    }

    if password.len() < 8 {
        return Err(ControllerError::Validation("password must be at least 8 characters long".to_string()));
    }

    // Create validated request object
    let validated_request = CreateUserRequest {
        email: Some(email.clone()),
        password: Some(password.clone()),
        first_name: Some(first_name.clone()),
        last_name: Some(last_name.clone()),
        company: request.company.clone(),
    };

    let auth_service = AuthService::new(&state.database, &state.config);
    let user_profile = auth_service.register(validated_request).await?;

    Ok(success_response(user_profile))
}

#[instrument(skip(state, request))]
pub async fn login(
    State(state): State<Arc<AppState>>,
    Json(request): Json<LoginRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<LoginResponse>>> {
    // Inline validation checks
    let email = request.email.as_ref().ok_or_else(|| {
        ControllerError::Validation("email is required".to_string())
    })?;

    let password = request.password.as_ref().ok_or_else(|| {
        ControllerError::Validation("password is required".to_string())
    })?;

    if email.is_empty() {
        return Err(ControllerError::Validation("email is required".to_string()));
    }

    if password.is_empty() {
        return Err(ControllerError::Validation("password is required".to_string()));
    }

    if !email.contains('@') {
        return Err(ControllerError::Validation("email must be a valid email address".to_string()));
    }

    // Create validated request object
    let validated_request = LoginRequest {
        email: Some(email.clone()),
        password: Some(password.clone()),
    };

    let auth_service = AuthService::new(&state.database, &state.config);
    let login_response = auth_service.login(validated_request).await?;

    Ok(success_response(login_response))
}

#[instrument(skip(state))]
pub async fn refresh_token(
    State(state): State<Arc<AppState>>,
    // TODO: Extract refresh token from request
) -> ControllerResult<Json<crate::models::ApiResponse<LoginResponse>>> {
    // TODO: Implement refresh token logic
    Err(ControllerError::Internal("Not implemented".to_string()))
}
