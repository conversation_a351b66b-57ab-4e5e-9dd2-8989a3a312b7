use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildJob {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub application_id: ObjectId,
    pub deployment_id: ObjectId,
    pub status: BuildStatus,
    pub build_config: BuildConfig,
    pub source: BuildSource,
    pub artifacts: Vec<BuildArtifact>,
    pub logs: Vec<BuildLogEntry>,
    pub metrics: BuildMetrics,
    pub created_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub failed_at: Option<DateTime<Utc>>,
    pub worker_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BuildStatus {
    Queued,
    Running,
    Success,
    Failed,
    Cancelled,
    Timeout,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildConfig {
    pub runtime: BuildRuntime,
    pub build_command: Option<String>,
    pub install_command: Option<String>,
    pub dockerfile_path: Option<String>,
    pub root_directory: String,
    pub environment_variables: HashMap<String, String>,
    pub cache_enabled: bool,
    pub timeout: u32, // seconds
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BuildRuntime {
    Node { version: String },
    Python { version: String },
    Ruby { version: String },
    Go { version: String },
    Rust { version: String },
    PHP { version: String },
    Java { version: String },
    Docker { dockerfile: String },
    Static,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildSource {
    pub repository_url: String,
    pub branch: String,
    pub commit_sha: String,
    pub commit_message: String,
    pub author: String,
    pub tarball_url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildArtifact {
    pub name: String,
    pub path: String,
    pub size: u64,
    pub checksum: String,
    pub artifact_type: ArtifactType,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ArtifactType {
    ContainerImage,
    StaticFiles,
    Binary,
    Archive,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildLogEntry {
    pub timestamp: DateTime<Utc>,
    pub level: LogLevel,
    pub message: String,
    pub step: BuildStep,
    pub stream: LogStream,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogLevel {
    Debug,
    Info,
    Warn,
    Error,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BuildStep {
    Initialize,
    Clone,
    InstallDependencies,
    Build,
    Test,
    Package,
    Upload,
    Cleanup,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogStream {
    Stdout,
    Stderr,
    System,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildMetrics {
    pub total_duration: Option<u64>,     // seconds
    pub clone_duration: Option<u64>,     // seconds
    pub install_duration: Option<u64>,   // seconds
    pub build_duration: Option<u64>,     // seconds
    pub test_duration: Option<u64>,      // seconds
    pub package_duration: Option<u64>,   // seconds
    pub cache_hit_rate: f32,
    pub artifact_size: u64,              // bytes
    pub peak_memory_usage: u64,          // bytes
    pub cpu_time: u64,                   // milliseconds
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildWorker {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub worker_id: String,
    pub status: WorkerStatus,
    pub capabilities: WorkerCapabilities,
    pub current_job: Option<ObjectId>,
    pub region: String,
    pub instance_id: String,
    pub last_heartbeat: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub jobs_completed: u64,
    pub jobs_failed: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WorkerStatus {
    Available,
    Busy,
    Offline,
    Maintenance,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkerCapabilities {
    pub max_concurrent_jobs: u32,
    pub supported_runtimes: Vec<BuildRuntime>,
    pub docker_enabled: bool,
    pub max_build_time: u32, // seconds
    pub max_memory: u64,     // bytes
    pub max_disk: u64,       // bytes
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildQueue {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub priority: BuildPriority,
    pub job_id: ObjectId,
    pub estimated_duration: Option<u32>, // seconds
    pub queued_at: DateTime<Utc>,
    pub assigned_worker: Option<String>,
    pub assigned_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BuildPriority {
    Low,
    Normal,
    High,
    Critical,
}

// Request/Response DTOs
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct CreateBuildJobRequest {
    pub application_id: String,
    pub deployment_id: String,
    pub build_config: BuildConfig,
    pub source: BuildSource,
    pub priority: BuildPriority,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildJobResponse {
    pub id: String,
    pub application_id: String,
    pub deployment_id: String,
    pub status: BuildStatus,
    pub build_config: BuildConfig,
    pub source: BuildSource,
    pub artifacts: Vec<BuildArtifact>,
    pub metrics: BuildMetrics,
    pub created_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub failed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildLogsResponse {
    pub logs: Vec<BuildLogEntry>,
    pub has_more: bool,
    pub next_cursor: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildMetricsResponse {
    pub job_id: String,
    pub metrics: BuildMetrics,
    pub real_time_stats: Option<RealTimeBuildStats>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RealTimeBuildStats {
    pub current_step: BuildStep,
    pub progress_percent: f32,
    pub estimated_remaining: Option<u32>, // seconds
    pub current_memory_usage: u64,        // bytes
    pub current_cpu_usage: f32,           // percentage
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildCacheEntry {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub application_id: ObjectId,
    pub cache_key: String,
    pub cache_type: CacheType,
    pub size: u64,
    pub created_at: DateTime<Utc>,
    pub last_accessed: DateTime<Utc>,
    pub access_count: u64,
    pub expires_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CacheType {
    Dependencies,
    BuildArtifacts,
    DockerLayers,
    TestResults,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildTemplate {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub name: String,
    pub description: String,
    pub runtime: BuildRuntime,
    pub default_build_config: BuildConfig,
    pub example_files: HashMap<String, String>,
    pub documentation_url: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub usage_count: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildHook {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub application_id: ObjectId,
    pub hook_type: HookType,
    pub trigger: HookTrigger,
    pub script: String,
    pub timeout: u32, // seconds
    pub enabled: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HookType {
    PreBuild,
    PostBuild,
    PreDeploy,
    PostDeploy,
    OnFailure,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HookTrigger {
    Always,
    OnSuccess,
    OnFailure,
    OnBranch { branches: Vec<String> },
    OnTag { pattern: String },
}

// Build system configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildSystemConfig {
    pub max_concurrent_builds: u32,
    pub default_timeout: u32,        // seconds
    pub max_timeout: u32,            // seconds
    pub cache_retention_days: u32,
    pub log_retention_days: u32,
    pub artifact_retention_days: u32,
    pub worker_heartbeat_interval: u32, // seconds
    pub worker_timeout: u32,         // seconds
    pub supported_runtimes: Vec<BuildRuntime>,
}
