use crate::{
    controllers::{success_response, <PERSON><PERSON><PERSON><PERSON>, ControllerR<PERSON><PERSON>},
    middleware::auth::get_current_user,
    models::{CreateInstanceRequest, InstanceResponse, PaginatedResponse, PaginationQuery},
    services::instance::InstanceService,
    AppState,
};
use axum::{
    extract::{Path, Query, Request, State},
    Json,
    body::Body,
};
use std::sync::Arc;
use tracing::instrument;
use validator::Validate;

#[instrument(skip(state, req))]
pub async fn list_instances(
    State(state): State<Arc<AppState>>,
    Query(pagination): Query<PaginationQuery>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<PaginatedResponse<InstanceResponse>>>> {
    let claims = get_current_user(&req)?;
    
    pagination.validate().map_err(|e| {
        ControllerError::Validation(format!("Validation failed: {}", e))
    })?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    let instances = instance_service
        .list_user_instances(&claims.sub, pagination)
        .await?;

    Ok(success_response(instances))
}

#[instrument(skip(state, req))]
pub async fn get_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceResponse>>> {
    let claims = get_current_user(&req)?;

    // Inline validation checks
    if instance_id.is_empty() {
        return Err(ControllerError::Validation("MISSING_INSTANCE_ID".to_string()));
    }

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    let instance = instance_service
        .get_user_instance(&claims.sub, &instance_id)
        .await?;

    Ok(success_response(instance))
}

#[instrument(skip(state, req, request))]
pub async fn create_instance(
    State(state): State<Arc<AppState>>,
    req: Request<Body>,
    Json(request): Json<CreateInstanceRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceResponse>>> {
    let claims = get_current_user(&req)?;

    // Inline validation checks
    let name = request.name.as_ref().ok_or_else(|| {
        ControllerError::Validation("MISSING_NAME".to_string())
    })?;

    let region = request.region.as_ref().ok_or_else(|| {
        ControllerError::Validation("MISSING_REGION".to_string())
    })?;

    let plan = request.plan.as_ref().ok_or_else(|| {
        ControllerError::Validation("MISSING_PLAN".to_string())
    })?;

    let os = request.os.as_ref().ok_or_else(|| {
        ControllerError::Validation("MISSING_OS".to_string())
    })?;

    if name.is_empty() {
        return Err(ControllerError::Validation("MISSING_NAME".to_string()));
    }

    if region.is_empty() {
        return Err(ControllerError::Validation("MISSING_REGION".to_string()));
    }

    if plan.is_empty() {
        return Err(ControllerError::Validation("MISSING_PLAN".to_string()));
    }

    if os.is_empty() {
        return Err(ControllerError::Validation("MISSING_OS".to_string()));
    }

    // Create validated request object
    let validated_request = CreateInstanceRequest {
        name: Some(name.clone()),
        region: Some(region.clone()),
        plan: Some(plan.clone()),
        os: Some(os.clone()),
        ssh_key_ids: request.ssh_key_ids.clone(),
        startup_script_id: request.startup_script_id.clone(),
        tags: request.tags.clone(),
    };

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    let instance = instance_service
        .create_instance(&claims.sub, validated_request)
        .await?;

    Ok(success_response(instance))
}

#[instrument(skip(state, req))]
pub async fn update_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request<Body>,
    Json(request): Json<serde_json::Value>,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceResponse>>> {
    let claims = get_current_user(&req)?;

    // Inline validation checks
    if instance_id.is_empty() {
        return Err(ControllerError::Validation("MISSING_INSTANCE_ID".to_string()));
    }

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    let instance = instance_service
        .update_instance(&claims.sub, &instance_id, request)
        .await?;

    Ok(success_response(instance))
}

#[instrument(skip(state, req))]
pub async fn delete_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    let claims = get_current_user(&req)?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    instance_service
        .delete_instance(&claims.sub, &instance_id)
        .await?;

    Ok(success_response(()))
}

#[instrument(skip(state, req))]
pub async fn start_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    let claims = get_current_user(&req)?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    instance_service
        .start_instance(&claims.sub, &instance_id)
        .await?;

    Ok(success_response(()))
}

#[instrument(skip(state, req))]
pub async fn stop_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    let claims = get_current_user(&req)?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    instance_service
        .stop_instance(&claims.sub, &instance_id)
        .await?;

    Ok(success_response(()))
}

#[instrument(skip(state, req))]
pub async fn restart_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    let claims = get_current_user(&req)?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    instance_service
        .restart_instance(&claims.sub, &instance_id)
        .await?;

    Ok(success_response(()))
}
