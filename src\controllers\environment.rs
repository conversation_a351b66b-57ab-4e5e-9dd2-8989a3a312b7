use crate::{
    controllers::{<PERSON><PERSON><PERSON><PERSON>, ControllerResult},
    models::{ApiResponse, EnvironmentGroup},
    services::EnvironmentService,
    state::AppState,
    utils::auth::Claims,
};
use axum::{
    extract::{Path, State},
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::instrument;
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct CreateEnvironmentGroupRequest {
    #[validate(length(min = 1, max = 100))]
    pub name: String,
    pub description: Option<String>,
    pub variables: HashMap<String, String>,
    pub secrets: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct SetEnvironmentVariableRequest {
    #[validate(length(min = 1, max = 100))]
    pub key: String,
    #[validate(length(min = 1))]
    pub value: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Validate)]
pub struct SetSecretRequest {
    #[validate(length(min = 1, max = 100))]
    pub key: String,
    #[validate(length(min = 1))]
    pub value: String,
    pub description: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentGroupResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub variables: HashMap<String, String>, // Values hidden for security
    pub secrets: Vec<String>,               // Only keys, values hidden
    pub linked_applications: Vec<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct LinkApplicationRequest {
    #[validate(length(min = 1))]
    pub application_id: String,
}

#[instrument(skip(state, claims, request))]
pub async fn create_environment_group(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Json(request): Json<CreateEnvironmentGroupRequest>,
) -> ControllerResult<Json<ApiResponse<EnvironmentGroupResponse>>> {
    // Inline validation checks
    request.validate().map_err(|e| {
        ControllerError::Validation(format!("environment group data is invalid: {}", e))
    })?;

    if request.name.is_empty() {
        return Err(ControllerError::Validation("name is required".to_string()));
    }

    if request.name.len() > 100 {
        return Err(ControllerError::Validation("name must be less than 100 characters".to_string()));
    }

    // Validate environment variable keys
    for key in request.variables.keys() {
        if key.is_empty() {
            return Err(ControllerError::Validation("environment variable key cannot be empty".to_string()));
        }
        if !key.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(ControllerError::Validation("environment variable key must contain only alphanumeric characters and underscores".to_string()));
        }
    }

    // Validate secret keys
    for key in request.secrets.keys() {
        if key.is_empty() {
            return Err(ControllerError::Validation("secret key cannot be empty".to_string()));
        }
        if !key.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(ControllerError::Validation("secret key must contain only alphanumeric characters and underscores".to_string()));
        }
    }

    let environment_service = EnvironmentService::new(&state.database, &state.config);
    
    let group = environment_service
        .create_environment_group(
            &claims.sub,
            request.name,
            request.description,
            request.variables,
            request.secrets,
        )
        .await
        .map_err(|e| ControllerError::Service(e.to_string()))?;

    let response = EnvironmentGroupResponse {
        id: group.id.unwrap().to_hex(),
        name: group.name,
        description: group.description,
        variables: group.variables.iter().map(|(k, _)| (k.clone(), "***".to_string())).collect(),
        secrets: group.secrets.keys().cloned().collect(),
        linked_applications: group.linked_applications.iter().map(|id| id.to_hex()).collect(),
        created_at: group.created_at,
        updated_at: group.updated_at,
    };

    Ok(Json(ApiResponse::success(response)))
}

#[instrument(skip(state, claims))]
pub async fn list_environment_groups(
    State(state): State<Arc<AppState>>,
    claims: Claims,
) -> ControllerResult<Json<ApiResponse<Vec<EnvironmentGroupResponse>>>> {
    let environment_service = EnvironmentService::new(&state.database, &state.config);
    
    let groups = environment_service
        .list_environment_groups(&claims.sub)
        .await
        .map_err(|e| ControllerError::Service(e.to_string()))?;

    let response: Vec<EnvironmentGroupResponse> = groups
        .into_iter()
        .map(|group| EnvironmentGroupResponse {
            id: group.id.unwrap().to_hex(),
            name: group.name,
            description: group.description,
            variables: group.variables.iter().map(|(k, _)| (k.clone(), "***".to_string())).collect(),
            secrets: group.secrets.keys().cloned().collect(),
            linked_applications: group.linked_applications.iter().map(|id| id.to_hex()).collect(),
            created_at: group.created_at,
            updated_at: group.updated_at,
        })
        .collect();

    Ok(Json(ApiResponse::success(response)))
}

#[instrument(skip(state, claims, group_id))]
pub async fn get_environment_group(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(group_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<EnvironmentGroupResponse>>> {
    // Inline validation checks
    if group_id.is_empty() {
        return Err(ControllerError::Validation("group_id is required".to_string()));
    }

    let environment_service = EnvironmentService::new(&state.database, &state.config);
    
    let group = environment_service
        .get_environment_group(&claims.sub, &group_id)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Environment group not found".to_string()),
            _ => ControllerError::Service(e.to_string()),
        })?;

    let response = EnvironmentGroupResponse {
        id: group.id.unwrap().to_hex(),
        name: group.name,
        description: group.description,
        variables: group.variables.iter().map(|(k, _)| (k.clone(), "***".to_string())).collect(),
        secrets: group.secrets.keys().cloned().collect(),
        linked_applications: group.linked_applications.iter().map(|id| id.to_hex()).collect(),
        created_at: group.created_at,
        updated_at: group.updated_at,
    };

    Ok(Json(ApiResponse::success(response)))
}

#[instrument(skip(state, claims, group_id, request))]
pub async fn set_environment_variable(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(group_id): Path<String>,
    Json(request): Json<SetEnvironmentVariableRequest>,
) -> ControllerResult<Json<ApiResponse<()>>> {
    // Inline validation checks
    if group_id.is_empty() {
        return Err(ControllerError::Validation("group_id is required".to_string()));
    }

    request.validate().map_err(|e| {
        ControllerError::Validation(format!("environment variable data is invalid: {}", e))
    })?;

    if request.key.is_empty() {
        return Err(ControllerError::Validation("key is required".to_string()));
    }

    if request.value.is_empty() {
        return Err(ControllerError::Validation("value is required".to_string()));
    }

    if !request.key.chars().all(|c| c.is_alphanumeric() || c == '_') {
        return Err(ControllerError::Validation("key must contain only alphanumeric characters and underscores".to_string()));
    }

    let environment_service = EnvironmentService::new(&state.database, &state.config);
    
    environment_service
        .set_environment_variable(&claims.sub, &group_id, request.key, request.value)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Environment group not found".to_string()),
            _ => ControllerError::Service(e.to_string()),
        })?;

    Ok(Json(ApiResponse::success(())))
}

#[instrument(skip(state, claims, group_id, request))]
pub async fn set_secret(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(group_id): Path<String>,
    Json(request): Json<SetSecretRequest>,
) -> ControllerResult<Json<ApiResponse<()>>> {
    // Inline validation checks
    if group_id.is_empty() {
        return Err(ControllerError::Validation("group_id is required".to_string()));
    }

    request.validate().map_err(|e| {
        ControllerError::Validation(format!("secret data is invalid: {}", e))
    })?;

    if request.key.is_empty() {
        return Err(ControllerError::Validation("key is required".to_string()));
    }

    if request.value.is_empty() {
        return Err(ControllerError::Validation("value is required".to_string()));
    }

    if !request.key.chars().all(|c| c.is_alphanumeric() || c == '_') {
        return Err(ControllerError::Validation("key must contain only alphanumeric characters and underscores".to_string()));
    }

    let environment_service = EnvironmentService::new(&state.database, &state.config);
    
    environment_service
        .set_secret(&claims.sub, &group_id, request.key, request.value, request.description)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Environment group not found".to_string()),
            _ => ControllerError::Service(e.to_string()),
        })?;

    Ok(Json(ApiResponse::success(())))
}

#[instrument(skip(state, claims, group_id, key))]
pub async fn delete_environment_variable(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path((group_id, key)): Path<(String, String)>,
) -> ControllerResult<Json<ApiResponse<()>>> {
    // Inline validation checks
    if group_id.is_empty() {
        return Err(ControllerError::Validation("group_id is required".to_string()));
    }

    if key.is_empty() {
        return Err(ControllerError::Validation("key is required".to_string()));
    }

    let environment_service = EnvironmentService::new(&state.database, &state.config);
    
    environment_service
        .delete_environment_variable(&claims.sub, &group_id, &key)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Environment group not found".to_string()),
            _ => ControllerError::Service(e.to_string()),
        })?;

    Ok(Json(ApiResponse::success(())))
}

#[instrument(skip(state, claims, group_id, key))]
pub async fn delete_secret(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path((group_id, key)): Path<(String, String)>,
) -> ControllerResult<Json<ApiResponse<()>>> {
    // Inline validation checks
    if group_id.is_empty() {
        return Err(ControllerError::Validation("group_id is required".to_string()));
    }

    if key.is_empty() {
        return Err(ControllerError::Validation("key is required".to_string()));
    }

    let environment_service = EnvironmentService::new(&state.database, &state.config);
    
    environment_service
        .delete_secret(&claims.sub, &group_id, &key)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Environment group not found".to_string()),
            _ => ControllerError::Service(e.to_string()),
        })?;

    Ok(Json(ApiResponse::success(())))
}

#[instrument(skip(state, claims, group_id, request))]
pub async fn link_application(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(group_id): Path<String>,
    Json(request): Json<LinkApplicationRequest>,
) -> ControllerResult<Json<ApiResponse<()>>> {
    // Inline validation checks
    if group_id.is_empty() {
        return Err(ControllerError::Validation("group_id is required".to_string()));
    }

    request.validate().map_err(|e| {
        ControllerError::Validation(format!("link application data is invalid: {}", e))
    })?;

    if request.application_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    let environment_service = EnvironmentService::new(&state.database, &state.config);
    
    environment_service
        .link_application(&claims.sub, &group_id, &request.application_id)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Environment group or application not found".to_string()),
            _ => ControllerError::Service(e.to_string()),
        })?;

    Ok(Json(ApiResponse::success(())))
}
